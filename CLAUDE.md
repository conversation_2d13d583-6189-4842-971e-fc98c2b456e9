# Praja API Development Guide

## Docker Setup
This project runs exclusively in Docker containers. Local setup is not supported.

## Build & Run Commands
- Build and start all containers: `docker compose up --build -d`
- Create external volumes (first time): 
  ```
  docker volume create praja-redis
  docker volume create praja-mysql
  docker volume create praja-opensearch
  docker volume create praja-bundle
  ```
- Create external network (first time): `docker network create praja-network --driver host`
- Access container shell: `docker exec -it praja-api /bin/bash`
- Run bundle commands in container: `docker exec -it praja-api bundle install`
- Run specific rake tasks: `docker exec -it praja-api bundle exec rake [task]`

## Test & Lint Commands
- Run individual tests: `docker exec -it praja-api bash -c "RAILS_ENV=test DEPLOYMENT=test bundle exec rspec path/to/file:line_number"`
- Run all tests: `docker exec -it praja-api bash -c "RAILS_ENV=test DEPLOYMENT=test bundle exec rspec"`
- Run linter: `docker exec -it praja-api bundle exec rubocop`
- Run linter with auto-correct: `docker exec -it praja-api bundle exec rubocop -a`
- Setup test DB: `docker exec -it praja-api bash -c "RAILS_ENV=test rake db:create db:migrate"`

## Code Style Conventions
- Follow RuboCop rules defined in `.rubocop.yml`
- Use snake_case for variables, methods and file names
- Use CamelCase for classes and modules
- Models: include validations, associations, callbacks, then methods
- Use consistent trailing commas in multiline arrays and hashes
- Use meaningful model and method names
- Follow Rails associations best practices (has_many, belongs_to)
- For migrations: first add the column, then deploy, then use the column
- Test coverage is generated using SimpleCov
- Use concerns to extract shared functionality

## Error Handling
- Use proper ActiveRecord validations
- Wrap external API calls in begin/rescue blocks
- Use callbacks to handle model state transitions (AASM)
- Log errors appropriately using Rails logger
- Handle API errors with standardized responses