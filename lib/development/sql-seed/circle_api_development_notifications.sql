INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (1, 'Mani మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-08-29 10:47:45', '2022-08-29 10:47:45', null, 0, 'post',
        1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (2, 'Mani మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-08-29 10:56:41', '2022-08-29 10:56:41', null, 0, 'post',
        1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (3, '? "sarpanch Village" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1, 0, 1, '2022-09-01 02:39:21',
        '2022-09-01 02:39:21', null, 0, 'UserBadge', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (4, 'Fantom User 2 మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-01 08:09:44', '2022-09-01 08:09:44', null, 0,
        'post', 31, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (5, '? "సర్పంచ్ Village" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 24, 0, 1, '2022-09-02 06:59:36',
        '2022-09-02 06:59:36', null, 0, 'UserBadge', 2, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (6, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-05 12:45:14', '2022-09-05 12:45:14', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (7, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:26',
        '2022-09-06 02:24:26', null, 0, 'user', 1070, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (8, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:26',
        '2022-09-06 02:24:26', null, 0, 'user', 1071, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (9, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:26',
        '2022-09-06 02:24:26', null, 0, 'user', 1072, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (10, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:26',
        '2022-09-06 02:24:26', null, 0, 'user', 1073, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (11, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:27',
        '2022-09-06 02:24:27', null, 0, 'user', 1074, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (12, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:27',
        '2022-09-06 02:24:27', null, 0, 'user', 1075, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (13, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:27',
        '2022-09-06 02:24:27', null, 0, 'user', 1076, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (14, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:27',
        '2022-09-06 02:24:27', null, 0, 'user', 1077, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (15, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:27',
        '2022-09-06 02:24:27', null, 0, 'user', 1078, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (16, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:24:28',
        '2022-09-06 02:24:28', null, 0, 'user', 1079, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (17, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:02',
        '2022-09-06 02:26:02', null, 0, 'user', 1080, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (18, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:03',
        '2022-09-06 02:26:03', null, 0, 'user', 1081, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (19, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:03',
        '2022-09-06 02:26:03', null, 0, 'user', 1082, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (20, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:03',
        '2022-09-06 02:26:03', null, 0, 'user', 1083, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (21, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:03',
        '2022-09-06 02:26:03', null, 0, 'user', 1084, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (22, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:04',
        '2022-09-06 02:26:04', null, 0, 'user', 1085, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (23, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:04',
        '2022-09-06 02:26:04', null, 0, 'user', 1086, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (24, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:04',
        '2022-09-06 02:26:04', null, 0, 'user', 1087, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (25, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:04',
        '2022-09-06 02:26:04', null, 0, 'user', 1088, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (26, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:04',
        '2022-09-06 02:26:04', null, 0, 'user', 1089, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (27, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:55',
        '2022-09-06 02:26:55', null, 0, 'user', 1090, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (28, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:56',
        '2022-09-06 02:26:56', null, 0, 'user', 1091, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (29, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:56',
        '2022-09-06 02:26:56', null, 0, 'user', 1092, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (30, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:57',
        '2022-09-06 02:26:57', null, 0, 'user', 1093, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (31, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:26:57',
        '2022-09-06 02:26:57', null, 0, 'user', 1094, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (32, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:46',
        '2022-09-06 02:27:46', null, 0, 'user', 1095, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (33, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:46',
        '2022-09-06 02:27:46', null, 0, 'user', 1096, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (34, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:47',
        '2022-09-06 02:27:47', null, 0, 'user', 1097, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (35, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:47',
        '2022-09-06 02:27:47', null, 0, 'user', 1098, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (36, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:47',
        '2022-09-06 02:27:47', null, 0, 'user', 1099, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (37, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:47',
        '2022-09-06 02:27:47', null, 0, 'user', 1100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (38, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:48',
        '2022-09-06 02:27:48', null, 0, 'user', 1101, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (39, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:27:48',
        '2022-09-06 02:27:48', null, 0, 'user', 1102, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (40, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:28:33',
        '2022-09-06 02:28:33', null, 0, 'user', 1103, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (41, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:28:34',
        '2022-09-06 02:28:34', null, 0, 'user', 1104, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (42, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:28:34',
        '2022-09-06 02:28:34', null, 0, 'user', 1105, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (43, 'మీ పరిచయస్తులు testing ప్రజా అప్ లో జాయిన్ అయ్యారు.', 4, 1, 0, 1, '2022-09-06 02:28:34',
        '2022-09-06 02:28:34', null, 0, 'user', 1106, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (44, 'Fantom User 1 మీ పోస్ట్ పై కామెంట్ చేశారు', 1, 1, 0, 1, '2022-09-06 13:09:11', '2022-09-06 13:09:11', null,
        0, 'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (45, 'Fantom User 1 మీ పోస్ట్ పై కామెంట్ చేశారు', 1, 1, 0, 1, '2022-09-06 13:09:16', '2022-09-06 13:09:16', null,
        0, 'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (46, 'Fantom User 1 మీ పోస్ట్ పై కామెంట్ చేశారు', 1, 1, 0, 1, '2022-09-06 13:11:22', '2022-09-06 13:11:22', null,
        0, 'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (47, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-07 11:23:19', '2022-09-07 11:23:19', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (48, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-07 11:27:37', '2022-09-07 11:27:37', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (49, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-07 11:28:04', '2022-09-07 11:28:04', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (50, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-08 07:42:22', '2022-09-08 07:42:22', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (51, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-08 09:27:45', '2022-12-15 11:52:18', null, 0,
        'post', 60, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (52, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-08 09:28:35', '2022-12-15 11:52:18', null, 0,
        'post', 60, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (53, 'Dheeraj & 2 ఇతరులు మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 836, 0, 1, '2022-09-12 10:17:57',
        '2022-09-12 10:17:57', null, 0, 'post', 84, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (54, 'DDDJJJ & 2 ఇతరులు మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 836, 0, 1, '2022-09-12 10:19:13', '2022-09-12 10:19:13',
        null, 0, 'post', 84, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (55, 'DDDJJJ మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 25, 0, 1, '2022-09-12 10:20:46', '2022-12-15 11:52:18', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (56, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-12 11:39:57', '2022-11-02 06:40:20', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (57, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-12 11:41:10', '2022-11-02 06:40:20', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (58, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-13 06:35:46', '2022-11-02 06:40:20', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (59, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-13 06:51:26', '2022-11-02 06:40:20', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (60, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-13 08:08:08', '2022-11-02 06:40:20', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (61, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-13 08:08:24', '2022-11-02 06:40:20', null, 0,
        'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (62, 'Fantom User 1 మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-15 04:47:55', '2022-09-15 04:47:55', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (63, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-15 10:53:25', '2022-09-15 10:53:25', null, 0,
        'post', 100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (64, 'DDDJJJ మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-15 10:58:43', '2022-09-15 10:58:43', null, 0,
        'post', 100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (65, '? "Top Fan TRS EN" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 827, 0, 1, '2022-09-19 07:56:32',
        '2022-09-19 07:56:32', null, 0, 'UserBadge', 3, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (66, '? "MLA MLA Const." గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1173, 0, 1, '2022-09-20 12:48:47',
        '2022-09-20 12:48:47', null, 0, 'UserBadge', 4, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (67, '? "sarpanch Village 2" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1914, 0, 1, '2022-09-20 12:49:34',
        '2022-09-20 12:49:34', null, 0, 'UserBadge', 5, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (68, 'DDDJJJ మీరు కామెంట్ పెట్టిన పోస్ట్ పై కామెంట్ చేశారు', 1, 24, 0, 1, '2022-09-22 06:41:34',
        '2022-09-22 06:41:34', null, 0, 'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (69, 'dravid మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-23 07:08:54', '2022-09-23 07:08:54', null, 0,
        'post', 100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (70, 'dravid మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-23 07:09:17', '2022-09-23 07:09:17', null, 0,
        'post', 100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (71, 'dravid మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-23 07:16:38', '2022-09-23 07:16:38', null, 0,
        'post', 100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (72, 'dravid మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-09-23 07:17:00', '2022-09-23 07:17:00', null, 0,
        'post', 100, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (73, 'tendulkar మీ పోస్ట్ ని షేర్ చేశారు', 2, 25, 0, 1, '2022-09-29 08:32:53', '2022-11-02 06:40:20', null, 0,
        'post', 203, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (74, 'tendulkar మీ పోస్ట్ ని షేర్ చేశారు', 2, 829, 0, 1, '2022-09-29 08:50:45', '2022-09-29 08:50:45', null, 0,
        'post', 194, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (75, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-09-29 10:25:32', '2022-09-29 10:25:32', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (76, 'dravid & 6 ఇతరులు మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 25, 0, 1, '2022-10-03 07:35:46', '2022-11-02 06:40:20',
        null, 0, 'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (77, 'Gopi & 11 ఇతరులు మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 25, 0, 1, '2022-10-03 07:36:34', '2022-11-02 06:40:20',
        null, 0, 'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (78, 'testing 4 & 16 ఇతరులు మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 25, 0, 1, '2022-10-03 07:37:58',
        '2022-11-02 06:40:20', null, 0, 'post', 91, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (79, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-03 07:48:28', '2022-10-03 07:48:28', null, 0,
        'user', 24, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (80, 'DDDJJJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-03 07:48:36', '2022-10-03 07:48:36', null, 0,
        'user', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (81, 'DDDJJJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-03 10:45:45', '2022-10-03 10:45:45', null, 0,
        'user', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (82, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1,
        '2022-10-03 10:48:02', '2022-10-03 10:48:02', null, 0, 'user', 828, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (83, 'DDDJJJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-03 11:12:41', '2022-10-03 11:12:41', null, 0,
        'user', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (84, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-03 11:17:12', '2022-10-03 11:17:12', null, 0,
        'user', 24, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (85, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-03 11:18:29', '2022-10-03 11:18:29', null, 0,
        'user', 24, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (86, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1, 0, 1, '2022-10-03 11:45:46', '2022-10-03 11:45:46', null, 0,
        'user', 24, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (87, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1, 0, 1, '2022-10-03 11:53:54', '2022-10-03 11:53:54', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (88, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1, 0, 1,
        '2022-10-03 11:53:58', '2022-10-03 11:53:58', null, 0, 'user', 828, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (89, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-04 04:06:59', '2022-10-04 04:06:59', null, 0,
        'user', 24, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (90, 'DDDJJJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-04 04:08:30', '2022-10-04 04:08:30', null, 0,
        'user', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (91, 'DDDJJJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-04 04:47:01', '2022-10-04 04:47:01', null, 0,
        'user', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (92, 'tendulkar మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1, 0, 1, '2022-10-04 04:48:36', '2022-10-04 04:48:36', null, 0,
        'user', 830, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (93, 'testin మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2022-10-04 04:49:25', '2022-10-04 04:49:25', null, 0,
        'user', 1919, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (94, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 13, 828, 0, 1,
        '2022-10-07 05:56:46', '2022-10-07 05:56:46', null, 0, 'UserBadge', 794, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (95, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 13, 827, 0, 1,
        '2022-10-07 05:57:07', '2022-10-07 05:57:07', null, 0, 'UserBadge', 793, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (96, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 827, 0, 1,
        '2022-10-07 06:29:46', '2022-10-07 06:29:46', null, 0, 'UserBadge', 793, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (97, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 827, 0, 1,
        '2022-10-07 06:29:58', '2022-10-07 06:29:58', null, 0, 'UserBadge', 793, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (98, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 827, 0, 1,
        '2022-10-07 06:30:32', '2022-10-07 06:30:32', null, 0, 'UserBadge', 793, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (99, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 827, 0, 1,
        '2022-10-07 06:31:00', '2022-10-07 06:31:00', null, 0, 'UserBadge', 793, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (100, '? "lawyer" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 836, 0, 1, '2022-10-10 10:26:17', '2022-10-10 10:26:17',
        null, 0, 'UserBadge', 796, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (101, '? "lawyer" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 836, 0, 1, '2022-10-10 10:29:17', '2022-10-10 10:29:17',
        null, 0, 'UserBadge', 796, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (102, '? "president District 1" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1914, 0, 1, '2022-10-10 11:01:33',
        '2022-10-10 11:01:33', null, 0, 'UserBadge', 797, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (103, '? "commission chairman State" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1916, 0, 1, '2022-10-10 11:18:02',
        '2022-10-10 11:18:02', null, 0, 'UserBadge', 798, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (104, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-10 18:17:23', '2022-10-10 18:17:23', null, 0, 'post', null, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (107, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:23:16', '2022-10-11 05:23:16', null, 0, 'post', 313, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (108, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:25:03', '2022-10-11 05:25:03', null, 0, 'post', 316, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (109, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:25:17', '2022-10-11 05:25:17', null, 0, 'post', 317, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (110, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:29:10', '2022-10-11 05:29:10', null, 0, 'post', 318, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (111, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:33:05', '2022-10-11 05:33:05', null, 0, 'post', 319, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (112, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:36:24', '2022-10-11 05:36:24', null, 0, 'post', 320, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (113, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:40:55', '2022-10-11 05:40:55', null, 0, 'post', 321, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (114, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 05:45:17', '2022-10-11 05:45:17', null, 0, 'post', 322, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (115, 'కోళ్ల లలిత కుమారి మాజీ శాసనసభ్యులు శృంగవరపుకోట మీ పోస్ట్ ని షేర్ చేశారు', 2, 1916, 0, 1,
        '2022-10-11 06:09:17', '2022-10-11 06:09:17', null, 0, 'post', 323, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (116, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 828, 0, 1, '2022-10-11 07:29:01', '2022-10-11 07:29:01', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (117, 'tendulkar మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 828, 0, 1, '2022-10-11 13:02:06', '2022-10-11 13:02:06', null,
        0, 'user', 830, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (118, 'tendulkar మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1, 0, 1, '2022-10-11 13:02:30', '2022-10-11 13:02:30', null, 0,
        'user', 830, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (119, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 828, 0, 1, '2022-10-11 13:02:45', '2022-10-11 13:02:45', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (120, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1, 0, 1, '2022-10-11 13:02:59', '2022-10-11 13:02:59', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (121, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 1919, 0, 1, '2022-10-11 13:08:14', '2022-10-11 13:08:14', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (122, '? "cm State" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 836, 0, 1, '2022-10-12 06:01:42', '2022-10-12 06:01:42',
        null, 0, 'UserBadge', 800, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (123, '? "Journalist" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1915, 0, 1, '2022-10-13 08:29:57',
        '2022-10-13 08:29:57', null, 0, 'UserBadge', 801, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (124, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 835, 0, 1,
        '2022-10-19 05:52:12', '2022-10-19 05:52:12', null, 0, 'UserBadge', 802, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (125, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-11-03 07:07:50', '2022-11-03 07:07:50', null, 0,
        'post', 441, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (126, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-11-03 07:07:51', '2022-11-03 07:07:51', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (127, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-11-03 07:13:38', '2022-11-03 07:13:38', null, 0,
        'post', 442, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (128, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-11-03 07:13:38', '2022-11-03 07:13:38', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (129, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-11-03 07:20:03', '2022-11-03 07:20:03', null, 0,
        'post', 443, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (130, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-11-03 07:20:04', '2022-11-03 07:20:04', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (131, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2022-11-07 11:11:40', '2022-12-15 11:52:18', null, 0,
        'user', 5, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (132, 'Dheeraj మీ పోస్ట్ పై కామెంట్ చేశారు', 1, 827, 0, 1, '2022-11-08 06:25:57', '2022-11-08 06:25:57', null, 0,
        'post', 394, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (133, 'Dheeraj మీ పోస్ట్ పై కామెంట్ చేశారు', 1, 827, 0, 1, '2022-11-08 06:26:39', '2022-11-08 06:26:39', null, 0,
        'post', 394, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (134, '? "Top Fan" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 2014, 0, 1, '2022-11-14 12:36:54', '2022-11-14 12:36:54',
        null, 0, 'UserBadge', 803, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (135, '? "Top Fan" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 2013, 0, 1, '2022-11-14 12:41:36', '2022-11-14 12:41:36',
        null, 0, 'UserBadge', 804, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (136, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 2013, 0, 1,
        '2022-11-14 12:52:17', '2022-11-14 12:52:17', null, 0, 'UserBadge', 805, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (137, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 2013, 0, 1,
        '2022-11-14 12:54:33', '2022-11-14 12:54:33', null, 0, 'UserBadge', 806, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (138, '? "testing for badge card adklj సర్పంచ్ " గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 2012, 0, 1,
        '2022-11-14 12:55:34', '2022-11-14 12:55:34', null, 0, 'UserBadge', 807, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (139, '? "ZPTC Mandal" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 10:20:01', '2022-11-16 10:20:01',
        null, 0, 'UserBadge', 808, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (140, '? "ZPTC Mandal" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 11:51:57', '2022-11-16 11:51:57',
        null, 0, 'UserBadge', 809, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (141, '? "ZPTC Mandal" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 11:53:48', '2022-11-16 11:53:48',
        null, 0, 'UserBadge', 810, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (142, '? "ZPTC Mandal" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 11:55:46', '2022-11-16 11:55:46',
        null, 0, 'UserBadge', 811, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (143, '? "lawyer" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 11:57:09', '2022-11-16 11:57:09',
        null, 0, 'UserBadge', 812, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (144, '? "lawyer" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 11:58:09', '2022-11-16 11:58:09',
        null, 0, 'UserBadge', 813, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (145, '? "lawyer" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 11:59:21', '2022-11-16 11:59:21',
        null, 0, 'UserBadge', 814, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (146, '? "Social Media" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 12:01:09',
        '2022-11-16 12:01:09', null, 0, 'UserBadge', 815, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (147, '? "Journalist" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 12:28:07', '2022-11-16 12:28:07',
        null, 0, 'UserBadge', 816, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (148, '? "Journalist" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-16 12:29:32', '2022-11-16 12:29:32',
        null, 0, 'UserBadge', 817, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (149, '? "MLA MLA Const." గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-17 07:07:21',
        '2022-11-17 07:07:21', null, 0, 'UserBadge', 818, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (150, '? "lawyer" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-17 07:10:41', '2022-11-17 07:10:41',
        null, 0, 'UserBadge', 819, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (151, '? "Mandal మండల ఉపాధ్యక్షులు" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-17 07:12:35',
        '2022-11-17 07:12:35', null, 0, 'UserBadge', 820, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (152, '? "Mandal మండల ఉపాధ్యక్షులు" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 8, 0, 1, '2022-11-17 07:14:15',
        '2022-11-17 07:14:15', null, 0, 'UserBadge', 821, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (153, 'Dheeraj మీ పోస్ట్ ని ట్రెండ్ చేశారు', 0, 24, 0, 1, '2022-11-18 07:50:15', '2022-11-18 07:50:15', null, 0,
        'post', 481, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (154, '? "test admin role" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 25, 0, 1, '2022-11-20 13:30:27',
        '2022-12-15 11:52:18', null, 0, 'UserBadge', 822, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (155, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-12-14 07:06:24', '2022-12-14 07:06:24', null, 0,
        'post', 1275, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (156, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-12-14 07:06:25', '2022-12-14 07:06:25', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (157, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-12-15 09:22:11', '2022-12-15 09:22:11', null, 0,
        'post', 1277, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (158, 'Dheeraj మీ పోస్ట్ ని షేర్ చేశారు', 2, 1, 0, 1, '2022-12-15 09:22:12', '2022-12-15 09:22:12', null, 0,
        'post', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (159, 'మీ పోస్ట్ పై ట్యాగ్ చేయబడిన Village 1 సర్కిల్ తీసివేయబడింది', 13, 1, 0, 1, '2022-12-15 13:51:49',
        '2022-12-15 13:51:49', null, 0, 'post', 143, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (160, 'మీ పోస్ట్ పై ట్యాగ్ చేయబడిన Village సర్కిల్ తీసివేయబడింది', 13, 834, 0, 1, '2022-12-15 13:54:38',
        '2022-12-15 13:54:38', null, 0, 'post', 1251, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (161, 'మీ పోస్ట్ పై ట్యాగ్ చేయబడిన Village సర్కిల్ తీసివేయబడింది', 13, 834, 0, 1, '2022-12-15 13:55:19',
        '2022-12-15 13:55:19', null, 0, 'post', 1251, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (162, 'మీ పోస్ట్ పై మీరు ట్యాగ్ చేసిన దేవినేని ఉమామహేశ్వరరావు సర్కిల్ తీసివేయబడింది', 13, 25, 0, 1,
        '2022-12-22 11:23:01', '2022-12-22 11:23:16', null, 0, 'post', 440, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (163, 'మీ పోస్ట్ పై మీరు ట్యాగ్ చేసిన దేవినేని ఉమామహేశ్వరరావు సర్కిల్ తీసివేయబడింది', 13, 25, 0, 1,
        '2022-12-22 11:26:46', '2022-12-22 11:26:59', null, 0, 'post', 429, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (164, 'Dheeraj మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 830, 0, 1, '2023-01-02 10:52:47', '2023-01-02 10:52:47', null, 0,
        'user', 25, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (165, 'DDJJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-03 12:58:54', '2023-01-06 09:20:49', null, 0,
        'user', 1, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (166, 'D                   J మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-03 13:08:37',
        '2023-01-06 09:20:49', null, 0, 'user', 25, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (167, 'Dheeraj 1 మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-03 13:09:11', '2023-01-06 09:20:49', null, 0,
        'user', 24, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (168, 'Dheeraj Avvari మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-06 07:40:51', '2023-01-06 09:20:49',
        null, 0, 'user', 5, 1);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (169, 'tendulkar మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-09 18:01:28', '2023-01-09 18:01:28', null, 0,
        'user', 830, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (170, 'DJ మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-09 18:04:49', '2023-01-09 18:04:49', null, 0,
        'user', 1, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (171, '? "sarpanch" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 1, 0, 1, '2023-01-23 04:05:03', '2023-01-23 04:05:03',
        null, 0, 'UserBadge', 875, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (172, 'Dheeraj Avvari మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-23 11:08:21', '2023-01-23 11:08:21',
        null, 0, 'user', 5, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (173, 'Dheeraj Avvari మిమ్మల్ని ఫాలో అవుతున్నారు', 4, 25, 0, 1, '2023-01-23 11:09:50', '2023-01-23 11:09:50',
        null, 0, 'user', 5, 0);
INSERT IGNORE INTO circle_api_development.notifications (id, description, notification_type, user_id, delivered, active,
                                                         created_at, updated_at, deep_link, `read`, entity_type,
                                                         entity_id, received)
VALUES (174, '? "cm State" గా మీకు ప్రత్యేక గుర్తింపు !!', 12, 828, 0, 1, '2023-01-25 08:12:38', '2023-01-25 08:12:38',
        null, 0, 'UserBadge', 1066, 0);