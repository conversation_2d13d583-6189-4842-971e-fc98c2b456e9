INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (54, 'Poster 1', '2022-09-27 19:04:29', '2022-09-28 19:04:32', 1, 392, 1, '2022-09-27 19:06:02.429082',
        '2022-09-27 19:06:02.429082', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (55, 'Poster 2', '2022-09-27 19:06:43', '2022-09-28 19:06:46', 0, 866, 1, '2022-09-27 19:07:08.133830',
        '2022-09-28 03:29:11.775879', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (56, 'Poster 3', '2022-09-28 03:41:22', '2022-09-29 03:41:24', 0, 54, 1, '2022-09-28 03:43:17.831852',
        '2022-09-28 03:45:49.158346', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (57, 'Poster 4', '2022-09-28 03:45:57', '2022-09-29 03:46:00', 0, 54, 1, '2022-09-28 03:46:44.115639',
        '2022-09-28 07:53:23.377032', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (58, 'poster 5', '2022-09-28 06:55:30', '2022-09-29 06:55:33', 0, 54, 1, '2022-09-28 07:01:42.206846',
        '2022-09-28 07:53:15.245633', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (59, 'Poster 6', '2022-09-28 07:03:00', '2022-09-29 07:03:02', 0, 54, 1, '2022-09-28 07:03:22.459103',
        '2022-09-28 07:53:08.757287', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (60, 'Poster 7', '2022-09-28 07:04:18', '2022-09-29 07:04:21', 0, 54, 1, '2022-09-28 07:05:08.174773',
        '2022-09-28 07:53:00.963240', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (61, 'Poster 8', '2022-09-28 07:05:46', '2022-09-29 07:05:49', 1, 54, 1, '2022-09-28 07:06:07.346950',
        '2022-09-28 09:57:58.199583', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (62, 'Poster 9', '2022-09-28 07:11:03', '2022-09-28 07:11:06', 1, 54, 1, '2022-09-28 07:20:51.715583',
        '2022-09-28 09:56:47.591660', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (63, 'Poster 6', '2022-09-28 07:53:34', '2022-09-29 07:53:37', 1, 866, 1, '2022-09-28 07:54:05.769492',
        '2022-09-28 07:54:05.769492', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (64, 'Neutral', '2022-09-28 10:00:02', '2022-09-29 10:00:06', 1, 0, 1, '2022-09-28 10:00:26.454079',
        '2022-09-28 10:00:26.454079', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (65, 'Poster 7', '2022-09-29 13:45:20', '2022-09-30 13:45:22', 1, 866, 1, '2022-09-29 13:45:41.098916',
        '2022-09-29 13:45:41.098916', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (66, 'Poster 8', '2022-09-29 14:21:00', '2022-09-30 14:21:03', 1, 54, 1, '2022-09-29 14:21:22.483058',
        '2022-09-29 14:21:22.483058', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (67, 'Poster 9', '2022-09-29 14:32:32', '2022-09-30 14:32:34', 1, 0, 1, '2022-09-29 14:32:54.066642',
        '2022-09-29 14:32:54.066642', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (68, 'Poster 866', '2022-10-04 05:01:09', '2022-10-05 05:01:11', 1, 866, 1, '2022-10-04 05:03:03.869822',
        '2022-10-04 05:03:03.869822', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (69, 'Aspect ratio posters', '2022-10-04 11:42:38', '2022-10-05 11:42:40', 1, 54, 1,
        '2022-10-04 11:44:29.936394', '2022-10-04 11:44:29.936394', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (70, 'Aspect ratio posters 2', '2022-10-04 12:42:24', '2022-10-05 12:42:27', 1, 54, 1,
        '2022-10-04 12:43:39.158926', '2022-10-04 12:43:39.158926', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (71, 'Aspect check', '2022-10-04 12:44:10', '2022-10-05 12:44:12', 1, 54, 1, '2022-10-04 12:45:32.877780',
        '2022-10-04 12:45:32.877780', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (72, 'working poster', '2022-10-06 12:37:20', '2022-10-07 12:37:25', 1, 54, 1, '2022-10-06 12:38:45.818553',
        '2022-10-06 12:38:45.818553', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (73, 'oh', '2022-10-06 15:54:01', '2022-10-07 15:54:05', 1, 866, 1, '2022-10-06 15:54:22.983920',
        '2022-10-06 15:54:22.983920', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (74, 'ok', '2022-10-06 16:33:22', '2022-10-07 16:33:25', 1, 54, 1, '2022-10-06 16:33:39.884438',
        '2022-10-06 16:33:39.884438', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (75, 'test3', '2022-10-11 11:54:49', '2022-10-11 17:55:04', 1, 0, 1, '2022-10-11 11:56:10.776852',
        '2022-10-11 11:56:10.776852', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (76, 'political party test 1', '2022-10-11 12:10:06', '2022-10-11 18:10:09', 1, 54, 1,
        '2022-10-11 12:10:33.610741', '2022-10-11 12:10:33.610741', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (77, 'Banner nil check', '2022-10-13 07:57:54', '2022-10-14 07:57:56', 1, 54, 1, '2022-10-13 07:58:17.967835',
        '2022-10-13 07:58:17.967835', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (78, 'test for circle new', '2022-11-16 07:25:52', '2022-11-16 13:25:58', 0, 54, 1, '2022-11-16 07:28:10.872182',
        '2022-11-16 12:52:14.934785', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (79, 'Party poster', '2022-11-16 12:52:37', '2022-11-17 12:52:39', 0, 54, 1, '2022-11-16 12:53:14.984615',
        '2022-11-17 07:00:05.744580', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (80, 'Poster for Public', '2022-11-16 12:53:31', '2022-11-17 12:53:33', 1, 0, 1, '2022-11-16 12:53:48.062054',
        '2022-11-16 12:53:48.062054', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (81, 'Poster for village', '2022-11-16 12:54:04', '2022-11-17 12:54:06', 1, 5, 1, '2022-11-16 12:54:22.359063',
        '2022-11-16 12:54:22.359063', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (82, 'Poster for Mandal', '2022-11-16 12:54:36', '2022-11-17 12:54:38', 1, 4, 1, '2022-11-16 12:54:52.847513',
        '2022-11-16 12:54:52.847513', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (83, 'Poster 867', '2022-11-17 06:57:19', '2022-11-18 06:57:22', 1, 867, 1, '2022-11-17 06:57:40.732754',
        '2022-11-17 06:57:40.732754', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (84, 'party poster with 2 photos', '2022-11-17 07:04:17', '2022-11-17 13:10:26', 1, 54, 1,
        '2022-11-17 07:05:07.475022', '2022-11-17 07:05:07.475022', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (85, 'Poster for 54', '2022-11-18 06:37:05', '2022-11-19 06:37:08', 1, 54, 1, '2022-11-18 06:37:24.523207',
        '2022-11-18 06:37:24.523207', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (86, 'Poster for 392', '2022-11-18 06:38:06', '2022-11-19 06:38:09', 1, 392, 1, '2022-11-18 06:38:27.656484',
        '2022-11-18 06:38:27.656484', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (87, 'Poster 5', '2022-11-18 06:38:40', '2022-11-19 06:38:42', 1, 5, 1, '2022-11-18 06:39:01.767386',
        '2022-11-18 06:39:01.767386', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (88, 'Poster 3', '2022-11-18 06:39:11', '2022-11-18 06:39:13', 1, 3, 1, '2022-11-18 06:39:35.041578',
        '2022-11-18 06:39:35.041578', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (89, 'Poster 10', '2022-11-18 06:39:44', '2022-11-19 06:39:47', 1, 10, 1, '2022-11-18 06:40:05.090857',
        '2022-11-18 06:40:05.090857', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (90, 'state level poster', '2022-11-18 12:51:23', '2022-11-19 12:51:26', 1, 2, 1, '2022-11-18 12:51:45.615102',
        '2022-11-18 12:51:45.615102', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (91, '54', '2022-11-20 06:51:09', '2022-11-22 06:51:11', 1, 54, 1, '2022-11-20 06:51:32.057216',
        '2022-11-20 07:18:43.122249', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (92, '392', '2022-11-20 06:52:43', '2022-11-22 06:52:47', 1, 392, 1, '2022-11-20 06:53:05.232808',
        '2022-11-20 06:53:05.232808', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (93, '5', '2022-11-20 06:53:17', '2022-11-22 06:53:20', 1, 5, 1, '2022-11-20 06:53:37.410236',
        '2022-11-20 06:53:37.410236', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (94, '3', '2022-11-20 06:54:06', '2022-11-22 06:54:09', 1, 3, 1, '2022-11-20 06:54:25.536599',
        '2022-11-20 06:54:25.536599', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (95, '2', '2022-11-20 06:54:46', '2022-11-22 06:54:50', 1, 2, 1, '2022-11-20 06:55:22.452555',
        '2022-11-20 06:55:22.452555', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (96, '10', '2022-11-20 06:55:37', '2022-11-22 06:55:40', 1, 10, 1, '2022-11-20 06:56:11.912454',
        '2022-11-20 06:56:11.912454', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (97, 'poster ycp', '2022-11-22 09:56:24', '2022-11-23 15:56:27', 1, 54, 1, '2022-11-22 09:56:48.654781',
        '2022-11-22 09:56:48.654781', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (98, 'poster ysrcp test', '2022-11-24 10:29:20', '2022-11-30 10:29:24', 0, 54, 1, '2022-11-24 10:30:32.497602',
        '2022-11-25 09:53:02.814413', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (99, 'Poster 54', '2022-11-24 17:38:03', '2022-11-26 17:38:06', 0, 54, 1, '2022-11-24 17:38:24.606775',
        '2022-11-25 09:52:55.431309', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (100, 'Poster 2', '2022-11-24 17:38:37', '2022-11-26 17:38:39', 0, 2, 1, '2022-11-24 17:38:55.316726',
        '2022-11-25 09:52:45.344065', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (299, 'Poster', '2022-11-28 09:35:07', '2022-11-28 09:50:07', 1, 2388, 337, '2022-11-28 09:35:07.998608',
        '2022-11-28 09:35:07.998608', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (300, 'Poster', '2022-11-28 09:35:08', '2022-11-28 09:50:08', 1, 2389, null, '2022-11-28 09:35:08.317758',
        '2022-11-28 09:35:08.317758', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (301, 'Poster', '2022-11-28 09:36:57', '2022-11-28 09:51:57', 1, 2393, null, '2022-11-28 09:36:57.526584',
        '2022-11-28 09:36:57.526584', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (302, 'Poster', '2022-11-28 09:36:57', '2022-11-28 09:51:57', 1, 2394, null, '2022-11-28 09:36:57.948048',
        '2022-11-28 09:36:57.948048', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (303, 'Poster', '2022-11-28 09:38:28', '2022-11-28 09:53:28', 1, 2398, null, '2022-11-28 09:38:28.165142',
        '2022-11-28 09:38:28.165142', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (304, 'Poster', '2022-11-28 09:38:28', '2022-11-28 09:53:28', 1, 2399, null, '2022-11-28 09:38:28.433632',
        '2022-11-28 09:38:28.433632', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (317, ' yscrp temp', '2022-11-28 13:50:25', '2022-11-30 13:50:29', 1, 54, 1, '2022-11-28 13:50:48.393557',
        '2022-11-28 13:50:48.393557', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (318, 'state poster', '2022-11-29 05:28:15', '2022-11-30 05:28:18', 0, 2, 1, '2022-11-29 05:28:49.868382',
        '2022-11-29 10:54:53.481635', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (319, 'village poster', '2022-11-29 05:56:55', '2022-11-30 05:56:59', 0, 4, 1, '2022-11-29 05:57:18.392248',
        '2022-11-29 10:55:02.027476', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (320, 'mandal poster', '2022-11-29 05:58:20', '2022-11-30 05:58:22', 0, 5, 1, '2022-11-29 05:58:41.295362',
        '2022-11-29 10:55:08.368639', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (327, 'ysrcp ', '2022-12-01 06:00:04', '2022-12-02 06:00:07', 0, 54, 1, '2022-12-01 06:00:38.831095',
        '2022-12-15 11:11:41.463899', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (328, 'tdp testing', '2022-12-09 07:16:54', '2022-12-31 07:16:56', 0, 392, 1, '2022-12-09 07:17:16.420114',
        '2022-12-15 11:11:32.256047', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (329, 'ycp poster', '2022-12-15 10:11:13', '2022-12-16 10:11:15', 0, 54, 1, '2022-12-15 10:11:34.282606',
        '2022-12-15 11:11:23.194026', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (330, 'trs poster', '2022-12-15 10:12:04', '2022-12-16 10:12:07', 0, 6, 1, '2022-12-15 10:12:21.195757',
        '2022-12-15 11:11:12.014696', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (331, 'tdp poster', '2022-12-15 10:12:32', '2022-12-16 10:12:35', 0, 392, 1, '2022-12-15 10:12:52.440379',
        '2022-12-15 11:11:02.511186', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (332, 'village poster', '2022-12-15 10:13:13', '2022-12-16 10:13:15', 0, 5, 1, '2022-12-15 10:13:30.241473',
        '2022-12-15 11:10:54.925054', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (339, 'state level poster', '2022-12-22 06:51:47', '2022-12-31 06:51:50', 1, 2, 1, '2022-12-22 06:52:36.818134',
        '2022-12-30 14:44:06.322172', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (340, 'hi', '2022-12-27 04:12:10', '2022-12-28 04:12:13', 0, 54, 1, '2022-12-27 04:13:47.749159',
        '2022-12-30 13:22:45.741373', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (341, 'state poster 2', '2022-12-30 10:13:10', '2023-01-07 10:13:15', 0, 2, 1, '2022-12-30 10:13:43.089140',
        '2023-01-04 07:09:29.017303', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (342, 'district level', '2022-12-30 12:56:43', '2023-01-07 12:56:47', 0, 3, 1, '2022-12-30 12:57:18.126178',
        '2022-12-31 14:09:35.013121', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (343, 'mp constituency ', '2022-12-30 13:23:03', '2023-01-07 13:23:09', 0, 9, 1, '2022-12-30 13:23:56.447485',
        '2022-12-30 13:27:46.495525', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (344, 'mla constituency', '2022-12-30 13:24:14', '2023-01-07 13:24:16', 0, 10, 1, '2022-12-30 13:24:41.973017',
        '2022-12-31 14:08:48.320624', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (345, 'mandal poster', '2022-12-30 13:24:59', '2023-01-07 13:25:02', 0, 4, 1, '2022-12-30 13:25:22.278708',
        '2022-12-31 14:08:03.733068', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (346, 'village poster', '2022-12-30 13:25:38', '2023-01-07 13:25:43', 0, 5, 1, '2022-12-30 13:26:03.123774',
        '2023-01-03 11:03:39.738469', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (347, 'ysrcp poster', '2022-12-30 16:28:52', '2023-01-07 16:28:55', 0, 54, 1, '2022-12-30 16:29:12.853456',
        '2023-01-03 11:04:21.422578', null);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (353, 'trs normal poster', '2023-01-02 03:32:49', '2023-01-31 03:32:53', 0, 6, 1, '2023-01-03 03:33:26.997009',
        '2023-01-04 07:08:39.950057', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (354, 'trs framed poster', '2023-01-03 03:33:41', '2023-01-31 03:33:44', 0, 6, 1, '2023-01-03 03:34:08.973441',
        '2023-01-04 07:08:32.947864', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (355, 'trs normal poster 2', '2023-01-03 03:55:36', '2023-01-31 03:55:40', 0, 6, 1, '2023-01-03 03:56:01.684759',
        '2023-01-04 07:08:17.157030', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (356, 'tdp poster normal', '2023-01-03 11:06:56', '2023-01-31 11:06:59', 0, 392, 1, '2023-01-03 11:07:19.546703',
        '2023-01-04 07:08:09.404246', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (357, 'tdp poster frame', '2023-01-03 11:07:33', '2023-01-31 11:07:35', 0, 392, 1, '2023-01-03 11:07:57.384490',
        '2023-01-04 07:08:03.852054', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (359, 'tdp normal', '2023-01-04 07:10:35', '2023-01-05 07:10:38', 0, 392, 1, '2023-01-04 07:11:37.044141',
        '2023-01-05 04:30:01.568276', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (360, 'tdp frame', '2023-01-04 07:11:53', '2023-01-05 07:12:23', 0, 392, 1, '2023-01-04 07:12:44.073498',
        '2023-01-05 04:29:53.289032', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (361, 'trs frame', '2023-01-04 07:15:22', '2023-01-05 07:15:24', 0, 6, 1, '2023-01-04 07:15:45.107980',
        '2023-01-05 04:29:44.724289', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (362, 'trs normal', '2023-01-04 07:15:55', '2023-01-05 07:15:58', 0, 6, 1, '2023-01-04 07:16:22.094661',
        '2023-01-05 04:29:22.720467', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (451, 'testing error', '2023-01-04 17:51:00', '2023-01-05 17:51:02', 0, 392, 1, '2023-01-04 18:08:06.265107',
        '2023-01-05 04:29:13.868150', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (465, 'tdp normal', '2023-01-05 04:35:10', '2023-01-06 04:35:17', 1, 392, 1, '2023-01-05 04:36:52.020671',
        '2023-01-05 07:02:57.005920', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (466, 'tdp frame', '2023-01-05 04:41:12', '2023-01-06 04:41:15', 0, 392, 1, '2023-01-05 04:42:06.594867',
        '2023-01-05 04:47:22.853515', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (467, 'tdp frame', '2023-01-05 04:47:32', '2023-01-06 04:47:34', 0, 392, 1, '2023-01-05 04:47:57.077758',
        '2023-01-05 04:51:09.825763', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (468, 'tdp frame', '2023-01-05 04:51:28', '2023-01-06 04:51:31', 1, 392, 1, '2023-01-05 04:51:54.440144',
        '2023-01-05 04:51:54.440144', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (469, 'village poster', '2023-01-05 04:59:00', '2023-01-06 04:59:03', 1, 5, 1, '2023-01-05 04:59:20.406490',
        '2023-01-05 04:59:20.406490', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (470, 'village poster 1', '2023-01-09 16:32:36', '2023-01-11 16:32:39', 1, 5, 1, '2023-01-09 16:32:58.546890',
        '2023-01-09 16:32:58.546890', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (471, 'tdp frame 1', '2023-01-10 04:11:31', '2023-01-11 04:11:34', 1, 54, 1, '2023-01-10 04:12:07.044658',
        '2023-01-10 04:12:07.044658', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (472, 'yscrcp normal 1', '2023-01-10 04:18:58', '2023-01-11 04:19:00', 1, 54, 1, '2023-01-10 04:19:32.233390',
        '2023-01-10 04:19:32.233390', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (473, 'bhogi testing', '2023-01-13 17:40:44', '2023-01-14 17:40:47', 0, 392, 1, '2023-01-13 17:41:37.296655',
        '2023-01-13 17:54:34.278113', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (474, 'bhogi testing state', '2023-01-13 17:45:43', '2023-01-14 17:45:46', 1, 2, 1, '2023-01-13 17:46:31.051934',
        '2023-01-13 18:16:44.200570', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (475, 'frame poster test', '2023-01-13 17:53:09', '2023-01-14 17:53:12', 1, 2, 1, '2023-01-13 17:53:42.321538',
        '2023-01-13 18:16:59.741242', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (477, 'testing frame poster type photo for png', '2023-01-16 05:52:25', '2023-01-17 05:52:27', 1, 5, 1,
        '2023-01-16 05:56:05.443922', '2023-01-16 05:56:05.443922', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (478, 'testing', '2023-01-16 05:56:27', '2023-01-17 05:56:30', 1, 5, 1, '2023-01-16 05:56:48.643591',
        '2023-01-16 05:56:48.643591', 0);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (505, 'testing framed poster', '2023-01-24 07:42:32', '2023-01-25 07:42:35', 1, 5, 1,
        '2023-01-24 07:43:01.693651', '2023-01-24 07:43:01.693651', 1);
INSERT IGNORE INTO circle_api_development.posters (id, name, start_time, end_time, active, circle_id, admin_user_id,
                                                   created_at, updated_at, poster_type)
VALUES (506, 'party poster', '2023-01-24 13:08:17', '2023-01-25 13:08:20', 1, 54, 1, '2023-01-24 13:09:30.578684',
        '2023-01-24 13:09:30.578684', 0);