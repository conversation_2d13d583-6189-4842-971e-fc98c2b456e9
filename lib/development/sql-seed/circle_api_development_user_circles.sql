INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (6, 25, 7, 1, '2022-08-30 15:20:34', '2022-08-30 15:20:34', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (12, 827, 4, 1, '2022-09-05 02:35:47', '2022-09-05 02:35:47', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (13, 831, 4, 1, '2022-09-05 02:43:46', '2022-09-05 02:43:46', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (14, 832, 5, 1, '2022-09-05 02:47:34', '2022-09-05 02:47:34', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (16, 834, 5, 1, '2022-09-05 03:41:44', '2022-09-05 03:41:44', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (20, 24, 54, 1, '2022-09-07 05:59:25', '2022-09-07 05:59:25', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (31, 24, 6, 1, '2022-09-19 04:49:24', '2022-09-19 04:49:24', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (33, 827, 6, 1, '2022-09-19 07:56:32', '2022-09-19 07:56:32', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (34, 24, 866, 1, '2022-09-19 08:05:12', '2022-09-19 08:05:12', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (35, 1, 866, 1, '2022-09-19 08:06:35', '2022-09-19 08:06:35', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (37, 25, 866, 1, '2022-09-19 08:08:50', '2022-09-19 08:08:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (38, 827, 866, 1, '2022-09-19 08:09:42', '2022-09-19 08:09:42', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (39, 828, 866, 1, '2022-09-19 08:10:34', '2022-09-19 08:10:34', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (40, 829, 866, 1, '2022-09-19 08:11:08', '2022-09-19 08:11:08', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (42, 1918, 865, 1, '2022-09-20 12:36:01', '2022-09-20 12:36:01', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (43, 1917, 865, 1, '2022-09-20 12:39:04', '2022-09-20 12:39:04', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (44, 1916, 865, 1, '2022-09-20 12:39:30', '2022-09-20 12:39:30', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (45, 1915, 865, 1, '2022-09-20 12:39:49', '2022-09-20 12:39:49', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (46, 1914, 865, 1, '2022-09-20 12:40:14', '2022-09-20 12:40:14', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (47, 1173, 865, 1, '2022-09-20 12:40:34', '2022-09-20 12:40:34', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (49, 829, 865, 1, '2022-09-23 11:35:36', '2022-09-23 11:35:36', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (50, 829, 54, 1, '2022-09-23 11:42:30', '2022-09-23 11:42:30', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (54, 833, 5, 1, '2022-09-27 17:33:53', '2022-09-27 17:33:53', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (58, 828, 54, 1, '2022-09-28 10:41:45', '2022-09-28 10:41:45', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (62, 1919, 5, 1, '2022-10-03 16:12:24', '2022-10-03 16:12:24', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (64, 835, 6, 1, '2022-10-19 05:52:11', '2022-10-19 05:52:11', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (102, 831, 392, 1, '2022-10-25 11:44:02', '2022-10-25 11:44:02', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (106, 2008, 865, 1, '2022-11-02 05:33:22', '2022-11-02 05:33:22', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (107, 2009, 939, 1, '2022-11-02 06:44:17', '2022-11-02 06:44:17', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (108, 2010, 939, 1, '2022-11-02 06:53:01', '2022-11-02 06:53:01', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (109, 2011, 953, 1, '2022-11-02 07:12:30', '2022-11-10 03:55:15', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (110, 2012, 939, 1, '2022-11-02 07:18:18', '2022-11-02 07:18:18', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (115, 2014, 939, 1, '2022-11-03 05:39:21', '2022-11-03 05:39:21', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (120, 25, 953, 1, '2022-11-10 03:44:37', '2022-11-10 03:44:37', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (123, 5, 953, 1, '2022-11-10 03:44:56', '2022-11-10 03:44:56', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (128, 2013, 953, 1, '2022-11-10 03:50:19', '2022-11-10 04:06:03', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (130, 834, 953, 1, '2022-11-10 03:50:39', '2022-11-10 04:06:04', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (131, 835, 953, 1, '2022-11-10 03:50:43', '2022-11-10 04:06:04', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (133, 836, 953, 1, '2022-11-10 03:51:05', '2022-11-10 03:51:05', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (134, 2012, 953, 1, '2022-11-10 03:51:14', '2022-11-10 03:51:14', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (140, 2014, 54, 1, '2022-11-14 12:36:54', '2022-11-14 12:36:54', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (141, 2013, 54, 1, '2022-11-14 12:41:36', '2022-11-14 12:41:36', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (143, 2013, 939, 1, '2022-11-14 12:52:55', '2022-11-14 12:52:55', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (145, 2012, 54, 1, '2022-11-14 12:55:34', '2022-11-14 12:55:34', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (146, 2018, 5, 1, '2022-11-15 11:57:14', '2022-11-15 11:57:14', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (149, 8, 54, 1, '2022-11-16 12:01:08', '2022-11-16 12:01:08', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (152, 25, 54, 1, '2022-11-20 13:30:26', '2022-11-20 13:30:26', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (312, 25, 867, 1, '2022-11-25 08:59:48', '2022-11-25 08:59:48', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (469, 2011, 939, 1, '2022-11-29 07:25:29', '2022-11-29 07:25:29', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (472, 836, 5, 1, '2022-11-29 08:55:47', '2022-11-29 08:55:47', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (483, 2019, 5, 1, '2022-11-30 06:38:52', '2022-11-30 06:38:52', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (485, 2019, 866, 1, '2022-12-01 06:37:48', '2022-12-01 06:37:48', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (573, 834, 54, 1, '2022-12-06 06:08:17', '2022-12-06 06:08:17', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (598, 1, 6, 1, '2022-12-08 11:52:49', '2022-12-08 11:52:49', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (600, 1, 392, 1, '2022-12-08 11:52:49', '2022-12-08 11:52:49', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (601, 1, 909, 1, '2022-12-08 11:52:50', '2022-12-08 11:52:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (602, 1, 910, 1, '2022-12-08 11:52:50', '2022-12-08 11:52:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (603, 1, 1478, 1, '2022-12-08 11:52:50', '2022-12-08 11:52:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (604, 1, 1479, 1, '2022-12-08 11:52:50', '2022-12-08 11:52:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (605, 1, 2389, 1, '2022-12-08 11:52:50', '2022-12-08 11:52:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (606, 1, 2394, 1, '2022-12-08 11:52:50', '2022-12-08 11:52:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (607, 1, 2399, 1, '2022-12-08 11:52:51', '2022-12-08 11:52:51', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (608, 1, 7, 1, '2022-12-08 11:52:51', '2022-12-08 11:52:51', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (609, 1, 8, 1, '2022-12-08 11:52:51', '2022-12-08 11:52:51', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (611, 1, 867, 1, '2022-12-08 11:53:52', '2022-12-08 11:53:52', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (613, 1, 916, 1, '2022-12-08 11:53:53', '2022-12-08 11:53:53', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (614, 1, 953, 1, '2022-12-08 11:53:53', '2022-12-08 11:53:53', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (629, 835, 5, 1, '2022-12-22 08:52:03', '2022-12-22 08:52:03', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (644, 5, 5, 1, '2022-12-23 09:26:48', '2022-12-23 09:26:48', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (645, 24, 5, 1, '2022-12-23 09:36:30', '2022-12-23 09:36:30', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (647, 1, 5, 1, '2022-12-23 09:42:18', '2022-12-23 09:42:18', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (648, 830, 865, 1, '2022-12-23 09:44:38', '2022-12-23 09:44:38', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (653, 25, 5, 1, '2022-12-29 15:23:10', '2022-12-29 15:23:10', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (654, 25, 6, 1, '2023-01-03 11:05:01', '2023-01-03 11:05:01', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (655, 2012, 911, 1, '2023-01-03 12:51:53', '2023-01-03 12:51:53', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (658, 2011, 911, 1, '2023-01-03 12:57:12', '2023-01-03 12:57:12', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (659, 2013, 911, 1, '2023-01-03 12:57:13', '2023-01-03 12:57:13', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (660, 2014, 911, 1, '2023-01-03 12:57:13', '2023-01-03 12:57:13', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (661, 2018, 911, 1, '2023-01-03 12:57:13', '2023-01-03 12:57:13', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (666, 24, 911, 1, '2023-01-03 13:09:10', '2023-01-03 13:09:10', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (667, 24, 867, 1, '2023-01-04 06:28:44', '2023-01-04 06:28:44', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (668, 830, 866, 1, '2023-01-04 06:29:53', '2023-01-04 06:29:53', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (677, 25, 911, 1, '2023-01-06 09:16:18', '2023-01-06 09:16:18', 0);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (678, 11690, 5, 1, '2023-01-09 09:03:43', '2023-01-09 09:03:43', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (679, 11691, 5, 1, '2023-01-09 09:16:49', '2023-01-09 09:16:49', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (680, 11692, 5, 1, '2023-01-09 09:51:41', '2023-01-09 09:51:41', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (681, 11693, 5, 1, '2023-01-09 10:00:28', '2023-01-09 10:00:28', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (682, 11694, 5, 1, '2023-01-09 10:03:26', '2023-01-09 10:03:26', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (683, 11695, 5, 1, '2023-01-09 10:10:15', '2023-01-09 10:10:15', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (684, 11696, 5, 1, '2023-01-09 12:28:59', '2023-01-09 12:28:59', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (685, 11698, 5, 1, '2023-01-09 12:30:18', '2023-01-09 12:30:18', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (686, 11699, 5, 1, '2023-01-09 12:31:20', '2023-01-09 12:31:20', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (687, 11700, 5, 1, '2023-01-09 12:43:56', '2023-01-09 12:43:56', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (688, 11701, 5, 1, '2023-01-09 12:44:39', '2023-01-09 12:44:39', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (689, 11702, 5, 1, '2023-01-12 10:57:52', '2023-01-12 10:57:52', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (690, 11703, 5, 1, '2023-01-12 10:58:54', '2023-01-12 10:58:54', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (691, 11704, 5, 1, '2023-01-12 10:59:42', '2023-01-12 10:59:42', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (693, 3994, 945, 1, '2023-01-13 05:34:50', '2023-01-13 05:34:50', null);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (694, 5, 392, 1, '2023-01-20 10:10:12', '2023-01-20 10:10:12', 0);
INSERT IGNORE INTO circle_api_development.user_circles (id, user_id, circle_id, active, created_at, updated_at,
                                                        source_of_join)
VALUES (1157, 828, 5, 1, '2023-01-25 08:07:49', '2023-01-25 08:07:49', null);