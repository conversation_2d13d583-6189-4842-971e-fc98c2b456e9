INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1, 'there is no hashtag in this post but creating one hashtag from received hashta', null, 1, null, 1,
        '2022-08-29 10:39:17', '2022-08-29 10:39:17', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (2, 'venkat deleted data', null, 1, null, 1, '2022-08-29 10:39:34', '2022-08-29 10:39:34', null, 0, null, 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (3, 'bhargavi accepted venkat''s apologies', null, 1, null, 1, '2022-08-29 10:41:22', '2022-08-29 10:41:22',
        null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (5, 'opinion is given now', null, 1, 1, 1, '2022-08-29 10:47:45', '2022-08-29 10:47:45', null, 0, null, 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (10, '            "', null, 1, 1, 1, '2022-08-29 10:56:41', '2022-08-29 10:56:41', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (28, 'YSRCP Post to get in circles feed', null, 1, null, 1, '2022-08-30 06:30:53', '2022-08-30 06:30:53', null,
        0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (29, 'Posting in Leader circle with id 7', null, 25, null, 1, '2022-08-30 15:21:20', '2022-08-30 15:21:20', null,
        0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (30, 'A post by user 1 in ysrcp circle with id 54', null, 1, null, 1, '2022-08-30 15:22:31',
        '2022-08-30 15:22:31', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (31, 'opinion in ysrcp circle 54', null, 25, 30, 1, '2022-09-01 08:09:43', '2022-09-01 08:09:43', null, 0, null,
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (32, '#cmjagan', null, 25, null, 1, '2022-09-01 12:16:52', '2022-09-01 12:16:52', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (33, '#pawankalyanbirthday', null, 25, null, 1, '2022-09-01 12:17:06', '2022-09-01 12:17:06', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (34, 'tagging village and political party to check index', null, 25, null, 1, '2022-09-05 03:42:08',
        '2022-09-05 03:42:08', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (35, null, null, 25, null, 1, '2022-09-05 07:12:06', '2022-09-05 07:12:06', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (48, 'Opinion in new post format without circle public', null, 25, 1, 1, '2022-09-05 12:45:14',
        '2022-09-05 12:45:14', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (53, 'Create new post', null, 25, null, 1, '2022-09-06 09:58:57', '2022-09-06 09:58:57', null, 0, null, 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (54, 'Post from active admin ', null, 24, null, 1, '2022-09-06 10:17:42', '2022-09-06 10:17:42', null, 0, null,
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (55, 'Post from admin as Public post checked', null, 24, null, 1, '2022-09-06 10:31:48', '2022-09-06 10:31:48',
        null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (56, 'opinion in public', null, 25, 1, 1, '2022-09-07 11:23:19', '2022-09-07 11:23:19', null, 0, null, 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (57, 'opinion error', null, 25, 1, 1, '2022-09-07 11:27:36', '2022-09-07 11:27:36', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (58, 'opinion error fix', null, 25, 1, 1, '2022-09-07 11:28:03', '2022-09-07 11:28:03', null, 0, null, 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (59, 'opinion for parent post user badge check', null, 25, 1, 1, '2022-09-08 07:42:22', '2022-09-08 07:42:22',
        null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (60, 'Create new post in multiple circles once', null, 25, null, 1, '2022-09-08 09:19:36', '2022-09-08 09:19:36',
        null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (61, 'opinion post with multiple circles tagged', null, 25, 60, 1, '2022-09-08 09:27:44', '2022-09-08 09:27:44',
        null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (62, 'opinion post with public as selected', null, 25, 60, 1, '2022-09-08 09:28:35', '2022-09-08 09:28:35', null,
        0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (63, 'Admin post creation with circles validation', null, 836, null, 1, '2022-09-08 11:08:05',
        '2022-09-08 11:08:05', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (64, 'Admin post with error', null, 836, null, 1, '2022-09-08 12:31:46', '2022-09-08 12:31:46', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (65, 'lit up', null, 836, null, 1, '2022-09-08 12:45:47', '2022-09-08 12:45:47', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (66, 'lit up', null, 836, null, 1, '2022-09-08 12:47:09', '2022-09-08 12:47:09', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (67, 'sober', null, 836, null, 1, '2022-09-08 12:49:45', '2022-09-08 12:49:45', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (68, 'but why?', null, 836, null, 1, '2022-09-08 12:53:59', '2022-09-08 12:53:59', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (69, 'why so', null, 836, null, 1, '2022-09-08 14:00:34', '2022-09-08 14:00:34', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (70, 'argh', null, 24, null, 1, '2022-09-08 14:02:49', '2022-09-08 14:02:49', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (71, 'multiple', null, 25, null, 1, '2022-09-08 14:45:06', '2022-09-08 14:45:06', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (72, 'just try', null, 24, null, 1, '2022-09-08 15:40:12', '2022-09-08 15:40:12', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (73, 'missed it', null, 24, null, 1, '2022-09-08 15:44:34', '2022-09-08 15:44:34', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (74, 'don''t know', null, 25, null, 1, '2022-09-08 15:50:13', '2022-09-08 15:50:13', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (75, 'hope', null, 25, null, 0, '2022-09-08 15:51:34', '2022-10-28 11:30:00', null, 0, null, 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (76, 'test for 4', null, 25, null, 1, '2022-09-08 15:58:40', '2022-09-08 15:58:40', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (77, 'ok', null, 25, null, 1, '2022-09-08 16:03:47', '2022-09-08 16:03:47', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (78, 'yes', null, 25, null, 1, '2022-09-08 16:05:08', '2022-09-08 16:05:08', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (79, 'tough', null, 25, null, 1, '2022-09-08 16:07:13', '2022-09-08 16:07:13', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (80, 'anyway we can do it', null, 25, null, 1, '2022-09-08 16:08:30', '2022-09-08 16:08:30', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (81, 'but quite interesting', null, 24, null, 1, '2022-09-08 16:12:04', '2022-09-08 16:12:04', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (82, 'let''s see the game', null, 25, null, 1, '2022-09-08 16:29:39', '2022-09-08 16:29:39', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (83, 'ouch', null, 25, null, 1, '2022-09-08 16:30:26', '2022-09-08 16:30:26', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (84, 'hello world', null, 836, null, 1, '2022-09-08 17:00:40', '2022-09-08 17:00:40', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (85, 'no circle', null, 24, null, 1, '2022-09-08 17:11:10', '2022-09-08 17:11:10', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (86, 'Let''s check reindex', null, 836, null, 1, '2022-09-09 09:49:12', '2022-11-21 10:22:23', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (87, 'New post', null, 836, null, 1, '2022-09-09 09:51:24', '2022-09-09 09:51:24', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (88, 'Old version post', null, 25, null, 1, '2022-09-09 12:26:09', '2022-09-09 12:26:09', null, 0, null, 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (89, 'Old version post 1', null, 25, null, 1, '2022-09-09 12:27:11', '2022-09-09 12:27:11', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (90, 'New Post', null, 25, null, 1, '2022-09-09 12:27:32', '2022-09-09 12:27:32', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (91, 'New Post 2', null, 25, null, 1, '2022-09-09 12:27:56', '2022-09-09 12:27:56', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (92, 'opinion link', null, 1, 91, 1, '2022-09-12 11:33:24', '2022-09-12 11:33:24', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (93, 'opinion link', null, 1, 91, 1, '2022-09-12 11:37:57', '2022-09-12 11:37:57', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (94, 'opinion link 2', null, 1, 91, 1, '2022-09-12 11:40:04', '2022-09-12 11:40:04', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (95, 'Added App version', null, 1, null, 1, '2022-09-13 06:23:51', '2022-09-13 06:23:51', null, 0, '0.3.6', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (96, 'Add app version as forward', null, 1, 91, 1, '2022-09-13 06:35:46', '2022-09-13 06:35:46', null, 0,
        '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (97, 'Added App version 2', null, 1, null, 1, '2022-09-13 06:40:41', '2022-09-13 06:40:41', null, 0, '1.15.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (98, 'any issue?', null, 1, null, 1, '2022-09-13 06:41:57', '2022-09-13 06:41:57', null, 0, '1.15.1', 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (99, 'Issue resolved', null, 1, null, 1, '2022-09-13 06:49:49', '2022-09-13 06:49:49', null, 0, '1.15.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (100, 'Issue resolved public', null, 1, null, 0, '2022-09-13 06:50:53', '2022-12-12 07:03:02', null, 0, '1.15.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (101, 'Issue resolved with app version in forward', null, 1, 91, 0, '2022-09-13 06:51:25', '2022-12-15 03:28:28',
        null, 0, '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (102, 'Error check circle ids', null, 1, null, 0, '2022-09-13 08:02:09', '2022-12-15 03:09:18', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (103, 'Error check circle ids 2', null, 1, null, 0, '2022-09-13 08:02:41', '2022-12-13 06:14:57', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (104, 'Error check circle ids 3', null, 1, null, 1, '2022-09-13 08:03:01', '2022-09-13 08:03:01', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (105, 'Error check circle ids 4', null, 1, null, 1, '2022-09-13 08:05:07', '2022-09-13 08:05:07', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (106, 'Error check circle ids 5', null, 1, null, 1, '2022-09-13 08:05:33', '2022-09-13 08:05:33', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (107, 'Error check circle ids 6', null, 1, null, 1, '2022-09-13 08:06:27', '2022-09-13 08:06:27', null, 0,
        '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (108, 'Forward circle ids str to int', null, 1, 91, 1, '2022-09-13 08:07:37', '2022-09-13 08:07:37', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (109, 'Forward circle ids str to int 1', null, 1, 91, 1, '2022-09-13 08:08:13', '2022-09-13 08:08:13', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (110, 'Hell', null, 1, null, 1, '2022-09-13 08:20:09', '2022-09-13 08:20:09', null, 0, '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (111, 'Hell0', null, 1, null, 0, '2022-09-13 08:20:32', '2022-12-09 05:51:25', null, 0, '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (112, '#India', null, 1, null, 1, '2022-09-14 07:30:47', '2022-09-14 07:30:47', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (113, 'India #India', null, 1, null, 1, '2022-09-14 07:31:07', '2022-09-14 07:31:07', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (114, 'Indian #India', null, 1, null, 1, '2022-09-14 07:31:13', '2022-09-14 07:31:13', null, 0, null, 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (115, '75 indepence day #India', null, 1, null, 1, '2022-09-14 07:35:12', '2022-09-14 07:35:12', null, 0, null,
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (116, 'Hashtags not creating', null, 1, null, 1, '2022-09-14 07:38:22', '2022-09-14 07:38:22', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (117, 'Hashtags not creating #India', null, 1, null, 1, '2022-09-14 07:38:30', '2022-09-14 07:38:30', null, 0,
        null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (118, 'Is it #India', null, 1, null, 1, '2022-09-14 07:39:45', '2022-09-14 07:39:45', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (119, 'Karanataka #Floods', null, 1, null, 1, '2022-09-14 08:32:20', '2022-09-14 08:32:20', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (120, 'Tendulkar Yuvaraj Pathan are BACK #Rapido', null, 1, null, 1, '2022-09-14 08:39:54',
        '2022-09-14 08:39:54', null, 0, '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (121, 'J - ust #blinkit', null, 1, null, 1, '2022-09-14 08:41:02', '2022-09-14 08:41:02', null, 0, '1.15.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (122, 'Flight booked,Reach airport #ola', null, 1, null, 1, '2022-09-14 08:42:14', '2022-09-14 08:42:14', null,
        0, '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (123, 'Ikea furnishings #nagasandra', null, 1, null, 1, '2022-09-14 08:43:35', '2022-09-14 08:43:35', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (124, 'coging decoding #logicalreasoning', null, 1, null, 1, '2022-09-14 08:45:47', '2022-09-14 08:45:47', null,
        0, '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (125, 'We do #wedo', null, 1, null, 1, '2022-09-14 08:58:38', '2022-09-14 08:58:38', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (126, 'Public person #public', null, 1, null, 1, '2022-09-14 08:59:12', '2022-09-14 08:59:12', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (127, 'Add two circles #two', null, 1, null, 1, '2022-09-14 09:06:31', '2022-09-14 09:06:31', null, 0, '1.15.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (128, 'Andhra Telangana #two', null, 1, null, 1, '2022-09-14 09:07:13', '2022-09-14 09:07:13', null, 0, '1.15.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (129, 'CS IT #two', null, 1, null, 1, '2022-09-14 09:08:22', '2022-09-14 09:08:22', null, 0, '1.15.1', 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (130, 'Hell n Heaven #two', null, 1, null, 1, '2022-09-14 09:09:18', '2022-09-14 09:09:18', null, 0, '1.15.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (131, 'We You #two', null, 1, null, 1, '2022-09-14 09:12:02', '2022-09-14 09:12:02', null, 0, '1.15.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (132, 'India Pakistan #two', null, 1, null, 1, '2022-09-14 09:12:52', '2022-09-14 09:12:52', null, 0, '1.15.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (133, 'Tollywood Kollywood #two', null, 1, null, 1, '2022-09-14 09:13:56', '2022-09-14 09:13:56', null, 0,
        '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (134, 'West Bengal UP #two', null, 1, null, 1, '2022-09-14 09:15:48', '2022-09-14 09:15:48', null, 0, '1.15.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (135, 'yes or no #two', null, 1, null, 1, '2022-09-14 09:16:51', '2022-09-14 09:16:51', null, 0, '1.15.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (136, 'heel and hill #two', null, 1, null, 1, '2022-09-14 09:18:19', '2022-09-14 09:18:19', null, 0, '1.15.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (137, 'humpy dumpy #two', null, 1, null, 1, '2022-09-14 09:19:38', '2022-09-14 09:19:38', null, 0, '1.15.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (138, 'hakuna matata #two', null, 1, null, 1, '2022-09-14 09:20:15', '2022-09-14 09:20:15', null, 0, '1.15.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (139, 'opinion for parent post user badge check', null, 24, 1, 1, '2022-09-15 04:47:55', '2022-09-15 04:47:55',
        null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (140, 'opinion post with mutiple circles', null, 1, 100, 1, '2022-09-15 10:46:14', '2022-09-15 10:46:14', null,
        0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (141, 'opinion post with mutiple circles', null, 1, 100, 1, '2022-09-15 10:46:14', '2022-09-15 10:46:14', null,
        0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (142, 'opinion post with mutiple circles 2', null, 1, 100, 1, '2022-09-15 10:53:24', '2022-09-15 10:53:24', null,
        0, '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (143, 'opinion post with mutiple circles 3', null, 1, 100, 1, '2022-09-15 10:58:42', '2022-09-15 10:58:42', null,
        0, '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (144, 'opinion post with mutiple circles 4', null, 1, 100, 1, '2022-09-15 11:17:15', '2022-09-15 11:17:15', null,
        0, '1.15.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (145, '#devineni uma group post', null, 24, null, 0, '2022-09-21 07:27:01', '2022-09-21 07:50:39', null, 0, null,
        0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (146, '#devineni uma group post 2', null, 24, null, 1, '2022-09-21 07:51:55', '2022-09-21 15:26:18', null, 0,
        null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (152, '#devineni uma group post 2', null, 830, null, 1, '2022-09-21 08:20:19', '2022-11-22 07:56:26', null, 0,
        null, 2, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (153, '#devineni uma group post 3', null, 830, null, 1, '2022-09-21 08:20:41', '2022-11-21 10:22:34', null, 0,
        null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (188, 'think and react, don''t react and think ', null, 1, null, 1, '2022-09-22 06:40:23', '2022-09-22 06:40:23',
        null, 0, '1.14.5', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (190, 'comments privacy post', null, 829, null, 1, '2022-09-23 07:02:25', '2022-09-23 07:02:25', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (191, 'comments privacy post 2', null, 829, null, 1, '2022-09-23 07:02:48', '2022-09-23 07:02:48', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (192, 'comments privacy post 3', null, 829, null, 1, '2022-09-23 07:05:02', '2022-11-21 10:22:35', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (193, 'opinion with post privacy', null, 829, 100, 1, '2022-09-23 07:08:53', '2022-11-21 10:22:36', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (194, 'opinion with post privacy 2', null, 829, 100, 1, '2022-09-23 07:09:16', '2022-09-23 07:09:16', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (195, 'opinion with post privacy 2', null, 829, 100, 1, '2022-09-23 07:16:38', '2022-09-23 07:16:38', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (196, 'opinion with post privacy 3', null, 829, 100, 1, '2022-09-23 07:17:00', '2022-09-23 07:17:00', null, 0,
        '1.15.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (197, 'create post based on badge card purpose', null, 25, null, 1, '2022-09-24 14:27:09', '2022-09-24 14:27:09',
        null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (198, 'create post based on badge card purpose 1', null, 25, null, 1, '2022-09-24 14:27:16',
        '2022-09-24 14:27:16', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (199, 'create post based on badge card purpose 2', null, 25, null, 0, '2022-09-24 14:27:19',
        '2022-12-15 03:28:51', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (200, 'create post based on badge card purpose 3', null, 25, null, 1, '2022-09-24 14:27:22',
        '2022-09-24 14:27:22', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (201, 'create post based on badge card purpose 4', null, 25, null, 1, '2022-09-24 14:27:25',
        '2022-09-24 14:27:25', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (202, 'create post based on badge card purpose 5', null, 25, null, 1, '2022-09-24 14:27:27',
        '2022-09-24 14:27:27', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (203, 'create post based on badge card purpose 6', null, 25, null, 1, '2022-09-24 14:27:29',
        '2022-09-24 14:27:29', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (204, 'checking how it works', null, 24, null, 1, '2022-09-26 07:05:56', '2022-09-26 07:05:56', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (205, 'opinion for 203', null, 830, 203, 1, '2022-09-29 08:32:52', '2022-09-29 08:32:52', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (206, 'opinion for 194', null, 830, 194, 1, '2022-09-29 08:50:45', '2022-09-29 08:50:45', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (207, 'heic posts', null, 830, null, 1, '2022-09-29 10:15:12', '2022-11-21 10:22:40', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (208, 'heic posts 2', null, 830, null, 1, '2022-09-29 10:18:36', '2022-11-21 10:22:40', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (209, 'hdjd', null, 24, null, 1, '2022-09-29 13:04:29', '2022-09-29 13:04:29', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (210, 'hdjd', null, 24, null, 1, '2022-09-29 13:04:51', '2022-09-29 13:04:51', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (211, '', null, 25, null, 1, '2022-09-29 13:05:34', '2022-09-29 13:05:34', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (212, 'Hello post', null, 25, null, 1, '2022-09-29 13:09:22', '2022-09-29 13:09:22', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (213, 'Hello post', null, 25, null, 1, '2022-09-29 13:10:32', '2022-09-29 13:10:32', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (214, 'Hello post', null, 25, null, 1, '2022-09-29 13:10:59', '2022-09-29 13:10:59', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (215, 'Hello post', null, 25, null, 1, '2022-09-29 13:11:11', '2022-09-29 13:11:11', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (216, 'hello', null, 25, null, 1, '2022-09-29 13:18:04', '2022-09-29 13:18:04', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (217, null, null, 830, null, 1, '2022-09-29 13:19:11', '2022-09-29 13:19:11', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (218, '', null, 830, null, 1, '2022-09-29 13:20:30', '2022-09-29 13:20:30', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (219, '', null, 830, null, 1, '2022-09-29 13:21:39', '2022-09-29 13:21:39', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (220, 'heic posts 3', null, 830, null, 1, '2022-09-30 06:14:06', '2022-11-21 10:22:43', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (221, 'heic posts 4', null, 830, null, 1, '2022-09-30 06:17:04', '2022-11-21 10:22:43', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (222, 'heic posts 5', null, 830, null, 1, '2022-09-30 06:20:12', '2022-11-21 10:22:44', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (223, 'heic posts 6', null, 830, null, 1, '2022-09-30 06:22:29', '2022-11-21 10:22:44', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (224, 'heic posts 7', null, 830, null, 1, '2022-09-30 06:33:09', '2022-11-21 10:22:44', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (225, 'heic posts 7', null, 830, null, 1, '2022-09-30 06:38:56', '2022-11-21 10:22:45', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (226, 'heic posts 8', null, 830, null, 1, '2022-09-30 07:20:40', '2022-11-21 10:22:45', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (227, 'test my feed alogo', null, 830, null, 1, '2022-10-03 11:10:40', '2022-10-03 11:10:40', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (228, 'test my feed al
go 1', null, 830, null, 1, '2022-10-03 11:10:49', '2022-10-03 11:10:49', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (229, 'test my feed algo 2', null, 830, null, 1, '2022-10-03 11:10:57', '2022-10-03 11:10:57', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (230, 'test my feed algo 3', null, 830, null, 1, '2022-10-03 11:11:00', '2022-10-03 11:11:00', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (231, 'test my feed algo 4', null, 830, null, 1, '2022-10-03 11:11:02', '2022-10-03 11:11:02', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (232, 'test my feed algo 5', null, 830, null, 1, '2022-10-03 11:11:04', '2022-10-03 11:11:04', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (233, 'test my feed algo 6', null, 830, null, 1, '2022-10-03 11:11:06', '2022-10-03 11:11:06', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (234, 'test my feed algo 7', null, 1, null, 1, '2022-10-03 11:44:26', '2022-10-03 11:44:26', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (235, 'Hashtags post #hash', null, 1, null, 1, '2022-10-04 06:33:22', '2022-11-21 10:22:47', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (236, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-04 16:11:43', '2022-11-21 10:22:48', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (237, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-04 16:43:42', '2022-11-21 10:22:48', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (238, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-04 16:44:38', '2022-11-21 10:22:48', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (239, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-04 16:45:17', '2022-11-21 10:22:49', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (240, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-04 16:46:10', '2022-11-21 10:22:49', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (241, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-05 04:53:07', '2022-11-21 10:22:49', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (242, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-06 06:48:34', '2022-11-21 10:22:50', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (243, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-06 07:34:58', '2022-11-21 10:22:50', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (244, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-06 07:35:58', '2022-11-21 10:22:50', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (245, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-06 07:37:24', '2022-11-21 10:22:50', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (246, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-06 09:34:48', '2022-11-21 10:22:51', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (247, 'Hashtags post #hashing', null, 828, null, 1, '2022-10-06 09:49:28', '2022-11-21 10:22:51', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (248, 'Hashtags post #hashing 100', null, 828, null, 1, '2022-10-06 09:51:54', '2022-10-06 09:51:54', null, 0,
        '1.16.1', 1, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (249, 'Hi', null, 25, null, 1, '2022-10-06 17:27:45', '2022-10-06 17:27:45', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (250, 'Hashtags post #hashing 100', null, 828, null, 1, '2022-10-07 04:06:17', '2022-10-07 04:08:07', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (251, 'Hashtags post #hashing 100', null, 828, null, 1, '2022-10-07 05:54:56', '2022-10-07 05:54:56', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (252, 'Hashtags post #hashing 1001', null, 828, null, 1, '2022-10-07 06:06:07', '2022-10-07 06:06:07', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (253, 'Hashtags post #hashing 1001', null, 828, null, 1, '2022-10-07 06:14:02', '2022-10-07 06:14:02', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (254, 'Hashtags post #hashing 1001 argh', null, 828, null, 1, '2022-10-07 06:15:06', '2022-10-07 06:15:06', null,
        0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (257, 'Hashtags post #hashing 1001 argh in', null, 828, null, 1, '2022-10-07 06:18:23', '2022-10-07 06:18:23',
        null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (258, 'Hashtags post #hashing 1001 argh in s', null, 828, null, 1, '2022-10-07 06:24:37', '2022-10-07 06:24:37',
        null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (259, 'Hashtags post #hashing 1001 argh in so', null, 828, null, 1, '2022-10-07 06:26:08', '2022-10-07 06:26:08',
        null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (260, 'Hashtags post #hashing 1001 argh in so ok', null, 828, null, 1, '2022-10-07 06:28:57',
        '2022-10-07 06:28:57', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (261, 'Hashtags post #hashing 1001 argh in so ok on', null, 828, null, 1, '2022-10-07 08:30:38',
        '2022-10-07 08:30:38', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (262, 'Hashtags post #hashing 1001 argh in so ok on i', null, 828, null, 1, '2022-10-07 08:32:21',
        '2022-10-07 08:32:21', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (263, 'Hashtags post #hashing 1001 argh in so ok on is', null, 828, null, 1, '2022-10-07 08:35:31',
        '2022-10-07 08:35:31', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (264, 'Hashtags post #hashing 1001 argh in so ok on is y', null, 828, null, 1, '2022-10-07 08:36:28',
        '2022-10-07 08:36:28', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (265, 'Hashtags post #hashing 1001 argh in so ok on is y', null, 828, null, 1, '2022-10-10 06:03:20',
        '2022-10-10 06:03:20', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (266, 'Ha1shtags post #hashing 1001 argh in so ok on is y', null, 828, null, 1, '2022-10-10 06:04:00',
        '2022-10-10 06:04:00', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (267, 'Ha12shtags post #hashing 1001 argh in so ok on is', null, 828, null, 1, '2022-10-10 06:10:28',
        '2022-10-10 06:10:28', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (268, 'uoip #uiop', null, 828, null, 1, '2022-10-10 06:23:25', '2022-10-10 06:23:25', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (269, 'hi #uiop', null, 828, null, 1, '2022-10-10 06:24:13', '2022-10-10 06:24:13', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (270, 'hello #uiop', null, 828, null, 1, '2022-10-10 06:48:25', '2022-10-10 06:48:25', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (271, 'hello #uiop #you', null, 828, null, 1, '2022-10-10 06:50:33', '2022-10-10 06:50:33', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (272, 'hello #uiop #you #YELLOW', null, 828, null, 1, '2022-10-10 06:51:17', '2022-10-10 06:51:17', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (273, 'hello #uiop #you #YELLOW #frequent', null, 828, null, 1, '2022-10-10 07:03:55', '2022-10-10 07:03:55',
        null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (274, 'hello #uiop #you #YELLOW #frequent #etag', null, 828, null, 1, '2022-10-10 07:10:06',
        '2022-10-10 07:10:06', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (275, 'hello #uiop #you #YELLOW #frequent #etag #iop', null, 828, null, 1, '2022-10-10 07:11:26',
        '2022-10-10 07:11:26', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (276, 'hello #uiop #you #YELLOW #frequent #etag #iop *9', null, 828, null, 1, '2022-10-10 07:12:37',
        '2022-10-10 07:12:37', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (277, 'hello #uiop #you #YELLOW #frequent #etag #iop *92', null, 828, null, 1, '2022-10-10 07:14:00',
        '2022-10-10 07:14:00', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (278, 'iuytr', null, 828, null, 1, '2022-10-10 07:33:52', '2022-10-10 07:33:52', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (279, 'Hola hurry', null, 828, null, 1, '2022-10-10 07:58:28', '2022-10-10 07:58:28', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (280, 'Hola hurry 89', null, 828, null, 1, '2022-10-10 08:07:02', '2022-10-10 08:07:02', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (281, 'Hola hurry 891', null, 828, null, 1, '2022-10-10 08:07:31', '2022-10-10 08:07:31', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (282, 'Hola hurry 8912', null, 828, null, 1, '2022-10-10 08:08:58', '2022-10-10 08:08:58', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (283, 'Hola hurry 89123', null, 828, null, 1, '2022-10-10 08:10:03', '2022-10-10 08:10:03', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (284, 'Internal notifications fix', null, 836, null, 1, '2022-10-10 10:52:10', '2022-10-10 10:52:10', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (285, 'Internal notifications fix', null, 836, null, 1, '2022-10-10 10:53:55', '2022-10-10 10:53:55', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (286, 'Internal notifications fix post check', null, 1914, null, 1, '2022-10-10 11:02:28', '2022-10-10 11:02:28',
        null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (287, 'Internal notifications fix post check 2', null, 1916, null, 1, '2022-10-10 11:19:00',
        '2022-10-10 11:19:00', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (288, 'Internal notifications fix post check 3', null, 1916, null, 1, '2022-10-10 11:19:44',
        '2022-10-10 11:19:44', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (289, 'Internal notifications fix post check 4', null, 1916, null, 1, '2022-10-10 11:20:20',
        '2022-10-10 11:20:20', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (290, 'Internal notifications fix post check 5', null, 1916, null, 1, '2022-10-10 11:22:41',
        '2022-10-10 11:22:41', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (291, 'Internal notifications fix post check 6', null, 1916, null, 1, '2022-10-10 11:23:09',
        '2022-10-10 11:23:09', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (292, 'Internal notifications fix post check 7', null, 1916, null, 1, '2022-10-10 11:25:35',
        '2022-10-10 11:25:35', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (293, 'Internal notifications fix post check 8', null, 1916, null, 1, '2022-10-10 17:14:27',
        '2022-10-10 17:14:27', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (294, 'Internal notifications fix post check 8', null, 828, null, 1, '2022-10-10 17:16:02',
        '2022-10-10 17:16:02', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (295, 'Internal notifications fix post check 900', null, 828, null, 1, '2022-10-10 17:29:57',
        '2022-10-10 17:29:57', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (296, 'Internal notifications fix post check 9000', null, 828, null, 1, '2022-10-10 17:32:01',
        '2022-10-10 17:32:01', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (297, 'Internal notifications fix post check 90000', null, 828, null, 1, '2022-10-10 17:33:35',
        '2022-10-10 17:33:35', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (298, 'Internal notifications fix post check 900000', null, 828, null, 1, '2022-10-10 17:36:14',
        '2022-10-10 17:36:14', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (299, 'Internal notifications fix post check 90000000', null, 828, null, 1, '2022-10-10 18:03:41',
        '2022-10-10 18:03:41', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (300, 'Internal notifications fix post check 900000000', null, 828, null, 1, '2022-10-10 18:12:27',
        '2022-10-10 18:12:27', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (301, 'Internal notifications fix post check 90000000000', null, 828, null, 1, '2022-10-10 18:13:47',
        '2022-10-10 18:13:47', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (302, '89076543210', null, 828, 287, 1, '2022-10-10 18:17:23', '2022-10-10 18:17:23', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (303, '890765432101', null, 828, 287, 1, '2022-10-10 18:21:31', '2022-10-10 18:21:31', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (304, '8907654321012', null, 828, null, 1, '2022-10-10 18:22:43', '2022-10-10 18:22:43', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (313, '1234567890', null, 828, 287, 1, '2022-10-11 05:23:16', '2022-10-11 05:23:16', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (314, 'Hel yah', null, 828, null, 1, '2022-10-11 05:23:41', '2022-10-11 05:23:41', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (315, 'Hel yah you', null, 828, null, 1, '2022-10-11 05:24:48', '2022-10-11 05:24:48', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (316, 'Hel yah you iop', null, 828, 287, 1, '2022-10-11 05:25:03', '2022-10-11 05:25:03', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (317, 'Hel yah you iop 90', null, 828, 287, 1, '2022-10-11 05:25:17', '2022-10-11 05:25:17', null, 0, '1.16.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (318, 'index new post check', null, 828, 287, 1, '2022-10-11 05:29:10', '2022-10-11 05:29:10', null, 0, '1.16.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (319, 'index new post check 1', null, 828, 287, 1, '2022-10-11 05:32:29', '2022-10-11 05:32:29', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (320, 'index new post check 3', null, 828, 287, 1, '2022-10-11 05:36:23', '2022-10-11 05:36:23', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (321, 'index new post check 4', null, 828, 287, 1, '2022-10-11 05:40:42', '2022-10-11 05:40:42', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (322, 'index new post check 5', null, 828, 287, 1, '2022-10-11 05:44:05', '2022-10-11 05:44:05', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (323, 'index new post check 6', null, 828, 287, 1, '2022-10-11 06:09:10', '2022-10-11 06:09:10', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (324, 'Admin dashboard post', null, 24, null, 1, '2022-10-11 06:22:28', '2022-10-11 06:22:28', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (325, 'Admin post ', null, 24, null, 1, '2022-10-11 06:23:21', '2022-10-11 06:23:21', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (326, 'admin post on normal user ', null, 24, null, 1, '2022-10-11 06:26:19', '2022-10-11 06:26:19', null, 0,
        null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (327, 'One village tagging normal user', null, 24, null, 1, '2022-10-11 06:29:29', '2022-10-11 06:29:29', null,
        0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (328, 'Two circles and image testing for normal user', null, 24, null, 1, '2022-10-11 06:30:41',
        '2022-10-11 06:30:41', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (329, 'photo validation', null, 25, null, 1, '2022-10-11 06:35:13', '2022-10-11 06:35:13', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (330, 'index new post check 7', null, 828, null, 1, '2022-10-11 06:38:40', '2022-10-11 06:38:40', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (331, 'index new post check 8', null, 828, null, 1, '2022-10-11 06:57:18', '2022-10-11 06:57:18', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (332, 'index new post check 9', null, 828, null, 1, '2022-10-11 06:58:08', '2022-10-11 06:58:08', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (333, 'index new post check 9', null, 828, null, 1, '2022-10-11 07:14:55', '2022-10-11 07:14:55', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (334, 'index new post check 10', null, 828, null, 1, '2022-10-11 07:17:23', '2022-10-11 07:17:23', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (335, 'index new post check 11', null, 828, null, 1, '2022-10-11 07:17:47', '2022-10-11 07:17:47', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (336, 'index new post check 12', null, 828, null, 1, '2022-10-11 07:21:38', '2022-10-11 07:21:38', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (337, 'index new post check 12', null, 828, null, 1, '2022-10-11 07:22:13', '2022-10-11 07:22:13', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (338, 'index new post check 12', null, 828, null, 1, '2022-10-11 07:25:11', '2022-10-11 07:25:11', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (339, 'index new post check 13', null, 828, null, 1, '2022-10-11 10:15:35', '2022-10-11 10:15:35', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (340, 'index new post check 14', null, 828, null, 1, '2022-10-11 10:19:40', '2022-10-11 10:19:40', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (341, 'index new post check 15', null, 828, null, 1, '2022-10-11 10:23:47', '2022-10-11 10:23:47', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (342, 'index new post check 16', null, 828, null, 1, '2022-10-11 10:32:54', '2022-10-11 10:32:54', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (346, 'index new post check 16', null, 828, null, 1, '2022-10-11 10:48:24', '2022-10-11 10:48:24', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (347, 'index new post check 17', null, 828, null, 1, '2022-10-11 10:50:37', '2022-10-11 10:50:37', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (348, 'index new post check 18', null, 828, null, 1, '2022-10-11 10:54:08', '2022-10-11 10:54:08', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (349, 'index new post check 19', null, 828, null, 1, '2022-10-11 10:57:51', '2022-10-11 10:57:51', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (355, 'index new post check 2011', null, 828, null, 1, '2022-10-11 12:02:57', '2022-10-11 12:02:57', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (356, 'index new post check 201', null, 828, null, 1, '2022-10-11 12:04:08', '2022-10-11 12:04:08', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (357, 'index new post check 201', null, 828, null, 1, '2022-10-11 12:09:51', '2022-10-11 12:09:51', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (358, 'index new post check 100', null, 828, null, 1, '2022-10-11 12:31:48', '2022-10-11 12:31:48', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (360, 'index new post check 101', null, 828, null, 1, '2022-10-11 12:34:09', '2022-10-11 12:34:09', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (361, 'index new post check 102', null, 828, null, 1, '2022-10-11 12:35:24', '2022-10-11 12:35:24', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (362, null, null, 828, null, 1, '2022-10-11 12:46:51', '2022-10-11 12:46:51', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (363, null, null, 828, null, 1, '2022-10-11 12:48:05', '2022-10-11 12:48:05', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (364, 'index new post check 102', null, 828, null, 1, '2022-10-11 12:57:03', '2022-10-11 12:57:03', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (365, 'index new post check 103', null, 828, null, 1, '2022-10-11 12:59:33', '2022-10-11 12:59:33', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (372, 'index new post check 104', null, 828, null, 1, '2022-10-11 13:09:06', '2022-10-11 13:09:06', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (373, 'index new post check 105', null, 828, null, 1, '2022-10-11 13:09:40', '2022-10-11 13:09:40', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (376, 'index new post check 106', null, 828, null, 1, '2022-10-11 13:23:15', '2022-10-11 13:23:15', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (380, 'index new post check 107', null, 828, null, 1, '2022-10-11 13:30:30', '2022-10-11 13:30:30', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (381, 'index new post check 108', null, 828, null, 1, '2022-10-11 13:31:16', '2022-10-11 13:31:16', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (382, 'index new post check 109', null, 828, null, 1, '2022-10-11 13:40:23', '2022-10-11 13:40:23', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (383, 'index new post check 110', null, 828, null, 1, '2022-10-11 13:40:54', '2022-10-11 13:40:54', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (384, 'test my feed algo 7', null, 25, null, 1, '2022-10-11 13:44:08', '2022-10-13 11:56:19', null, 0, '1.16.1',
        1, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (385, 'index new post check 110', null, 1919, null, 1, '2022-10-11 17:57:22', '2022-10-11 17:57:22', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (386, 'index new post check 110', null, 1919, null, 1, '2022-10-11 18:18:12', '2022-10-11 18:18:12', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (387, 'index new post check 110', null, 1919, null, 1, '2022-10-11 18:20:08', '2022-10-11 18:20:08', null, 0,
        '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (388, 'index new post check 110', null, 1919, null, 1, '2022-10-11 18:24:25', '2022-10-11 18:24:25', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (389, 'index new post check 111', null, 1919, null, 0, '2022-10-11 18:36:07', '2022-11-16 06:19:53', null, 0,
        '1.16.1', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (390, 'index new post check 112', null, 1919, null, 0, '2022-10-11 18:41:11', '2022-11-16 05:56:25', null, 0,
        '1.16.1', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (391, 'index new post check 113', null, 1919, null, 0, '2022-10-11 18:42:47', '2022-11-16 05:55:44', null, 0,
        '1.16.1', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (392, 'index new post check 114', null, 1919, null, 0, '2022-10-11 18:43:05', '2022-11-16 05:55:03', null, 0,
        '1.16.1', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (393, 'index new post check 116', null, 1919, null, 0, '2022-10-11 18:43:53', '2022-11-21 10:23:18', null, 0,
        '1.16.1', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (394, 'test my feed algo  badge
', null, 827, null, 0, '2022-10-13 07:53:30', '2022-11-08 06:26:11', null, 0, '1.16.1', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (395, '', null, 1, null, 1, '2022-10-13 10:17:32', '2022-10-13 10:17:32', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (396, 'Hello', null, 1, null, 1, '2022-10-13 10:18:29', '2022-10-13 10:18:29', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (397, '', null, 1, null, 1, '2022-10-13 10:19:16', '2022-10-13 10:19:16', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (398, '', null, 1, null, 1, '2022-10-13 10:19:26', '2022-10-13 10:19:26', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (399, '', null, 1, null, 1, '2022-10-13 10:20:03', '2022-10-13 10:20:03', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (400, '', null, 1, null, 0, '2022-10-13 10:20:14', '2022-12-15 03:28:55', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (401, 'index new post check 116', null, 1, null, 1, '2022-10-13 10:23:36', '2022-10-13 10:23:36', null, 0,
        '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (404, 'Hello 123', null, 1, null, 0, '2022-10-13 10:35:08', '2022-10-13 12:18:50', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (410, 'Hello 1234', null, 1, null, 0, '2022-10-13 10:43:32', '2022-10-13 12:15:06', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (411, 'Hello 1234 #HASH', null, 1, null, 0, '2022-10-13 10:46:17', '2022-11-21 10:23:20', null, 0, '1.16.1', 0,
        null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (412, 'You are awesome', null, 1, null, 1, '2022-10-13 11:56:46', '2022-10-13 11:56:46', null, 0, '1.16.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (413, 'How is this', null, 1, null, 0, '2022-10-13 13:12:47', '2022-10-13 13:20:05', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (414, 'Ohh', null, 1, null, 1, '2022-10-13 13:20:51', '2022-10-13 13:20:51', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (415, 'Gotcha', null, 1, null, 0, '2022-10-13 13:30:47', '2022-10-13 13:36:04', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (416, 'Gotcha 2', null, 1, null, 0, '2022-10-13 13:36:11', '2022-10-13 13:36:21', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (417, 'Gotcha 3', null, 1, null, 0, '2022-10-13 13:39:06', '2022-11-21 10:23:21', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (418, 'Gotcha 4', null, 1, null, 0, '2022-10-13 13:39:46', '2022-10-13 13:40:02', null, 0, '1.16.1', 0, null,
        null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (419, '', null, 1, null, 1, '2022-10-13 13:51:10', '2022-10-13 13:54:04', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (420, 'Hello 1234 #HASH', null, 25, null, 1, '2022-10-19 06:58:45', '2022-10-19 06:58:45', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (421, 'Hello 1234 #HASH', null, 25, null, 1, '2022-10-19 07:05:11', '2022-10-19 07:05:11', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (422, 'Hello 1234 #HASH', null, 25, null, 1, '2022-10-19 07:36:24', '2022-10-19 07:36:24', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (423, 'test new posts index
', null, 25, null, 1, '2022-10-25 08:06:54', '2022-10-25 08:06:54', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (424, 'Post 1', null, 831, null, 1, '2022-10-25 09:39:03', '2022-10-25 09:39:03', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (425, 'test new posts index testing
', null, 25, null, 1, '2022-10-26 02:53:45', '2022-10-26 02:53:45', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (426, 'Post 2', null, 831, null, 1, '2022-10-26 06:45:38', '2022-10-26 06:45:38', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (427, 'Post 3', null, 831, null, 1, '2022-10-26 07:19:19', '2022-10-26 07:19:19', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (428, 'Post 3', null, 831, null, 1, '2022-10-26 07:26:33', '2022-10-26 07:26:33', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (429, 'test new posts index testing1
', null, 25, null, 1, '2022-10-28 08:47:10', '2022-10-28 08:47:10', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (430, 'Post 4', null, 1, null, 1, '2022-10-28 12:18:17', '2022-10-28 12:18:17', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (431, 'Post 5', null, 1, null, 1, '2022-10-28 12:36:09', '2022-10-28 12:36:09', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (432, 'Good morning all', null, 25, null, 1, '2022-10-30 05:12:54', '2022-10-30 05:12:54', null, 0, '1.16.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (433, 'Good morning all s', null, 25, null, 1, '2022-10-30 05:13:47', '2022-10-30 05:13:47', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (434, 'Good morning all yes', null, 25, null, 1, '2022-10-31 06:39:50', '2022-10-31 06:39:50', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (435, 'Good morning all yo', null, 25, null, 1, '2022-10-31 06:45:49', '2022-10-31 06:45:49', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (436, 'Good morning all yoh', null, 25, null, 1, '2022-10-31 06:51:38', '2022-10-31 06:51:38', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (437, 'just-in', null, 25, null, 1, '2022-10-31 09:20:24', '2022-10-31 09:20:24', null, 0, '1.16.1', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (438, 'just in bee bar', null, 25, null, 1, '2022-10-31 09:25:22', '2022-10-31 09:25:22', null, 0, '1.16.1', 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (439, 'just in bee bar app', null, 25, null, 1, '2022-11-01 13:33:34', '2022-11-01 13:33:34', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (440, '#ఎర్రన్నాయుడువర్ధంతి', null, 25, null, 1, '2022-11-03 05:44:16', '2022-11-03 05:44:16', null, 0, '1.16.1',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (441, 'opinion for parent post user badge check venkat #testing7', null, 25, 1, 1, '2022-11-03 07:07:50',
        '2022-11-03 07:07:50', null, 0, '1.16.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (442, 'bug fix #testing7', null, 25, 1, 1, '2022-11-03 07:13:38', '2022-11-03 07:13:38', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (443, 'bug fix #testing7', null, 25, 1, 1, '2022-11-03 07:20:03', '2022-11-03 07:20:03', null, 0, '1.16.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (444, 'just in bee bar app', null, 5, null, 1, '2022-11-03 07:26:24', '2022-11-03 07:26:24', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (445, 'just in bee bar app 2', null, 5, null, 1, '2022-11-03 07:27:05', '2022-11-03 07:27:05', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (446, 'just in bee bar app 3', null, 5, null, 1, '2022-11-03 07:36:58', '2022-11-03 07:36:58', null, 0, '1.16.1',
        0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (447, 'operation', null, 25, null, 1, '2022-11-07 11:05:39', '2022-11-07 11:05:39', null, 0, '1.16.1', 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (448, 'operation 2', null, 25, null, 1, '2022-11-07 11:05:46', '2022-11-07 11:05:46', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (449, 'operation 3', null, 25, null, 1, '2022-11-07 11:05:51', '2022-11-07 11:05:51', null, 0, '1.16.1', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (450, 'operation 4', null, 5, null, 1, '2022-11-07 11:57:02', '2022-11-07 11:57:02', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (451, 'operation 5', null, 5, null, 1, '2022-11-08 09:31:54', '2022-11-08 09:31:54', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (452, 'operation 6', null, 5, null, 1, '2022-11-08 10:31:03', '2022-11-08 10:31:03', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (453, 'operation 7', null, 5, null, 1, '2022-11-08 10:31:27', '2022-11-08 10:31:27', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (454, 'operation 8', null, 5, null, 1, '2022-11-08 12:22:19', '2022-11-08 12:22:19', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (455, 'operation #post #post  ', null, 5, null, 1, '2022-11-11 03:40:07', '2022-11-11 03:40:07', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (456, 'two spaces #post  #post', null, 5, null, 1, '2022-11-11 04:16:03', '2022-11-11 04:16:03', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (457, 'two spaces #post #post', null, 5, null, 1, '2022-11-11 04:17:14', '2022-11-11 04:17:14', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (458, '#jagan Hello #jagan ', null, 5, null, 1, '2022-11-11 07:08:12', '2022-11-11 07:08:12', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (459, '#jagan Hello #jagani ', null, 5, null, 1, '2022-11-11 07:08:27', '2022-11-11 07:08:27', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (460, 'Yes #jagan #jagani ', null, 5, null, 1, '2022-11-11 07:08:40', '2022-11-11 07:08:40', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (461, ' #jagani   #jagan Jogan', null, 5, null, 1, '2022-11-11 07:09:07', '2022-11-11 07:09:07', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (462, 'Two jagans #jagan #jagan', null, 5, null, 1, '2022-11-11 08:44:22', '2022-11-11 08:44:22', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (463, 'Two jagans #jagani', null, 5, null, 1, '2022-11-11 09:44:56', '2022-11-21 10:23:28', null, 0, '1.16.2', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (464, 'hehe #jagani', null, 5, null, 1, '2022-11-11 09:45:02', '2022-11-21 10:23:28', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (465, 'Post with medium likes', null, 5, null, 1, '2022-11-15 09:56:17', '2022-11-21 10:23:28', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (466, 'Post with less likes', null, 5, null, 1, '2022-11-15 10:00:14', '2022-11-21 10:20:50', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (467, 'Post with high likes', null, 5, null, 1, '2022-11-15 10:00:46', '2022-11-15 10:00:46', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (468, 'post by user with badge of village 5
no tagging circles , public post', null, 24, null, 1, '2022-11-16 07:10:52', '2022-11-16 07:10:52', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (471, 'Post by normal  user, tagged circle village 5, trends > 20', null, 828, null, 0, '2022-11-16 07:17:16',
        '2022-11-30 13:48:10', null, 0, null, 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (472, 'post by user with badge of village 865, tagging village 5', null, 830, null, 1, '2022-11-16 07:31:44',
        '2022-11-16 07:31:44', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (473, 'village 865 badge user post, public post', null, 830, null, 1, '2022-11-16 10:15:22',
        '2022-11-21 10:23:29', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (474, 'Mandal zptc posted without tagged village 943', null, 8, null, 1, '2022-11-16 10:21:43',
        '2022-11-16 10:21:43', null, 0, null, 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (475, 'Post with exclusive party users', null, 5, null, 0, '2022-11-17 16:19:08', '2022-11-21 10:23:30', null, 0,
        '1.16.2', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (476, 'Post for neutral', null, 2008, null, 1, '2022-11-18 03:58:06', '2022-11-18 03:58:06', null, 0, '1.16.2',
        0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (478, 'TEST FOR NEW FEED V2', null, 25, null, 1, '2022-11-18 04:47:02', '2022-11-18 04:47:02', null, 0, null, 0,
        null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (479, 'POST development_new_posts_v1/_delete_by_query
{
  "query": {
    "match": {
      "id" :393
    }
  }
} testing ra ', null, 25, null, 1, '2022-11-18 04:50:04', '2022-11-18 04:50:04', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (480, 'testing third post for new feed v2', null, 24, null, 1, '2022-11-18 04:51:25', '2022-11-18 04:51:25',
        null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (481, 'testing post 4', null, 24, null, 1, '2022-11-18 05:50:14', '2022-11-18 05:50:14', null, 0, null, 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (534, 'Post comments type 2', null, 830, null, 1, '2022-11-22 08:31:42', '2022-11-22 08:31:42', null, 0,
        '1.16.2', 2, 54, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (541, 'Post comments type 3', null, 830, null, 1, '2022-11-22 08:41:45', '2022-11-22 08:41:45', null, 0,
        '1.16.2', 3, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (554, 'Post comments type 0', null, 830, null, 1, '2022-11-22 09:03:28', '2022-11-22 09:03:28', null, 0,
        '1.16.2', 3, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (557, 'Post  comments type 0', null, 830, null, 1, '2022-11-22 09:05:04', '2022-11-22 09:05:04', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (680, 'peepi ', null, 830, null, 1, '2022-11-30 10:40:41', '2022-11-30 10:40:41', null, 0, '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (681, 'peepi 1', null, 830, null, 1, '2022-11-30 10:40:47', '2022-11-30 10:40:47', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (682, 'peepi 2', null, 830, null, 1, '2022-11-30 10:41:03', '2022-11-30 10:41:03', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (683, 'peepi 3', null, 830, null, 1, '2022-11-30 10:41:17', '2022-11-30 10:41:17', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (684, 'peepi 4', null, 830, null, 1, '2022-11-30 10:41:26', '2022-11-30 10:41:26', null, 0, '1.16.2', 0, null,
        0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (685, 'peepi 5', null, 830, null, 1, '2022-11-30 10:41:34', '2022-11-30 10:41:34', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (686, 'peepi 6', null, 830, null, 1, '2022-11-30 10:41:52', '2022-11-30 10:41:52', null, 0, '1.16.2', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (687, 'village has more than 31 likes', null, 830, null, 0, '2022-11-30 13:43:51', '2022-11-30 13:48:27', null,
        0, '1.16.2', 0, null, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (980, 'Village 5 badge user post 1', null, 1, null, 1, '2022-12-01 11:35:06', '2022-12-01 11:35:06', null, 0,
        '1.16.2', 0, 392, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (981, 'Village 5 badge user post 2', null, 25, null, 1, '2022-12-01 11:35:35', '2022-12-01 11:35:35', null, 0,
        '1.16.2', 0, 392, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (982, 'Village 5 badge user post 3', null, 24, null, 1, '2022-12-01 12:25:05', '2022-12-01 12:25:05', null, 0,
        '1.16.2', 0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (983, 'No location badge user tagging village 5', null, 25, null, 1, '2022-12-01 12:49:52',
        '2022-12-01 12:49:52', null, 0, '1.16.2', 0, 392, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (984, 'No location badge user tagging village 5 .', null, 25, null, 1, '2022-12-01 12:51:53',
        '2022-12-01 12:51:53', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (985, 'badge user not native with more post likes, tagging', null, 25, null, 1, '2022-12-01 12:59:56',
        '2022-12-01 12:59:56', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (986, 'post with > 20 likes', null, 24, null, 0, '2022-12-01 13:03:01', '2022-12-01 13:15:00', null, 0, '1.16.2',
        0, 54, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (987, 'post with > 30 likes', null, 24, null, 0, '2022-12-01 13:05:43', '2022-12-01 13:14:35', null, 0, '1.16.2',
        0, 54, null);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (988, 'post in 54 with > 40 likes', null, 24, null, 1, '2022-12-01 13:19:11', '2022-12-14 07:34:14', null, 0,
        '1.16.2', 0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (989, 'post in 392 with > 40 likes', null, 24, null, 1, '2022-12-01 13:20:21', '2022-12-01 13:20:21', null, 0,
        '1.16.2', 0, 54, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1010, 'Post with all_users by 834', null, 834, null, 1, '2022-12-05 06:35:15', '2022-12-05 06:35:15', null, 0,
        '1.16.2', 1, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1011, 'Post with excl users by 834', null, 834, null, 1, '2022-12-05 06:50:11', '2022-12-05 06:50:11', null, 0,
        '1.16.2', 2, 54, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1185, 'Post with all users by 834', null, 834, null, 1, '2022-12-06 05:57:03', '2022-12-06 05:57:03', null, 0,
        '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1186, 'Post with excl. and neutral by 830', null, 830, null, 1, '2022-12-06 05:59:51', '2022-12-06 05:59:51',
        null, 0, '1.16.2', 2, 54, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1187, 'Post with excl. and neutral 2 by 830', null, 830, null, 1, '2022-12-06 05:59:53', '2022-12-06 05:59:53',
        null, 0, '1.16.2', 2, 54, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1188, 'Post with excl. and by 834', null, 834, null, 1, '2022-12-06 06:03:20', '2022-12-06 06:09:07', null, 0,
        '1.16.2', 3, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1247, 'Post ', null, 834, null, 1, '2022-12-06 09:37:22', '2022-12-06 09:37:22', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1248, 'Post a', null, 834, null, 1, '2022-12-06 09:41:14', '2022-12-06 09:41:14', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1249, 'Post b', null, 834, null, 1, '2022-12-06 09:42:45', '2022-12-06 09:42:45', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1250, 'Post c', null, 834, null, 1, '2022-12-06 09:44:59', '2022-12-06 09:44:59', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1251, 'Post d', null, 834, null, 1, '2022-12-06 09:45:53', '2022-12-06 09:45:53', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1252, 'Post e', null, 834, null, 1, '2022-12-06 09:48:34', '2022-12-06 09:48:34', null, 0, '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1253, 'Post f', null, 834, null, 0, '2022-12-06 09:48:46', '2022-12-15 10:11:30', null, 0, '1.16.2', 0, 392, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1271, 'trending post in 943', null, 834, null, 1, '2022-12-07 11:26:29', '2022-12-07 11:26:29', null, 0,
        '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1272, 'trending post in 949', null, 834, null, 1, '2022-12-07 11:36:32', '2022-12-07 11:36:32', null, 0,
        '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1273, 'trending post 2 in 949', null, 834, null, 1, '2022-12-07 11:38:10', '2022-12-07 11:38:10', null, 0,
        '1.16.2', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1274, 'hi', null, 1, 1, 1, '2022-12-09 06:44:45', '2022-12-09 06:44:45', null, 0, '1.16.2', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1275, 'bug fix #testing7', null, 25, 1, 0, '2022-12-14 07:06:23', '2022-12-15 05:52:47', null, 0, '1.16.4', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1276, 'ppp', null, 25, null, 0, '2022-12-14 07:38:20', '2022-12-15 03:29:44', null, 0, '1.16.4', 0, 392, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1277, 'hi', null, 25, 1, 1, '2022-12-15 09:22:11', '2022-12-15 09:22:11', null, 0, '1.16.4', 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1318, 'ppp', null, 25, null, 1, '2022-12-21 17:15:09', '2022-12-21 17:15:09', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1319, 'hi', null, 25, null, 1, '2022-12-21 17:16:25', '2022-12-21 17:16:25', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1320, null, null, 25, null, 1, '2022-12-23 08:06:49', '2022-12-23 08:06:49', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1321, null, null, 25, null, 1, '2022-12-23 09:13:53', '2022-12-23 09:13:53', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1322, 'Hi', null, 25, null, 1, '2022-12-27 07:02:57', '2022-12-27 07:02:57', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1323, '                                  Hello', null, 25, null, 1, '2022-12-27 07:04:03',
        '2022-12-27 07:04:03', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1324, null, null, 25, null, 1, '2022-12-27 07:07:14', '2022-12-27 07:07:14', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1325, ' ', null, 25, null, 1, '2022-12-27 07:07:24', '2022-12-27 07:07:24', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1326, '', null, 25, null, 1, '2022-12-27 07:11:35', '2022-12-27 07:11:35', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1327, 'Hello                 Join        J', null, 25, null, 1, '2022-12-27 07:12:10', '2022-12-27 07:12:10',
        null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1328, null, null, 25, null, 1, '2022-12-27 07:13:04', '2022-12-27 07:13:04', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1329, '', null, 25, null, 1, '2022-12-27 07:14:10', '2022-12-27 07:14:10', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1330, null, null, 25, null, 1, '2022-12-27 07:38:53', '2022-12-27 07:38:53', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1331, 'Hello', null, 25, null, 1, '2022-12-27 07:40:13', '2022-12-27 07:40:13', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1332, 'Hello              Joy         Hi        K', null, 25, null, 1, '2022-12-27 07:40:38',
        '2022-12-27 07:40:38', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1333, 'Hello              Joy         Hi', null, 25, null, 1, '2022-12-27 07:40:58', '2022-12-27 07:40:58',
        null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1334, null, null, 25, null, 1, '2022-12-27 07:41:25', '2022-12-27 07:41:25', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1335, null, null, 25, null, 1, '2022-12-27 07:48:10', '2022-12-27 07:48:10', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1336, null, null, 25, null, 1, '2022-12-27 07:48:15', '2022-12-27 07:48:15', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1337, null, null, 25, null, 1, '2022-12-27 07:48:22', '2022-12-27 07:48:22', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1338, null, null, 25, null, 1, '2022-12-27 07:48:23', '2022-12-27 07:48:23', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1339, null, null, 25, null, 1, '2022-12-27 08:13:33', '2022-12-27 08:13:33', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1340, 'hhhhh', null, 25, null, 1, '2022-12-27 08:36:05', '2022-12-27 08:36:05', null, 0, '1.16.4', 0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1341, 'Post f #PawanKalyan', null, 5, null, 1, '2023-01-02 21:11:00', '2023-01-02 21:11:00', null, 0, '1.16.1',
        0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1342, 'Post f #Pawankalyan', null, 5, null, 1, '2023-01-02 21:11:09', '2023-01-02 21:11:09', null, 0, '1.16.1',
        0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1343, 'Post f #Pawankalyan #పవన్కళ్యాణ్', null, 5, null, 1, '2023-01-02 21:17:12', '2023-01-02 21:17:12', null,
        0, '1.16.1', 0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1344, 'Post f #పవన్కళ్యాణ్', null, 5, null, 1, '2023-01-02 21:17:19', '2023-01-02 21:17:19', null, 0, '1.16.1',
        0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1345, 'hhhhh', null, 1919, null, 1, '2023-01-03 07:47:26', '2023-01-03 07:47:26', null, 0, '1.16.4', 0, null,
        1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1346, 'video testing', null, 25, null, 0, '2023-01-09 09:12:40', '2023-01-09 09:58:35', null, 0, '1.17.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1347, 'video testing', null, 25, null, 1, '2023-01-09 09:52:57', '2023-01-09 09:52:57', null, 0, '1.17.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1348, 'video testing 1', null, 25, null, 1, '2023-01-09 10:04:49', '2023-01-09 10:04:49', null, 0, '1.17.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1349, 'Post f #CongressYatra #congressyatra', null, 5, null, 1, '2023-01-10 17:57:13', '2023-01-10 17:57:13',
        null, 0, '1.16.1', 0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1350, 'Post f #CongressYatra #congressyatra', null, 5, null, 1, '2023-01-10 18:08:44', '2023-01-10 18:08:44',
        null, 0, '1.16.1', 0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1351, 'Hi #ContentCreate #contentcreate #contentCreate', null, 24, null, 1, '2023-01-11 21:44:43',
        '2023-01-11 21:44:43', null, 0, null, 0, null, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1352, 'video testing 1', null, 25, null, 1, '2023-01-16 06:36:54', '2023-01-16 06:36:54', null, 0, '1.17.1', 0,
        null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1353, 'duplicate content test', null, 25, null, 0, '2023-01-16 06:40:16', '2023-01-16 06:46:50', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1354, 'duplicate content test', null, 25, null, 1, '2023-01-16 06:45:35', '2023-01-16 06:48:57', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1355, 'duplicate content test', null, 25, null, 0, '2023-01-16 06:53:53', '2023-01-16 06:58:24', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1356, 'duplicate content test 1', null, 25, null, 0, '2023-01-16 07:04:45', '2023-01-16 07:07:07', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1357, 'duplicate content test 1', null, 25, null, 0, '2023-01-16 07:07:20', '2023-01-16 07:08:02', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1358, 'duplicate content test 1', null, 25, null, 1, '2023-01-16 07:08:10', '2023-01-16 07:08:10', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1359, 'created at time check', null, 25, null, 0, '2023-01-17 04:51:49', '2023-01-20 04:48:09', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1360, 'Post f #CongressYatra #congressyatra_new', null, 5, null, 1, '2023-01-19 18:42:32',
        '2023-01-19 18:42:32', null, 0, '1.16.1', 0, 54, 0);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1361, 'created at time check recent pic', null, 25, null, 1, '2023-01-20 03:49:03', '2023-01-20 03:49:03', null,
        0, '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1362, 'created at time check recent one', null, 25, null, 1, '2023-01-20 09:35:38', '2023-01-20 09:35:38', null,
        0, '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1363, 'created at time check recent one #jagan', null, 25, null, 1, '2023-01-21 02:16:16',
        '2023-01-21 02:16:16', null, 0, '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1364, 'My new role post', null, 1, null, 1, '2023-01-23 04:13:08', '2023-01-23 04:13:08', null, 0, '1.17.0', 0,
        392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1365, 'My new role post 2', null, 1, null, 1, '2023-01-23 04:13:49', '2023-01-23 04:13:49', null, 0, '1.17.0',
        0, 392, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1482, 'testing to pin a post in feed', null, 25, null, 1, '2023-01-24 08:07:35', '2023-01-24 08:07:35', null, 0,
        '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1483, 'testing purpose of pinned post', null, 25, null, 1, '2023-01-24 08:07:47', '2023-01-24 08:07:47', null,
        0, '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1484, 'testing purpose of pinned post 1', null, 25, null, 1, '2023-01-24 08:07:51', '2023-01-24 08:07:51', null,
        0, '1.17.1', 0, null, 1);
INSERT IGNORE INTO circle_api_development.posts (id, content, dynamic_link, user_id, parent_post_id, active, created_at,
                                                 updated_at, link_id, is_poll, app_version, comments_type,
                                                 party_id_on_comments_type, has_tagged_circles)
VALUES (1485, 'testing purpose of pinned post 2', null, 25, null, 1, '2023-01-24 08:07:55', '2023-01-24 08:07:55', null,
        0, '1.17.1', 0, null, 1);