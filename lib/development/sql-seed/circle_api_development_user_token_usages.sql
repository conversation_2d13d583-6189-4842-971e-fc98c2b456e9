INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1, 1, '1.14.5', '2022-08-29 10:39:17', '2022-08-29 10:39:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (2, 1, '0.3.6', '2022-08-29 10:56:20', '2022-08-29 10:56:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (3, 1, '1.14.6', '2022-08-29 12:52:37', '2022-08-29 12:52:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (4, 1, '1.14.5', '2022-08-30 05:45:04', '2022-08-30 05:45:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (5, 1, '1.14.6', '2022-08-30 06:18:36', '2022-08-30 06:18:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (6, 1, '1.14.6', '2022-08-30 06:33:42', '2022-08-30 06:33:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (7, 1, '1.14.6', '2022-08-30 06:53:47', '2022-08-30 06:53:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (8, 1, '1.14.6', '2022-08-30 07:09:57', '2022-08-30 07:09:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (9, 1, '1.14.6', '2022-08-30 07:46:53', '2022-08-30 07:46:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (10, 1, '1.14.6', '2022-08-30 08:29:57', '2022-08-30 08:29:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (11, 1, '1.14.6', '2022-08-30 08:57:21', '2022-08-30 08:57:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (12, 1, '1.14.6', '2022-08-30 09:21:44', '2022-08-30 09:21:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (13, 1, '1.14.6', '2022-08-30 10:34:49', '2022-08-30 10:34:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (14, 1, '1.14.6', '2022-08-30 15:09:08', '2022-08-30 15:09:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (15, 2, '1.14.6', '2022-08-30 15:12:45', '2022-08-30 15:12:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (16, 2, '1.14.6', '2022-08-30 15:33:30', '2022-08-30 15:33:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (17, 2, '1.14.6', '2022-09-01 05:07:06', '2022-09-01 05:07:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (18, 2, '1.14.6', '2022-09-01 05:26:25', '2022-09-01 05:26:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (19, 2, '1.14.6', '2022-09-01 05:52:25', '2022-09-01 05:52:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (20, 2, '1.14.6', '2022-09-01 07:28:31', '2022-09-01 07:28:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (21, 2, '1.14.6', '2022-09-01 07:44:56', '2022-09-01 07:44:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (22, 2, '1.14.6', '2022-09-01 08:03:14', '2022-09-01 08:03:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (23, 2, '1.14.6', '2022-09-01 08:47:03', '2022-09-01 08:47:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (24, 2, '1.14.6', '2022-09-01 09:02:51', '2022-09-01 09:02:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (25, 2, '1.14.6', '2022-09-01 09:18:21', '2022-09-01 09:18:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (26, 2, '1.14.6', '2022-09-01 09:37:41', '2022-09-01 09:37:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (27, 2, '1.14.6', '2022-09-01 10:33:43', '2022-09-01 10:33:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (28, 2, '1.14.6', '2022-09-01 10:49:04', '2022-09-01 10:49:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (29, 2, '1.14.6', '2022-09-01 11:05:07', '2022-09-01 11:05:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (30, 2, '1.14.6', '2022-09-01 11:23:43', '2022-09-01 11:23:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (31, 2, '1.14.6', '2022-09-01 11:43:01', '2022-09-01 11:43:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (32, 2, '1.14.6', '2022-09-01 11:58:20', '2022-09-01 11:58:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (33, 1, '1.14.5', '2022-09-01 12:16:08', '2022-09-01 12:16:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (34, 2, '1.14.6', '2022-09-01 12:16:52', '2022-09-01 12:16:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (35, 2, '1.14.6', '2022-09-01 12:32:20', '2022-09-01 12:32:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (36, 2, '1.14.6', '2022-09-01 12:51:34', '2022-09-01 12:51:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (37, 2, '1.14.6', '2022-09-02 09:47:45', '2022-09-02 09:47:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (38, 2, '1.14.6', '2022-09-02 10:35:02', '2022-09-02 10:35:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (39, 2, '1.14.6', '2022-09-02 10:52:07', '2022-09-02 10:52:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (40, 2, '1.14.6', '2022-09-02 12:19:58', '2022-09-02 12:19:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (41, 2, '1.14.6', '2022-09-02 12:46:02', '2022-09-02 12:46:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (42, 1, '1.12.0', '2022-09-02 16:06:19', '2022-09-02 16:06:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (43, 1, '1.12.0', '2022-09-02 16:26:36', '2022-09-02 16:26:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (44, 1, '1.12.0', '2022-09-02 16:55:38', '2022-09-02 16:55:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (45, 1, '1.12.0', '2022-09-02 17:38:38', '2022-09-02 17:38:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (46, 1, '1.12.0', '2022-09-02 18:04:47', '2022-09-02 18:04:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (47, 1, '1.12.0', '2022-09-02 18:41:30', '2022-09-02 18:41:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (48, 2, '1.12.0', '2022-09-03 07:21:40', '2022-09-03 07:21:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (49, 2, '1.12.0', '2022-09-03 07:52:31', '2022-09-03 07:52:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (50, 2, '1.12.0', '2022-09-03 12:27:35', '2022-09-03 12:27:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (51, 2, '1.12.0', '2022-09-04 18:23:51', '2022-09-04 18:23:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (52, 2, '1.12.0', '2022-09-04 19:47:14', '2022-09-04 19:47:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (53, 2, '1.12.0', '2022-09-04 20:13:29', '2022-09-04 20:13:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (54, 3, '1.14.5', '2022-09-05 02:27:17', '2022-09-05 02:27:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (55, 2, '1.12.0', '2022-09-05 02:45:59', '2022-09-05 02:45:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (56, 3, '1.14.5', '2022-09-05 03:38:18', '2022-09-05 03:38:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (57, 2, '1.14.6', '2022-09-05 03:41:03', '2022-09-05 03:41:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (58, 3, '1.14.5', '2022-09-05 06:00:17', '2022-09-05 06:00:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (59, 2, '1.14.6', '2022-09-05 07:12:06', '2022-09-05 07:12:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (60, 2, '1.14.6', '2022-09-05 07:30:38', '2022-09-05 07:30:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (61, 2, '1.14.6', '2022-09-05 08:40:38', '2022-09-05 08:40:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (62, 2, '1.14.6', '2022-09-05 08:57:05', '2022-09-05 08:57:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (63, 2, '1.14.6', '2022-09-05 09:13:14', '2022-09-05 09:13:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (64, 2, '1.14.6', '2022-09-05 09:58:08', '2022-09-05 09:58:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (65, 2, '1.14.6', '2022-09-05 10:23:04', '2022-09-05 10:23:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (66, 2, '1.14.6', '2022-09-05 11:35:50', '2022-09-05 11:35:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (67, 2, '1.14.6', '2022-09-05 12:07:34', '2022-09-05 12:07:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (68, 2, '0.3.6', '2022-09-05 12:40:10', '2022-09-05 12:40:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (69, 13, '1.14.5', '2022-09-05 12:50:07', '2022-09-05 12:50:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (70, 2, '1.14.6', '2022-09-05 13:01:09', '2022-09-05 13:01:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (71, 1, '1.14.5', '2022-09-05 13:12:54', '2022-09-05 13:12:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (72, 13, '1.14.5', '2022-09-06 05:24:53', '2022-09-06 05:24:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (73, 13, '1.14.5', '2022-09-06 05:44:18', '2022-09-06 05:44:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (74, 13, '1.14.5', '2022-09-06 06:41:18', '2022-09-06 06:41:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (75, 14, '1.14.5', '2022-09-06 06:55:38', '2022-09-06 06:55:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (76, 14, '1.14.5', '2022-09-06 07:19:31', '2022-09-06 07:19:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (77, 14, '1.14.5', '2022-09-06 07:34:46', '2022-09-06 07:34:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (78, 14, '1.14.5', '2022-09-06 07:51:54', '2022-09-06 07:51:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (79, 14, '1.14.5', '2022-09-06 09:14:13', '2022-09-06 09:14:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (80, 2, '1.14.6', '2022-09-06 09:26:05', '2022-09-06 09:26:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (81, 14, '1.14.5', '2022-09-06 09:31:46', '2022-09-06 09:31:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (82, 2, '1.14.6', '2022-09-06 09:41:09', '2022-09-06 09:41:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (83, 2, '1.14.6', '2022-09-06 09:58:57', '2022-09-06 09:58:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (84, 14, '1.14.5', '2022-09-06 09:59:54', '2022-09-06 09:59:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (85, 2, '1.14.6', '2022-09-06 10:18:19', '2022-09-06 10:18:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (86, 14, '1.14.5', '2022-09-06 10:25:19', '2022-09-06 10:25:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (87, 14, '1.14.5', '2022-09-06 10:57:13', '2022-09-06 10:57:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (88, 14, '1.14.5', '2022-09-06 11:12:34', '2022-09-06 11:12:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (89, 14, '1.11.5', '2022-09-06 11:27:37', '2022-09-06 11:27:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (90, 2, '1.14.6', '2022-09-06 11:37:46', '2022-09-06 11:37:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (91, 14, '1.11.5', '2022-09-06 11:56:20', '2022-09-06 11:56:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (92, 2, '1.14.6', '2022-09-06 12:03:51', '2022-09-06 12:03:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (93, 14, '1.11.5', '2022-09-06 12:12:14', '2022-09-06 12:12:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (94, 14, '1.11.5', '2022-09-06 13:04:13', '2022-09-06 13:04:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (95, 2, '1.14.6', '2022-09-06 13:05:05', '2022-09-06 13:05:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (96, 14, '1.14.0', '2022-09-06 13:19:50', '2022-09-06 13:19:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (97, 14, '1.14.0', '2022-09-07 04:31:31', '2022-09-07 04:31:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (98, 14, '1.14.0', '2022-09-07 05:37:12', '2022-09-07 05:37:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (99, 14, '1.14.0', '2022-09-07 05:59:25', '2022-09-07 05:59:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (100, 2, '1.14.6', '2022-09-07 06:53:39', '2022-09-07 06:53:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (101, 2, '1.14.6', '2022-09-07 07:40:49', '2022-09-07 07:40:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (102, 14, '1.14.0', '2022-09-07 08:27:11', '2022-09-07 08:27:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (103, 13, '1.14.6', '2022-09-07 08:35:19', '2022-09-07 08:35:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (104, 13, '1.14.6', '2022-09-07 08:52:06', '2022-09-07 08:52:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (105, 13, '1.14.6', '2022-09-07 09:07:10', '2022-09-07 09:07:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (106, 2, '1.14.6', '2022-09-07 09:23:54', '2022-09-07 09:23:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (107, 2, '1.14.6', '2022-09-07 09:50:53', '2022-09-07 09:50:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (108, 2, '0.3.6', '2022-09-07 11:23:19', '2022-09-07 11:23:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (109, 2, '1.14.6', '2022-09-07 11:52:30', '2022-09-07 11:52:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (110, 2, '1.14.6', '2022-09-07 12:32:01', '2022-09-07 12:32:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (111, 2, '1.14.6', '2022-09-08 04:01:22', '2022-09-08 04:01:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (112, 2, '1.14.6', '2022-09-08 04:22:52', '2022-09-08 04:22:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (113, 2, '1.14.6', '2022-09-08 05:57:04', '2022-09-08 05:57:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (114, 2, '1.14.6', '2022-09-08 06:17:00', '2022-09-08 06:17:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (115, 2, '1.14.6', '2022-09-08 06:38:35', '2022-09-08 06:38:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (116, 2, '1.14.6', '2022-09-08 06:59:06', '2022-09-08 06:59:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (117, 2, '1.14.6', '2022-09-08 07:14:27', '2022-09-08 07:14:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (118, 2, '0.3.6', '2022-09-08 07:42:12', '2022-09-08 07:42:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (119, 2, '1.14.6', '2022-09-08 09:19:36', '2022-09-08 09:19:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (120, 14, '1.14.0', '2022-09-08 09:35:51', '2022-09-08 09:35:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (121, 2, '1.14.6', '2022-09-08 09:43:53', '2022-09-08 09:43:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (122, 2, '1.14.6', '2022-09-08 11:19:16', '2022-09-08 11:19:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (123, 14, '1.14.0', '2022-09-08 11:37:48', '2022-09-08 11:37:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (124, 14, '1.14.0', '2022-09-08 12:22:37', '2022-09-08 12:22:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (125, 2, '1.14.6', '2022-09-08 17:47:23', '2022-09-08 17:47:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (126, 2, '1.14.6', '2022-09-09 12:26:09', '2022-09-09 12:26:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (127, 2, '1.14.6', '2022-09-09 13:01:35', '2022-09-09 13:01:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (128, 2, '1.14.6', '2022-09-09 13:32:28', '2022-09-09 13:32:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (129, 14, '1.14.0', '2022-09-12 05:18:34', '2022-09-12 05:18:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (130, 2, '1.14.6', '2022-09-12 09:58:09', '2022-09-12 09:58:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (131, 14, '0.3.6', '2022-09-12 10:14:21', '2022-09-12 10:14:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (132, 1, '0.3.6', '2022-09-12 10:15:36', '2022-09-12 10:15:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (133, 1, '0.3.6', '2022-09-12 11:31:03', '2022-09-12 11:31:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (134, 1, '0.3.6', '2022-09-12 12:49:40', '2022-09-12 12:49:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (135, 1, '0.3.6', '2022-09-12 13:18:30', '2022-09-12 13:18:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (136, 14, '1.14.0', '2022-09-12 15:50:32', '2022-09-12 15:50:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (137, 1, '0.3.6', '2022-09-13 06:20:47', '2022-09-13 06:20:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (138, 1, '1.15.1', '2022-09-13 06:40:41', '2022-09-13 06:40:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (139, 1, '1.15.1', '2022-09-13 07:31:35', '2022-09-13 07:31:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (140, 1, '1.15.1', '2022-09-13 08:02:09', '2022-09-13 08:02:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (141, 1, '1.15.1', '2022-09-13 08:20:09', '2022-09-13 08:20:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (142, 14, '1.14.0', '2022-09-13 10:06:48', '2022-09-13 10:06:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (143, 1, '1.15.1', '2022-09-13 10:12:45', '2022-09-13 10:12:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (144, 14, '1.14.0', '2022-09-13 10:22:01', '2022-09-13 10:22:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (145, 14, '1.14.0', '2022-09-13 10:37:23', '2022-09-13 10:37:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (146, 1, '1.15.1', '2022-09-13 11:00:19', '2022-09-13 11:00:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (147, 1, '1.15.1', '2022-09-13 11:15:20', '2022-09-13 11:15:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (148, 1, '1.15.1', '2022-09-13 11:32:09', '2022-09-13 11:32:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (149, 1, '1.15.1', '2022-09-13 12:05:08', '2022-09-13 12:05:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (150, 1, '1.15.1', '2022-09-13 12:20:19', '2022-09-13 12:20:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (151, 1, '1.15.1', '2022-09-14 07:30:22', '2022-09-14 07:30:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (152, 1, '1.15.1', '2022-09-14 08:20:38', '2022-09-14 08:20:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (153, 1, '1.15.1', '2022-09-14 08:35:52', '2022-09-14 08:35:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (154, 1, '1.15.1', '2022-09-14 08:55:10', '2022-09-14 08:55:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (155, 1, '1.15.1', '2022-09-14 09:12:02', '2022-09-14 09:12:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (156, 14, '1.14.0', '2022-09-15 04:47:55', '2022-09-15 04:47:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (157, 14, '1.14.0', '2022-09-15 06:21:45', '2022-09-15 06:21:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (158, 14, '1.14.0', '2022-09-15 06:47:31', '2022-09-15 06:47:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (159, 1, '0.3.6', '2022-09-15 07:02:43', '2022-09-15 07:02:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (160, 1, '1.15.1', '2022-09-15 10:41:19', '2022-09-15 10:41:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (161, 1, '1.15.1', '2022-09-15 10:58:02', '2022-09-15 10:58:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (162, 1, '1.15.1', '2022-09-15 11:17:00', '2022-09-15 11:17:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (163, 14, '1.14.0', '2022-09-15 11:39:38', '2022-09-15 11:39:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (164, 14, '1.14.0', '2022-09-15 12:13:22', '2022-09-15 12:13:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (165, 1, '1.15.1', '2022-09-16 05:01:49', '2022-09-16 05:01:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (166, 1, '1.15.1', '2022-09-16 05:23:18', '2022-09-16 05:23:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (167, 3, '1.15.1', '2022-09-16 05:41:07', '2022-09-16 05:41:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (168, 14, '1.14.0', '2022-09-19 04:48:17', '2022-09-19 04:48:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (169, 3, '1.15.1', '2022-09-19 06:19:55', '2022-09-19 06:19:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (170, 3, '1.15.1', '2022-09-19 06:35:50', '2022-09-19 06:35:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (171, 14, '1.14.0', '2022-09-19 07:42:43', '2022-09-19 07:42:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (172, 14, '1.14.0', '2022-09-19 08:04:59', '2022-09-19 08:04:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (173, 20, '0.3.6', '2022-09-19 08:06:16', '2022-09-19 08:06:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (174, 21, '1.14.0', '2022-09-19 08:08:42', '2022-09-19 08:08:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (175, 22, '1.14.0', '2022-09-19 08:09:35', '2022-09-19 08:09:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (176, 23, '1.14.0', '2022-09-19 08:10:29', '2022-09-19 08:10:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (177, 24, '1.14.0', '2022-09-19 08:11:02', '2022-09-19 08:11:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (178, 25, '1.14.0', '2022-09-19 08:11:43', '2022-09-19 08:11:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (179, 25, '1.14.0', '2022-09-19 09:40:37', '2022-09-19 09:40:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (180, 25, '1.14.0', '2022-09-19 09:56:09', '2022-09-19 09:56:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (181, 25, '1.14.0', '2022-09-19 14:14:06', '2022-09-19 14:14:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (182, 25, '1.14.0', '2022-09-20 11:58:58', '2022-09-20 11:58:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (183, 25, '1.14.0', '2022-09-20 11:58:55', '2022-09-20 11:58:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (184, 25, '1.14.0', '2022-09-20 12:20:21', '2022-09-20 12:20:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (185, 25, '1.14.0', '2022-09-20 12:35:28', '2022-09-20 12:35:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (186, 25, '1.14.0', '2022-09-20 12:52:49', '2022-09-20 12:52:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (187, 25, '1.14.0', '2022-09-21 06:00:39', '2022-09-21 06:00:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (188, 25, '1.14.0', '2022-09-21 06:41:20', '2022-09-21 06:41:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (189, 26, '1.14.0', '2022-09-21 06:46:03', '2022-09-21 06:46:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (190, 26, '1.14.0', '2022-09-21 07:18:34', '2022-09-21 07:18:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (191, 26, '1.14.0', '2022-09-21 07:50:39', '2022-09-21 07:50:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (192, 26, '1.14.0', '2022-09-21 08:08:28', '2022-09-21 08:08:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (193, 31, '1.14.0', '2022-09-21 08:19:49', '2022-09-21 08:19:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (194, 3, '1.15.1', '2022-09-21 08:20:22', '2022-09-21 08:20:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (195, 3, '1.15.1', '2022-09-21 09:04:03', '2022-09-21 09:04:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (196, 3, '0.3.6', '2022-09-21 09:30:29', '2022-09-21 09:30:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (197, 3, '0.3.6', '2022-09-21 09:47:30', '2022-09-21 09:47:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (198, 31, '1.14.0', '2022-09-21 10:00:05', '2022-09-21 10:00:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (199, 3, '0.3.6', '2022-09-21 10:08:32', '2022-09-21 10:08:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (200, 31, '1.14.0', '2022-09-21 10:55:02', '2022-09-21 10:55:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (201, 31, '1.14.0', '2022-09-21 11:28:30', '2022-09-21 11:28:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (202, 3, '0.3.6', '2022-09-21 15:26:17', '2022-09-21 15:26:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (203, 31, '1.14.0', '2022-09-21 16:59:42', '2022-09-21 16:59:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (204, 106, '1.14.0', '2022-09-21 17:01:48', '2022-09-21 17:01:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (205, 107, '1.14.5', '2022-09-22 06:38:44', '2022-09-22 06:38:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (206, 3, '1.15.1', '2022-09-22 06:49:42', '2022-09-22 06:49:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (207, 3, '1.15.1', '2022-09-22 07:32:48', '2022-09-22 07:32:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (208, 107, '1.14.5', '2022-09-22 07:34:52', '2022-09-22 07:34:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (209, 3, '1.15.1', '2022-09-22 07:49:49', '2022-09-22 07:49:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (210, 107, '1.14.5', '2022-09-22 07:55:28', '2022-09-22 07:55:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (211, 3, '1.15.1', '2022-09-22 09:10:47', '2022-09-22 09:10:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (212, 3, '1.15.1', '2022-09-22 09:27:02', '2022-09-22 09:27:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (213, 3, '1.15.1', '2022-09-22 12:00:41', '2022-09-22 12:00:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (214, 3, '1.15.1', '2022-09-22 12:20:05', '2022-09-22 12:20:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (215, 106, '1.14.0', '2022-09-23 05:13:22', '2022-09-23 05:13:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (216, 106, '1.14.0', '2022-09-23 06:21:08', '2022-09-23 06:21:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (217, 3, '1.15.1', '2022-09-23 06:58:40', '2022-09-23 06:58:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (218, 3, '1.15.1', '2022-09-23 07:16:38', '2022-09-23 07:16:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (219, 106, '1.14.0', '2022-09-23 07:26:58', '2022-09-23 07:26:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (220, 106, '1.14.0', '2022-09-23 09:06:10', '2022-09-23 09:06:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (221, 106, '1.14.0', '2022-09-23 09:26:04', '2022-09-23 09:26:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (222, 106, '1.14.0', '2022-09-23 11:16:22', '2022-09-23 11:16:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (223, 3, '1.15.1', '2022-09-23 11:25:49', '2022-09-23 11:25:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (224, 3, '1.15.1', '2022-09-23 11:42:30', '2022-09-23 11:42:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (225, 106, '1.14.0', '2022-09-23 11:42:57', '2022-09-23 11:42:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (226, 3, '1.15.1', '2022-09-23 11:59:33', '2022-09-23 11:59:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (227, 106, '1.14.0', '2022-09-23 12:11:44', '2022-09-23 12:11:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (228, 106, '1.16.1', '2022-09-23 12:30:50', '2022-09-23 12:30:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (229, 106, '1.16.1', '2022-09-23 13:02:51', '2022-09-23 13:02:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (230, 3, '1.15.1', '2022-09-23 16:57:25', '2022-09-23 16:57:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (231, 3, '1.15.1', '2022-09-24 10:13:00', '2022-09-24 10:13:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (232, 106, '1.16.1', '2022-09-24 14:10:16', '2022-09-24 14:10:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (233, 106, '1.16.1', '2022-09-24 14:26:23', '2022-09-24 14:26:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (234, 106, '1.16.1', '2022-09-24 15:17:21', '2022-09-24 15:17:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (235, 106, '1.16.1', '2022-09-25 10:40:34', '2022-09-25 10:40:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (236, 106, '1.16.1', '2022-09-25 11:04:30', '2022-09-25 11:04:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (237, 106, '1.16.1', '2022-09-25 19:04:13', '2022-09-25 19:04:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (238, 106, '1.16.1', '2022-09-26 05:13:09', '2022-09-26 05:13:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (239, 3, '1.15.1', '2022-09-26 05:24:25', '2022-09-26 05:24:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (240, 106, '1.16.1', '2022-09-26 05:28:59', '2022-09-26 05:28:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (241, 106, '1.16.1', '2022-09-26 05:44:49', '2022-09-26 05:44:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (242, 106, '1.16.1', '2022-09-26 06:25:25', '2022-09-26 06:25:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (243, 106, '1.16.1', '2022-09-26 06:48:45', '2022-09-26 06:48:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (244, 3, '1.15.1', '2022-09-26 07:01:03', '2022-09-26 07:01:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (245, 31, '1.15.1', '2022-09-26 07:09:34', '2022-09-26 07:09:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (246, 106, '1.16.1', '2022-09-26 07:26:59', '2022-09-26 07:26:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (247, 106, '1.16.1', '2022-09-26 07:46:03', '2022-09-26 07:46:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (248, 106, '1.16.1', '2022-09-26 08:54:21', '2022-09-26 08:54:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (249, 106, '1.16.1', '2022-09-26 09:12:24', '2022-09-26 09:12:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (250, 31, '1.15.1', '2022-09-26 09:14:37', '2022-09-26 09:14:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (251, 106, '1.16.1', '2022-09-26 09:28:37', '2022-09-26 09:28:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (252, 31, '1.15.1', '2022-09-26 10:04:18', '2022-09-26 10:04:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (253, 31, '1.15.1', '2022-09-26 10:23:58', '2022-09-26 10:23:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (254, 106, '1.16.1', '2022-09-26 10:37:10', '2022-09-26 10:37:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (255, 31, '1.15.1', '2022-09-26 10:54:50', '2022-09-26 10:54:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (256, 106, '1.16.1', '2022-09-26 11:18:17', '2022-09-26 11:18:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (257, 106, '1.16.1', '2022-09-26 11:35:05', '2022-09-26 11:35:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (258, 106, '1.16.1', '2022-09-26 11:51:22', '2022-09-26 11:51:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (259, 31, '1.15.1', '2022-09-26 12:13:14', '2022-09-26 12:13:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (260, 106, '1.16.1', '2022-09-26 12:18:50', '2022-09-26 12:18:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (261, 31, '1.15.1', '2022-09-26 12:34:24', '2022-09-26 12:34:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (262, 31, '1.15.1', '2022-09-26 13:08:01', '2022-09-26 13:08:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (263, 31, '1.15.1', '2022-09-26 13:25:53', '2022-09-26 13:25:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (264, 106, '1.16.1', '2022-09-26 17:53:00', '2022-09-26 17:53:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (265, 31, '1.15.4', '2022-09-27 08:14:56', '2022-09-27 08:14:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (266, 106, '1.16.1', '2022-09-27 12:24:11', '2022-09-27 12:24:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (267, 31, '1.15.4', '2022-09-27 14:28:45', '2022-09-27 14:28:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (268, 31, '1.15.4', '2022-09-27 14:48:10', '2022-09-27 14:48:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (269, 31, '1.15.4', '2022-09-27 15:03:20', '2022-09-27 15:03:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (270, 31, '1.15.4', '2022-09-27 15:38:46', '2022-09-27 15:38:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (271, 31, '1.15.4', '2022-09-27 16:01:41', '2022-09-27 16:01:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (272, 31, '1.15.4', '2022-09-27 16:21:01', '2022-09-27 16:21:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (273, 31, '1.16.1', '2022-09-27 19:07:19', '2022-09-27 19:07:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (274, 31, '1.16.1', '2022-09-28 03:13:32', '2022-09-28 03:13:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (275, 31, '1.16.1', '2022-09-28 03:29:22', '2022-09-28 03:29:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (276, 31, '1.16.0', '2022-09-28 03:44:51', '2022-09-28 03:44:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (277, 31, '1.16.0', '2022-09-28 07:50:08', '2022-09-28 07:50:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (278, 31, '1.16.1', '2022-09-28 08:13:34', '2022-09-28 08:13:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (279, 31, '1.16.1', '2022-09-28 08:44:39', '2022-09-28 08:44:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (280, 106, '1.16.1', '2022-09-28 08:54:52', '2022-09-28 08:54:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (281, 31, '1.16.1', '2022-09-28 09:05:14', '2022-09-28 09:05:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (282, 31, '1.16.1', '2022-09-28 09:40:42', '2022-09-28 09:40:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (283, 31, '1.16.1', '2022-09-28 09:55:45', '2022-09-28 09:55:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (284, 31, '1.16.1', '2022-09-28 10:25:30', '2022-09-28 10:25:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (285, 110, '1.16.1', '2022-09-28 10:41:35', '2022-09-28 10:41:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (286, 110, '1.16.1', '2022-09-29 05:57:39', '2022-09-29 05:57:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (287, 111, '1.16.1', '2022-09-29 05:59:21', '2022-09-29 05:59:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (288, 31, '1.16.1', '2022-09-29 06:09:34', '2022-09-29 06:09:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (289, 31, '1.16.1', '2022-09-29 06:27:59', '2022-09-29 06:27:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (290, 112, '1.16.1', '2022-09-29 06:48:25', '2022-09-29 06:48:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (291, 31, '1.16.1', '2022-09-29 07:25:47', '2022-09-29 07:25:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (292, 31, '1.16.1', '2022-09-29 07:41:39', '2022-09-29 07:41:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (293, 31, '1.16.1', '2022-09-29 08:32:52', '2022-09-29 08:32:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (294, 112, '1.16.1', '2022-09-29 08:41:58', '2022-09-29 08:41:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (295, 31, '1.16.1', '2022-09-29 08:50:11', '2022-09-29 08:50:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (296, 112, '1.16.1', '2022-09-29 09:02:45', '2022-09-29 09:02:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (297, 31, '1.16.1', '2022-09-29 09:19:17', '2022-09-29 09:19:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (298, 112, '1.16.1', '2022-09-29 09:37:54', '2022-09-29 09:37:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (299, 112, '1.16.1', '2022-09-29 09:54:30', '2022-09-29 09:54:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (300, 31, '1.16.1', '2022-09-29 10:15:12', '2022-09-29 10:15:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (301, 111, '1.16.1', '2022-09-29 10:25:14', '2022-09-29 10:25:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (302, 112, '1.16.1', '2022-09-29 10:26:12', '2022-09-29 10:26:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (303, 31, '1.16.1', '2022-09-29 10:34:49', '2022-09-29 10:34:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (304, 112, '1.16.1', '2022-09-29 13:19:11', '2022-09-29 13:19:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (305, 31, '1.16.1', '2022-09-29 13:45:58', '2022-09-29 13:45:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (306, 31, '1.16.1', '2022-09-29 14:02:46', '2022-09-29 14:02:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (307, 31, '1.16.1', '2022-09-29 14:21:35', '2022-09-29 14:21:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (308, 31, '1.16.1', '2022-09-29 14:36:39', '2022-09-29 14:36:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (309, 112, '1.16.1', '2022-09-30 06:10:03', '2022-09-30 06:10:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (310, 112, '1.16.1', '2022-09-30 06:33:09', '2022-09-30 06:33:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (311, 112, '1.16.1', '2022-09-30 07:20:40', '2022-09-30 07:20:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (312, 112, '0.3.6', '2022-10-03 07:30:06', '2022-10-03 07:30:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (313, 1, '1.16.1', '2022-10-03 07:33:57', '2022-10-03 07:33:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (314, 2, '1.16.1', '2022-10-03 07:34:36', '2022-10-03 07:34:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (315, 3, '1.16.1', '2022-10-03 07:35:03', '2022-10-03 07:35:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (316, 4, '1.16.1', '2022-10-03 07:35:16', '2022-10-03 07:35:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (317, 5, '1.16.1', '2022-10-03 07:35:26', '2022-10-03 07:35:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (318, 6, '1.16.1', '2022-10-03 07:35:35', '2022-10-03 07:35:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (319, 7, '1.16.1', '2022-10-03 07:35:46', '2022-10-03 07:35:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (320, 8, '1.16.1', '2022-10-03 07:35:55', '2022-10-03 07:35:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (321, 9, '1.16.1', '2022-10-03 07:36:04', '2022-10-03 07:36:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (322, 10, '1.16.1', '2022-10-03 07:36:14', '2022-10-03 07:36:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (323, 11, '1.16.1', '2022-10-03 07:36:24', '2022-10-03 07:36:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (324, 12, '1.16.1', '2022-10-03 07:36:33', '2022-10-03 07:36:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (325, 14, '1.16.1', '2022-10-03 07:36:44', '2022-10-03 07:36:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (326, 15, '1.16.1', '2022-10-03 07:36:57', '2022-10-03 07:36:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (327, 16, '1.16.1', '2022-10-03 07:37:07', '2022-10-03 07:37:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (328, 18, '1.16.1', '2022-10-03 07:37:48', '2022-10-03 07:37:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (329, 17, '1.16.1', '2022-10-03 07:37:58', '2022-10-03 07:37:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (330, 19, '1.16.1', '2022-10-03 07:38:32', '2022-10-03 07:38:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (331, 2, '1.16.1', '2022-10-03 07:52:40', '2022-10-03 07:52:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (332, 112, '1.16.1', '2022-10-03 08:49:33', '2022-10-03 08:49:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (333, 113, '1.16.1', '2022-10-03 08:50:30', '2022-10-03 08:50:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (334, 113, '1.16.1', '2022-10-03 09:27:07', '2022-10-03 09:27:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (335, 1, '1.16.1', '2022-10-03 10:20:54', '2022-10-03 10:20:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (336, 107, '1.16.1', '2022-10-03 10:42:41', '2022-10-03 10:42:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (337, 112, '1.16.1', '2022-10-03 11:08:37', '2022-10-03 11:08:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (338, 107, '1.16.1', '2022-10-03 11:11:33', '2022-10-03 11:11:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (339, 26, '1.16.1', '2022-10-03 11:13:14', '2022-10-03 11:13:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (340, 107, '1.16.1', '2022-10-03 11:43:51', '2022-10-03 11:43:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (341, 1, '1.16.1', '2022-10-03 13:49:15', '2022-10-03 13:49:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (342, 1, '1.16.1', '2022-10-03 14:04:36', '2022-10-03 14:04:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (343, 1, '1.16.1', '2022-10-03 14:19:58', '2022-10-03 14:19:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (344, 1, '1.16.1', '2022-10-03 15:19:53', '2022-10-03 15:19:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (345, 107, '1.16.1', '2022-10-04 04:04:20', '2022-10-04 04:04:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (346, 111, '1.16.1', '2022-10-04 04:50:26', '2022-10-04 04:50:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (347, 1, '1.16.1', '2022-10-04 05:04:20', '2022-10-04 05:04:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (348, 1, '1.16.1', '2022-10-04 05:21:28', '2022-10-04 05:21:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (349, 1, '1.16.1', '2022-10-04 05:44:51', '2022-10-04 05:44:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (350, 1, '1.16.2', '2022-10-04 06:27:10', '2022-10-04 06:27:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (351, 2, '1.16.2', '2022-10-04 06:40:49', '2022-10-04 06:40:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (352, 3, '1.16.2', '2022-10-04 06:41:04', '2022-10-04 06:41:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (353, 4, '1.16.2', '2022-10-04 06:41:19', '2022-10-04 06:41:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (354, 5, '1.16.2', '2022-10-04 06:41:38', '2022-10-04 06:41:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (355, 111, '1.16.1', '2022-10-04 10:50:39', '2022-10-04 10:50:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (356, 5, '1.16.1', '2022-10-04 16:11:43', '2022-10-04 16:11:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (357, 5, '1.16.1', '2022-10-04 16:30:51', '2022-10-04 16:30:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (358, 5, '1.16.1', '2022-10-04 16:46:03', '2022-10-04 16:46:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (359, 5, '1.16.1', '2022-10-04 17:09:04', '2022-10-04 17:09:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (360, 111, '1.16.1', '2022-10-05 03:22:53', '2022-10-05 03:22:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (361, 5, '1.16.1', '2022-10-05 04:51:58', '2022-10-05 04:51:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (362, 5, '1.16.1', '2022-10-06 06:44:58', '2022-10-06 06:44:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (363, 5, '1.16.1', '2022-10-06 07:12:41', '2022-10-06 07:12:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (364, 5, '1.16.1', '2022-10-06 07:34:25', '2022-10-06 07:34:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (365, 107, '1.14.5', '2022-10-06 07:34:38', '2022-10-06 07:34:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (366, 107, '1.14.5', '2022-10-06 07:52:43', '2022-10-06 07:52:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (367, 5, '1.16.1', '2022-10-06 09:34:30', '2022-10-06 09:34:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (368, 5, '1.16.1', '2022-10-06 09:51:44', '2022-10-06 09:51:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (369, 5, '1.16.1', '2022-10-06 11:50:12', '2022-10-06 11:50:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (370, 5, '1.16.1', '2022-10-06 12:45:17', '2022-10-06 12:45:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (371, 5, '1.16.1', '2022-10-06 16:21:49', '2022-10-06 16:21:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (372, 5, '1.16.1', '2022-10-06 16:48:18', '2022-10-06 16:48:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (373, 5, '1.16.1', '2022-10-06 16:48:41', '2022-10-06 16:48:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (374, 5, '1.16.1', '2022-10-06 16:48:53', '2022-10-06 16:48:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (375, 5, '1.16.1', '2022-10-06 17:32:38', '2022-10-06 17:32:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (376, 5, '1.16.1', '2022-10-06 17:32:51', '2022-10-06 17:32:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (377, 5, '1.16.1', '2022-10-06 17:32:24', '2022-10-06 17:32:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (378, 5, '1.16.1', '2022-10-06 17:36:53', '2022-10-06 17:36:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (379, 5, '1.16.1', '2022-10-06 17:37:04', '2022-10-06 17:37:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (380, 5, '1.16.1', '2022-10-06 17:44:18', '2022-10-06 17:44:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (381, 5, '1.16.1', '2022-10-06 17:44:32', '2022-10-06 17:44:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (382, 5, '1.16.1', '2022-10-06 17:51:00', '2022-10-06 17:51:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (383, 5, '1.16.1', '2022-10-06 17:56:31', '2022-10-06 17:56:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (384, 5, '1.16.1', '2022-10-06 17:51:25', '2022-10-06 17:51:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (385, 5, '1.16.1', '2022-10-06 17:56:54', '2022-10-06 17:56:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (386, 5, '1.16.1', '2022-10-06 17:58:11', '2022-10-06 17:58:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (387, 5, '1.16.1', '2022-10-06 17:58:29', '2022-10-06 17:58:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (388, 5, '1.16.1', '2022-10-07 03:35:19', '2022-10-07 03:35:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (389, 5, '1.16.1', '2022-10-07 04:04:15', '2022-10-07 04:04:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (390, 26, '1.16.1', '2022-10-07 04:07:40', '2022-10-07 04:07:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (391, 110, '1.16.1', '2022-10-07 04:13:27', '2022-10-07 04:13:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (392, 110, '1.16.1', '2022-10-07 05:54:21', '2022-10-07 05:54:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (393, 110, '1.16.1', '2022-10-07 06:13:57', '2022-10-07 06:13:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (394, 110, '1.16.1', '2022-10-07 08:24:22', '2022-10-07 08:24:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (395, 110, '1.16.1', '2022-10-07 11:36:06', '2022-10-07 11:36:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (396, 110, '1.16.1', '2022-10-10 05:47:56', '2022-10-10 05:47:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (397, 110, '1.16.1', '2022-10-10 06:03:11', '2022-10-10 06:03:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (398, 110, '1.16.1', '2022-10-10 06:18:52', '2022-10-10 06:18:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (399, 110, '1.16.1', '2022-10-10 06:48:25', '2022-10-10 06:48:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (400, 110, '1.16.1', '2022-10-10 07:04:23', '2022-10-10 07:04:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (401, 110, '1.16.1', '2022-10-10 07:34:03', '2022-10-10 07:34:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (402, 110, '1.16.1', '2022-10-10 07:49:58', '2022-10-10 07:49:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (403, 110, '1.16.1', '2022-10-10 08:06:31', '2022-10-10 08:06:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (404, 110, '1.16.1', '2022-10-10 09:25:34', '2022-10-10 09:25:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (405, 110, '1.16.1', '2022-10-10 09:46:57', '2022-10-10 09:46:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (406, 12, '1.16.1', '2022-10-10 10:52:10', '2022-10-10 10:52:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (407, 15, '1.16.1', '2022-10-10 11:02:04', '2022-10-10 11:02:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (408, 17, '1.16.1', '2022-10-10 11:19:00', '2022-10-10 11:19:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (409, 17, '1.16.1', '2022-10-10 17:14:27', '2022-10-10 17:14:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (410, 23, '1.16.1', '2022-10-10 17:15:26', '2022-10-10 17:15:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (411, 23, '1.16.1', '2022-10-10 17:32:00', '2022-10-10 17:32:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (412, 23, '1.16.1', '2022-10-10 18:01:57', '2022-10-10 18:01:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (413, 23, '1.16.1', '2022-10-10 18:17:20', '2022-10-10 18:17:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (414, 23, '1.16.1', '2022-10-11 05:10:41', '2022-10-11 05:10:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (415, 23, '1.16.1', '2022-10-11 05:29:08', '2022-10-11 05:29:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (416, 23, '1.16.1', '2022-10-11 06:08:54', '2022-10-11 06:08:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (417, 23, '1.16.1', '2022-10-11 06:38:25', '2022-10-11 06:38:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (418, 23, '1.16.1', '2022-10-11 06:57:18', '2022-10-11 06:57:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (419, 23, '1.16.1', '2022-10-11 07:14:54', '2022-10-11 07:14:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (420, 111, '1.16.1', '2022-10-11 07:28:50', '2022-10-11 07:28:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (421, 111, '1.16.1', '2022-10-11 09:35:53', '2022-10-11 09:35:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (422, 23, '1.16.1', '2022-10-11 10:05:49', '2022-10-11 10:05:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (423, 111, '1.16.1', '2022-10-11 10:21:55', '2022-10-11 10:21:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (424, 23, '1.16.1', '2022-10-11 10:22:14', '2022-10-11 10:22:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (425, 23, '1.16.1', '2022-10-11 10:37:40', '2022-10-11 10:37:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (426, 23, '1.16.1', '2022-10-11 10:53:56', '2022-10-11 10:53:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (427, 23, '1.16.1', '2022-10-11 11:19:45', '2022-10-11 11:19:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (428, 23, '1.16.1', '2022-10-11 11:39:28', '2022-10-11 11:39:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (429, 114, '0.3.6', '2022-10-11 11:53:40', '2022-10-11 11:53:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (430, 23, '1.16.1', '2022-10-11 11:56:23', '2022-10-11 11:56:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (431, 114, '0.3.6', '2022-10-11 12:11:33', '2022-10-11 12:11:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (432, 23, '1.16.1', '2022-10-11 12:31:48', '2022-10-11 12:31:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (433, 23, '1.16.1', '2022-10-11 12:46:51', '2022-10-11 12:46:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (434, 111, '1.16.1', '2022-10-11 13:01:11', '2022-10-11 13:01:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (435, 23, '1.16.1', '2022-10-11 13:02:44', '2022-10-11 13:02:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (436, 111, '1.16.1', '2022-10-11 13:17:39', '2022-10-11 13:17:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (437, 23, '1.16.1', '2022-10-11 13:23:14', '2022-10-11 13:23:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (438, 23, '1.16.1', '2022-10-11 13:40:22', '2022-10-11 13:40:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (439, 111, '1.16.1', '2022-10-11 13:44:08', '2022-10-11 13:44:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (440, 19, '1.16.1', '2022-10-11 17:57:22', '2022-10-11 17:57:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (441, 19, '1.16.1', '2022-10-11 18:18:12', '2022-10-11 18:18:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (442, 19, '1.16.1', '2022-10-11 18:36:07', '2022-10-11 18:36:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (443, 111, '1.16.1', '2022-10-12 02:58:29', '2022-10-12 02:58:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (444, 19, '1.16.2', '2022-10-12 04:31:21', '2022-10-12 04:31:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (445, 2, '1.16.2', '2022-10-12 04:36:53', '2022-10-12 04:36:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (446, 2, '1.16.2', '2022-10-12 04:52:25', '2022-10-12 04:52:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (447, 2, '1.16.2', '2022-10-12 05:17:38', '2022-10-12 05:17:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (448, 2, '1.16.1', '2022-10-12 06:25:33', '2022-10-12 06:25:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (449, 111, '1.16.1', '2022-10-12 07:13:39', '2022-10-12 07:13:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (450, 2, '1.16.1', '2022-10-12 07:40:41', '2022-10-12 07:40:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (451, 114, '1.16.1', '2022-10-12 07:50:13', '2022-10-12 07:50:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (452, 114, '1.16.1', '2022-10-12 08:21:31', '2022-10-12 08:21:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (453, 111, '1.16.1', '2022-10-12 08:23:34', '2022-10-12 08:23:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (454, 111, '1.16.1', '2022-10-12 08:40:01', '2022-10-12 08:40:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (455, 114, '1.16.1', '2022-10-12 08:41:25', '2022-10-12 08:41:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (456, 114, '1.16.1', '2022-10-12 10:57:06', '2022-10-12 10:57:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (457, 2, '1.16.1', '2022-10-12 11:01:00', '2022-10-12 11:01:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (458, 111, '1.16.1', '2022-10-12 11:08:49', '2022-10-12 11:08:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (459, 114, '1.16.1', '2022-10-12 11:15:59', '2022-10-12 11:15:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (460, 114, '1.16.1', '2022-10-12 11:32:48', '2022-10-12 11:32:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (461, 111, '1.16.1', '2022-10-12 11:34:52', '2022-10-12 11:34:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (462, 111, '1.16.1', '2022-10-12 12:35:47', '2022-10-12 12:35:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (463, 115, '1.16.1', '2022-10-13 05:21:26', '2022-10-13 05:21:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (464, 115, '1.16.1', '2022-10-13 07:52:45', '2022-10-13 07:52:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (465, 115, '1.16.1', '2022-10-13 07:53:05', '2022-10-13 07:53:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (466, 115, '1.16.1', '2022-10-13 07:31:19', '2022-10-13 07:31:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (467, 115, '1.16.1', '2022-10-13 07:53:30', '2022-10-13 07:53:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (468, 115, '1.16.1', '2022-10-13 07:53:37', '2022-10-13 07:53:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (469, 115, '1.16.1', '2022-10-13 07:58:29', '2022-10-13 07:58:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (470, 115, '1.16.1', '2022-10-13 08:13:37', '2022-10-13 08:13:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (471, 115, '0.3.6', '2022-10-13 08:15:50', '2022-10-13 08:15:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (472, 115, '1.16.1', '2022-10-13 10:00:24', '2022-10-13 10:00:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (473, 113, '1.16.1', '2022-10-13 10:03:51', '2022-10-13 10:03:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (474, 111, '1.16.1', '2022-10-13 10:04:44', '2022-10-13 10:04:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (475, 116, '1.16.1', '2022-10-13 10:16:07', '2022-10-13 10:16:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (476, 1, '1.16.1', '2022-10-13 10:23:35', '2022-10-13 10:23:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (477, 116, '1.16.1', '2022-10-13 10:33:20', '2022-10-13 10:33:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (478, 116, '1.16.1', '2022-10-13 10:50:36', '2022-10-13 10:50:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (479, 1, '1.16.1', '2022-10-13 11:55:06', '2022-10-13 11:55:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (480, 116, '1.16.1', '2022-10-13 11:55:30', '2022-10-13 11:55:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (481, 116, '1.16.1', '2022-10-13 12:15:05', '2022-10-13 12:15:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (482, 116, '1.16.1', '2022-10-13 13:12:47', '2022-10-13 13:12:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (483, 116, '1.16.1', '2022-10-13 13:30:47', '2022-10-13 13:30:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (484, 116, '1.16.1', '2022-10-13 13:50:19', '2022-10-13 13:50:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (485, 111, '1.16.1', '2022-10-13 14:50:25', '2022-10-13 14:50:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (486, 116, '1.16.1', '2022-10-14 12:19:43', '2022-10-14 12:19:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (487, 116, '0.3.6', '2022-10-15 16:12:47', '2022-10-15 16:12:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (488, 19, '1.16.1', '2022-10-15 16:16:55', '2022-10-15 16:16:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (489, 111, '1.16.1', '2022-10-19 06:07:56', '2022-10-19 06:07:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (490, 19, '1.16.1', '2022-10-19 06:21:21', '2022-10-19 06:21:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (491, 1, '1.16.1', '2022-10-19 06:26:31', '2022-10-19 06:26:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (492, 2, '1.16.1', '2022-10-19 06:58:45', '2022-10-19 06:58:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (493, 2, '1.16.1', '2022-10-19 07:36:23', '2022-10-19 07:36:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (494, 2, '1.16.1', '2022-10-20 15:21:13', '2022-10-20 15:21:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (495, 111, '1.16.1', '2022-10-25 08:06:48', '2022-10-25 08:06:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (496, 111, '1.16.1', '2022-10-26 05:40:57', '2022-10-26 05:40:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (497, 7, '1.16.1', '2022-10-26 06:00:19', '2022-10-26 06:00:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (498, 7, '1.16.1', '2022-10-26 06:41:13', '2022-10-26 06:41:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (499, 7, '1.16.1', '2022-10-26 06:56:28', '2022-10-26 06:56:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (500, 7, '1.16.1', '2022-10-26 07:11:56', '2022-10-26 07:11:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (501, 7, '1.16.1', '2022-10-26 07:28:31', '2022-10-26 07:28:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (502, 7, '1.16.1', '2022-10-26 07:48:06', '2022-10-26 07:48:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (503, 7, '1.16.1', '2022-10-26 08:21:36', '2022-10-26 08:21:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (504, 7, '1.16.1', '2022-10-26 08:57:28', '2022-10-26 08:57:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (505, 111, '1.16.1', '2022-10-27 07:17:17', '2022-10-27 07:17:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (506, 111, '1.16.1', '2022-10-27 07:39:19', '2022-10-27 07:39:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (507, 7, '1.16.1', '2022-10-27 10:01:58', '2022-10-27 10:01:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (508, 7, '1.16.1', '2022-10-27 10:24:12', '2022-10-27 10:24:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (509, 111, '1.16.1', '2022-10-28 02:22:02', '2022-10-28 02:22:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (510, 111, '1.16.1', '2022-10-28 02:37:38', '2022-10-28 02:37:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (511, 111, '1.16.1', '2022-10-28 02:54:37', '2022-10-28 02:54:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (512, 111, '1.16.1', '2022-10-28 03:09:39', '2022-10-28 03:09:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (513, 111, '1.16.1', '2022-10-28 04:44:06', '2022-10-28 04:44:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (514, 111, '1.16.1', '2022-10-28 05:21:15', '2022-10-28 05:21:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (515, 111, '1.16.1', '2022-10-28 06:50:31', '2022-10-28 06:50:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (516, 111, '1.16.1', '2022-10-28 07:44:11', '2022-10-28 07:44:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (517, 111, '1.16.1', '2022-10-28 08:47:10', '2022-10-28 08:47:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (518, 111, '1.16.1', '2022-10-28 09:21:35', '2022-10-28 09:21:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (519, 111, '1.16.1', '2022-10-28 10:17:48', '2022-10-28 10:17:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (520, 7, '1.16.1', '2022-10-28 11:24:33', '2022-10-28 11:24:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (521, 2, '1.16.1', '2022-10-28 11:25:44', '2022-10-28 11:25:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (522, 2, '1.16.1', '2022-10-28 11:40:58', '2022-10-28 11:40:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (523, 2, '1.16.1', '2022-10-28 11:56:36', '2022-10-28 11:56:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (524, 2, '1.16.1', '2022-10-28 12:12:27', '2022-10-28 12:12:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (525, 1, '1.16.1', '2022-10-28 12:18:00', '2022-10-28 12:18:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (526, 2, '1.16.1', '2022-10-28 12:28:01', '2022-10-28 12:28:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (527, 1, '1.16.1', '2022-10-28 12:36:08', '2022-10-28 12:36:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (528, 2, '1.16.1', '2022-10-28 12:43:24', '2022-10-28 12:43:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (529, 2, '1.16.1', '2022-10-28 12:58:54', '2022-10-28 12:58:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (530, 2, '1.16.1', '2022-10-30 05:12:53', '2022-10-30 05:12:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (531, 111, '1.16.1', '2022-10-30 09:02:57', '2022-10-30 09:02:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (532, 111, '1.16.1', '2022-10-31 01:36:35', '2022-10-31 01:36:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (533, 111, '1.16.1', '2022-10-31 02:04:04', '2022-10-31 02:04:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (534, 111, '1.16.1', '2022-10-31 02:11:30', '2022-10-31 02:11:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (535, 111, '1.16.1', '2022-10-31 03:29:57', '2022-10-31 03:29:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (536, 111, '1.16.1', '2022-10-31 03:44:39', '2022-10-31 03:44:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (537, 111, '1.16.1', '2022-10-31 04:46:38', '2022-10-31 04:46:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (538, 111, '1.16.1', '2022-10-31 05:28:31', '2022-10-31 05:28:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (539, 111, '1.16.1', '2022-10-31 05:57:29', '2022-10-31 05:57:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (540, 2, '1.16.1', '2022-10-31 06:37:05', '2022-10-31 06:37:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (541, 2, '1.16.1', '2022-10-31 06:54:12', '2022-10-31 06:54:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (542, 2, '1.16.1', '2022-10-31 09:20:15', '2022-10-31 09:20:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (543, 111, '1.16.1', '2022-10-31 10:10:38', '2022-10-31 10:10:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (544, 111, '1.16.1', '2022-10-31 10:55:32', '2022-10-31 10:55:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (545, 111, '1.16.1', '2022-10-31 11:37:15', '2022-10-31 11:37:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (546, 111, '1.16.1', '2022-10-31 17:26:53', '2022-10-31 17:26:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (547, 2, '1.16.1', '2022-11-01 13:31:47', '2022-11-01 13:31:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (548, 117, '1.16.2.a.1', '2022-11-02 05:33:25', '2022-11-02 05:33:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (549, 117, '1.16.2.a.1', '2022-11-02 05:33:25', '2022-11-02 05:33:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (550, 117, '1.16.2.a.1', '2022-11-02 06:17:49', '2022-11-02 06:17:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (551, 117, '1.16.2.a.1', '2022-11-02 06:17:49', '2022-11-02 06:17:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (552, 118, '1.16.2.a', '2022-11-02 06:34:02', '2022-11-02 06:34:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (553, 118, '1.16.2.a', '2022-11-02 06:34:02', '2022-11-02 06:34:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (554, 119, '1.16.2.a', '2022-11-02 06:36:20', '2022-11-02 06:36:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (555, 119, '1.16.2.a', '2022-11-02 06:36:20', '2022-11-02 06:36:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (556, 111, '1.16.1', '2022-11-02 06:37:28', '2022-11-02 06:37:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (557, 119, '1.16.2.a', '2022-11-02 06:54:38', '2022-11-02 06:54:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (558, 119, '1.16.2.a', '2022-11-02 06:54:38', '2022-11-02 06:54:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (559, 111, '1.16.1', '2022-11-02 08:08:21', '2022-11-02 08:08:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (560, 111, '1.16.1', '2022-11-02 09:07:43', '2022-11-02 09:07:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (561, 111, '1.16.1', '2022-11-02 09:29:27', '2022-11-02 09:29:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (562, 111, '1.16.1', '2022-11-02 09:50:18', '2022-11-02 09:50:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (563, 2, '1.16.1', '2022-11-02 10:42:34', '2022-11-02 10:42:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (564, 125, '1.16.1', '2022-11-02 10:44:53', '2022-11-02 10:44:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (565, 125, '1.16.1', '2022-11-02 11:02:13', '2022-11-02 11:02:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (566, 125, '1.16.1', '2022-11-02 11:54:33', '2022-11-02 11:54:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (567, 125, '1.16.1', '2022-11-02 12:27:51', '2022-11-02 12:27:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (568, 111, '1.16.1', '2022-11-03 05:36:17', '2022-11-03 05:36:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (569, 111, '1.16.1', '2022-11-03 06:03:54', '2022-11-03 06:03:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (570, 111, '1.16.1', '2022-11-03 07:02:38', '2022-11-03 07:02:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (571, 125, '1.16.1', '2022-11-03 07:12:14', '2022-11-03 07:12:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (572, 111, '1.16.1', '2022-11-03 07:20:03', '2022-11-03 07:20:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (573, 125, '1.16.1', '2022-11-03 07:36:48', '2022-11-03 07:36:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (574, 111, '1.16.1', '2022-11-03 09:17:29', '2022-11-03 09:17:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (575, 125, '1.16.1', '2022-11-04 06:52:12', '2022-11-04 06:52:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (576, 111, '1.16.1', '2022-11-04 07:59:02', '2022-11-04 07:59:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (577, 125, '1.16.1', '2022-11-07 10:38:01', '2022-11-07 10:38:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (578, 111, '1.16.1', '2022-11-07 10:49:13', '2022-11-07 10:49:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (579, 111, '1.16.1', '2022-11-07 11:04:54', '2022-11-07 11:04:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (580, 2, '1.16.1', '2022-11-07 11:05:38', '2022-11-07 11:05:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (581, 125, '1.16.1', '2022-11-07 11:08:25', '2022-11-07 11:08:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (582, 125, '1.16.1', '2022-11-07 11:31:07', '2022-11-07 11:31:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (583, 111, '1.16.2', '2022-11-07 11:33:19', '2022-11-07 11:33:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (584, 2, '1.16.1', '2022-11-07 11:34:03', '2022-11-07 11:34:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (585, 125, '1.16.2', '2022-11-07 11:46:24', '2022-11-07 11:46:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (586, 125, '1.16.2', '2022-11-07 12:13:46', '2022-11-07 12:13:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (587, 125, '1.16.2', '2022-11-07 14:14:34', '2022-11-07 14:14:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (588, 127, '1.16.2', '2022-11-07 14:18:20', '2022-11-07 14:18:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (589, 127, '1.16.2', '2022-11-08 06:24:58', '2022-11-08 06:24:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (590, 127, '1.16.2', '2022-11-08 08:56:23', '2022-11-08 08:56:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (591, 125, '1.16.2', '2022-11-08 09:31:54', '2022-11-08 09:31:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (592, 125, '1.16.2', '2022-11-08 10:31:02', '2022-11-08 10:31:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (593, 127, '1.16.2', '2022-11-08 11:59:17', '2022-11-08 11:59:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (594, 125, '1.16.2', '2022-11-08 12:22:18', '2022-11-08 12:22:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (595, 127, '1.16.2', '2022-11-08 12:24:28', '2022-11-08 12:24:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (596, 127, '1.16.2', '2022-11-08 13:00:29', '2022-11-08 13:00:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (597, 128, '1.16.1', '2022-11-08 18:34:28', '2022-11-08 18:34:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (598, 127, '1.16.2', '2022-11-09 07:44:22', '2022-11-09 07:44:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (599, 127, '1.16.2', '2022-11-09 12:39:05', '2022-11-09 12:39:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (600, 127, '1.16.2', '2022-11-10 06:32:39', '2022-11-10 06:32:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (601, 127, '1.16.2', '2022-11-10 10:09:01', '2022-11-10 10:09:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (602, 127, '1.16.2', '2022-11-10 11:10:12', '2022-11-10 11:10:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (603, 127, '1.16.2', '2022-11-10 11:34:16', '2022-11-10 11:34:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (604, 125, '1.16.2', '2022-11-11 03:39:32', '2022-11-11 03:39:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (605, 125, '1.16.2', '2022-11-11 04:15:35', '2022-11-11 04:15:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (606, 125, '1.16.2', '2022-11-11 06:12:26', '2022-11-11 06:12:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (607, 125, '1.16.2', '2022-11-11 07:08:12', '2022-11-11 07:08:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (608, 127, '1.16.2', '2022-11-11 08:24:33', '2022-11-11 08:24:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (609, 125, '1.16.2', '2022-11-11 08:44:21', '2022-11-11 08:44:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (610, 127, '1.16.2', '2022-11-11 08:56:26', '2022-11-11 08:56:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (611, 125, '1.16.2', '2022-11-11 09:44:55', '2022-11-11 09:44:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (612, 127, '1.16.2', '2022-11-14 09:14:10', '2022-11-14 09:14:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (613, 127, '1.16.2', '2022-11-14 09:34:07', '2022-11-14 09:34:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (614, 127, '1.16.2', '2022-11-14 09:49:40', '2022-11-14 09:49:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (615, 127, '1.16.2', '2022-11-14 10:07:22', '2022-11-14 10:07:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (616, 127, '1.16.2', '2022-11-14 10:23:31', '2022-11-14 10:23:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (617, 127, '1.16.2', '2022-11-14 12:36:36', '2022-11-14 12:36:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (618, 127, '1.16.2', '2022-11-15 04:25:21', '2022-11-15 04:25:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (619, 127, '1.16.2', '2022-11-15 05:34:33', '2022-11-15 05:34:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (620, 127, '1.16.2', '2022-11-15 05:51:06', '2022-11-15 05:51:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (621, 127, '1.16.2', '2022-11-15 06:08:29', '2022-11-15 06:08:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (622, 127, '1.16.2', '2022-11-15 06:25:15', '2022-11-15 06:25:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (623, 127, '1.16.2', '2022-11-15 06:42:01', '2022-11-15 06:42:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (624, 127, '1.16.2', '2022-11-15 07:49:19', '2022-11-15 07:49:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (625, 125, '1.16.2', '2022-11-15 09:56:17', '2022-11-15 09:56:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (626, 127, '1.16.2', '2022-11-15 13:17:58', '2022-11-15 13:17:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (627, 127, '1.16.2', '2022-11-16 04:41:12', '2022-11-16 04:41:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (628, 127, '1.16.2', '2022-11-16 05:07:30', '2022-11-16 05:07:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (629, 127, '1.16.2', '2022-11-16 05:36:19', '2022-11-16 05:36:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (630, 127, '1.16.2', '2022-11-16 05:53:50', '2022-11-16 05:53:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (631, 127, '1.16.2', '2022-11-16 06:10:17', '2022-11-16 06:10:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (632, 127, '1.16.2', '2022-11-16 07:20:35', '2022-11-16 07:20:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (633, 127, '1.16.2', '2022-11-16 07:36:08', '2022-11-16 07:36:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (634, 127, '1.16.2', '2022-11-16 08:08:46', '2022-11-16 08:08:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (635, 127, '1.16.2', '2022-11-16 08:50:55', '2022-11-16 08:50:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (636, 127, '1.16.2', '2022-11-16 10:40:26', '2022-11-16 10:40:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (637, 127, '1.16.2', '2022-11-16 11:41:58', '2022-11-16 11:41:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (638, 127, '1.16.2', '2022-11-16 11:59:14', '2022-11-16 11:59:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (639, 127, '1.16.2', '2022-11-16 12:19:47', '2022-11-16 12:19:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (640, 127, '1.16.2', '2022-11-16 15:26:55', '2022-11-16 15:26:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (641, 127, '1.16.2', '2022-11-16 17:52:22', '2022-11-16 17:52:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (642, 127, '1.16.2', '2022-11-17 04:41:28', '2022-11-17 04:41:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (643, 127, '1.16.2', '2022-11-17 04:55:31', '2022-11-17 04:55:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (644, 127, '1.16.2', '2022-11-17 05:10:58', '2022-11-17 05:10:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (645, 127, '1.16.2', '2022-11-17 06:03:52', '2022-11-17 06:03:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (646, 127, '1.16.2', '2022-11-17 06:04:13', '2022-11-17 06:04:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (647, 127, '1.16.2', '2022-11-17 06:39:11', '2022-11-17 06:39:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (648, 127, '1.16.2', '2022-11-17 06:54:34', '2022-11-17 06:54:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (649, 127, '1.16.2', '2022-11-17 07:13:47', '2022-11-17 07:13:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (650, 127, '1.16.2', '2022-11-17 08:53:35', '2022-11-17 08:53:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (651, 127, '1.16.2', '2022-11-17 09:19:27', '2022-11-17 09:19:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (652, 127, '1.16.2', '2022-11-17 10:10:26', '2022-11-17 10:10:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (653, 127, '1.16.2', '2022-11-17 10:39:01', '2022-11-17 10:39:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (654, 127, '1.16.2', '2022-11-17 11:05:51', '2022-11-17 11:05:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (655, 127, '1.16.2', '2022-11-17 12:06:45', '2022-11-17 12:06:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (656, 125, '1.16.2', '2022-11-17 16:14:21', '2022-11-17 16:14:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (657, 112, '1.16.2', '2022-11-17 16:28:57', '2022-11-17 16:28:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (658, 126, '1.16.2', '2022-11-17 16:30:57', '2022-11-17 16:30:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (659, 125, '1.16.2', '2022-11-17 16:37:43', '2022-11-17 16:37:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (660, 17, '1.16.2', '2022-11-17 16:40:04', '2022-11-17 16:40:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (661, 124, '1.16.2', '2022-11-17 16:41:10', '2022-11-17 16:41:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (662, 1, '1.16.2', '2022-11-17 16:42:35', '2022-11-17 16:42:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (663, 4, '1.16.2', '2022-11-17 16:57:07', '2022-11-17 16:57:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (664, 4, '1.16.2', '2022-11-17 17:25:47', '2022-11-17 17:25:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (665, 127, '1.16.2', '2022-11-17 18:28:46', '2022-11-17 18:28:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (666, 127, '1.16.2', '2022-11-17 18:41:54', '2022-11-17 18:41:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (667, 127, '1.16.2', '2022-11-17 18:57:08', '2022-11-17 18:57:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (668, 127, '1.16.2', '2022-11-17 18:57:23', '2022-11-17 18:57:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (669, 127, '1.16.2', '2022-11-17 19:12:28', '2022-11-17 19:12:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (670, 127, '1.16.2', '2022-11-17 19:27:37', '2022-11-17 19:27:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (671, 4, '1.16.2', '2022-11-18 03:15:41', '2022-11-18 03:15:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (672, 4, '1.16.2', '2022-11-18 03:48:30', '2022-11-18 03:48:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (673, 117, '1.16.2', '2022-11-18 03:56:59', '2022-11-18 03:56:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (674, 4, '1.16.2', '2022-11-18 04:06:47', '2022-11-18 04:06:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (675, 127, '1.16.2', '2022-11-18 04:55:28', '2022-11-18 04:55:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (676, 127, '1.16.2', '2022-11-18 05:35:59', '2022-11-18 05:35:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (677, 127, '1.16.2', '2022-11-18 05:44:46', '2022-11-18 05:44:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (678, 127, '1.16.2', '2022-11-18 05:44:45', '2022-11-18 05:44:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (679, 127, '1.16.2', '2022-11-18 05:45:39', '2022-11-18 05:45:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (680, 127, '1.16.2', '2022-11-18 05:45:01', '2022-11-18 05:45:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (681, 127, '1.16.2', '2022-11-18 06:01:23', '2022-11-18 06:01:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (682, 127, '1.16.2', '2022-11-18 06:18:08', '2022-11-18 06:18:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (683, 127, '1.16.2', '2022-11-18 07:48:06', '2022-11-18 07:48:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (684, 4, '1.16.2', '2022-11-18 11:40:20', '2022-11-18 11:40:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (685, 4, '1.16.2', '2022-11-20 08:56:28', '2022-11-20 08:56:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (686, 127, '1.16.2', '2022-11-20 13:13:41', '2022-11-20 13:13:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (687, 127, '1.16.4', '2022-11-20 13:28:49', '2022-11-20 13:28:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (688, 127, '1.16.4', '2022-11-20 13:28:02', '2022-11-20 13:28:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (689, 127, '1.16.4', '2022-11-20 13:28:37', '2022-11-20 13:28:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (690, 127, '1.16.4', '2022-11-20 13:44:55', '2022-11-20 13:44:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (691, 127, '1.16.4', '2022-11-21 02:24:55', '2022-11-21 02:24:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (692, 127, '1.16.4', '2022-11-21 02:46:51', '2022-11-21 02:46:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (693, 127, '1.16.4', '2022-11-21 03:09:02', '2022-11-21 03:09:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (694, 127, '1.16.4', '2022-11-21 03:42:47', '2022-11-21 03:42:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (695, 127, '1.16.4', '2022-11-21 05:16:37', '2022-11-21 05:16:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (696, 127, '1.16.4', '2022-11-21 05:32:05', '2022-11-21 05:32:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (697, 127, '1.16.4', '2022-11-21 06:23:21', '2022-11-21 06:23:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (698, 127, '1.16.4', '2022-11-21 06:40:30', '2022-11-21 06:40:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (699, 127, '1.16.4', '2022-11-21 06:55:31', '2022-11-21 06:55:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (700, 127, '1.16.4', '2022-11-21 07:33:51', '2022-11-21 07:33:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (701, 127, '1.16.4', '2022-11-21 08:13:17', '2022-11-21 08:13:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (702, 127, '1.16.4', '2022-11-21 08:47:54', '2022-11-21 08:47:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (703, 127, '1.16.4', '2022-11-21 10:00:38', '2022-11-21 10:00:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (704, 127, '1.16.4', '2022-11-21 10:16:57', '2022-11-21 10:16:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (705, 127, '1.16.4', '2022-11-21 15:41:17', '2022-11-21 15:41:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (706, 127, '1.16.4', '2022-11-22 02:58:07', '2022-11-22 02:58:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (707, 127, '1.16.4', '2022-11-22 03:50:16', '2022-11-22 03:50:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (708, 127, '1.16.4', '2022-11-22 05:20:29', '2022-11-22 05:20:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (709, 127, '1.16.4', '2022-11-22 05:56:29', '2022-11-22 05:56:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (710, 127, '1.16.4', '2022-11-22 06:16:28', '2022-11-22 06:16:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (711, 127, '1.16.4', '2022-11-22 06:59:03', '2022-11-22 06:59:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (712, 4, '1.16.2', '2022-11-22 07:50:46', '2022-11-22 07:50:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (713, 4, '1.16.2', '2022-11-22 08:19:41', '2022-11-22 08:19:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (714, 125, '1.16.2', '2022-11-22 08:34:58', '2022-11-22 08:34:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (715, 1, '1.16.2', '2022-11-22 08:35:35', '2022-11-22 08:35:35');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (716, 127, '1.16.4', '2022-11-22 08:36:25', '2022-11-22 08:36:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (717, 8, '1.16.2', '2022-11-22 08:37:03', '2022-11-22 08:37:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (718, 4, '1.16.2', '2022-11-22 08:37:51', '2022-11-22 08:37:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (719, 127, '1.16.4', '2022-11-22 08:52:39', '2022-11-22 08:52:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (720, 4, '1.16.2', '2022-11-22 08:55:12', '2022-11-22 08:55:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (721, 127, '1.16.4', '2022-11-22 09:07:42', '2022-11-22 09:07:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (722, 127, '1.16.4', '2022-11-22 09:32:31', '2022-11-22 09:32:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (723, 127, '1.16.4', '2022-11-22 09:51:29', '2022-11-22 09:51:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (724, 127, '1.16.4', '2022-11-22 10:15:00', '2022-11-22 10:15:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (725, 269, '1.16.4', '2022-11-22 10:15:53', '2022-11-22 10:15:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (726, 270, '1.16.4', '2022-11-22 10:17:45', '2022-11-22 10:17:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (727, 270, '1.16.4', '2022-11-22 16:02:44', '2022-11-22 16:02:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (728, 270, '1.16.4', '2022-11-22 16:24:19', '2022-11-22 16:24:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (729, 270, '1.16.4', '2022-11-22 16:31:24', '2022-11-22 16:31:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (730, 270, '1.16.4', '2022-11-22 16:31:08', '2022-11-22 16:31:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (731, 270, '1.16.4', '2022-11-22 16:27:02', '2022-11-22 16:27:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (732, 270, '1.16.4', '2022-11-22 16:31:01', '2022-11-22 16:31:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (733, 270, '1.16.4', '2022-11-22 16:31:19', '2022-11-22 16:31:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (734, 270, '1.16.4', '2022-11-23 11:45:33', '2022-11-23 11:45:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (735, 4, '1.16.2', '2022-11-24 06:05:04', '2022-11-24 06:05:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (736, 270, '1.16.4', '2022-11-24 10:12:44', '2022-11-24 10:12:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (737, 270, '1.16.4', '2022-11-24 10:30:54', '2022-11-24 10:30:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (738, 270, '1.16.4', '2022-11-24 11:39:03', '2022-11-24 11:39:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (739, 270, '1.16.4', '2022-11-24 11:58:54', '2022-11-24 11:58:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (740, 270, '1.16.4', '2022-11-24 12:20:34', '2022-11-24 12:20:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (741, 270, '1.16.4', '2022-11-24 12:42:23', '2022-11-24 12:42:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (742, 270, '1.16.4', '2022-11-25 07:02:24', '2022-11-25 07:02:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (743, 270, '1.16.4', '2022-11-25 09:29:11', '2022-11-25 09:29:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (744, 270, '1.16.4', '2022-11-25 09:37:22', '2022-11-25 09:37:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (745, 270, '1.16.4', '2022-11-25 09:49:17', '2022-11-25 09:49:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (746, 270, '1.16.4', '2022-11-25 09:50:33', '2022-11-25 09:50:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (747, 270, '1.16.4', '2022-11-25 09:50:36', '2022-11-25 09:50:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (748, 270, '1.16.4', '2022-11-25 09:50:28', '2022-11-25 09:50:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (749, 270, '1.16.4', '2022-11-25 09:51:23', '2022-11-25 09:51:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (750, 270, '1.16.4', '2022-11-25 09:51:28', '2022-11-25 09:51:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (751, 270, '1.16.4', '2022-11-25 09:51:30', '2022-11-25 09:51:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (752, 270, '1.16.4', '2022-11-25 09:52:30', '2022-11-25 09:52:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (753, 270, '1.16.4', '2022-11-25 09:52:48', '2022-11-25 09:52:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (754, 270, '1.16.4', '2022-11-25 09:53:02', '2022-11-25 09:53:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (761, 457, '1.16.4', '2022-12-02 04:54:01', '2022-12-02 04:54:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (762, 457, '1.16.4', '2022-12-02 05:44:43', '2022-12-02 05:44:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (763, 457, '1.16.4', '2022-12-04 11:48:12', '2022-12-04 11:48:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (764, 457, '1.16.4', '2022-12-04 16:54:06', '2022-12-04 16:54:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (765, 457, '1.16.4', '2022-12-04 17:12:00', '2022-12-04 17:12:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (766, 457, '1.16.4', '2022-12-04 17:27:11', '2022-12-04 17:27:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (768, 457, '1.16.4', '2022-12-06 09:49:50', '2022-12-06 09:49:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (769, 457, '1.16.4', '2022-12-06 10:06:03', '2022-12-06 10:06:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (770, 457, '1.16.4', '2022-12-06 10:23:44', '2022-12-06 10:23:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (771, 457, '1.16.4', '2022-12-07 04:13:58', '2022-12-07 04:13:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (772, 457, '1.16.4', '2022-12-07 04:54:31', '2022-12-07 04:54:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (773, 457, '1.16.4', '2022-12-07 05:54:20', '2022-12-07 05:54:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (774, 457, '1.16.4', '2022-12-07 06:14:54', '2022-12-07 06:14:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (775, 457, '1.16.4', '2022-12-07 06:48:19', '2022-12-07 06:48:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (776, 457, '1.16.4', '2022-12-07 07:06:54', '2022-12-07 07:06:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (777, 457, '1.16.4', '2022-12-07 07:41:07', '2022-12-07 07:41:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (778, 457, '1.16.4', '2022-12-07 09:05:05', '2022-12-07 09:05:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (779, 759, '1.16.4', '2022-12-07 09:10:19', '2022-12-07 09:10:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (780, 759, '1.16.4', '2022-12-07 09:40:17', '2022-12-07 09:40:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (781, 760, '1.16.4', '2022-12-07 09:43:41', '2022-12-07 09:43:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (782, 760, '1.16.4', '2022-12-07 09:59:56', '2022-12-07 09:59:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (783, 760, '1.16.4', '2022-12-07 10:20:14', '2022-12-07 10:20:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (784, 760, '1.16.4', '2022-12-07 10:37:06', '2022-12-07 10:37:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (785, 760, '1.16.4', '2022-12-07 10:59:08', '2022-12-07 10:59:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (786, 760, '1.16.4', '2022-12-07 11:48:54', '2022-12-07 11:48:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (787, 760, '1.16.4', '2022-12-07 12:31:59', '2022-12-07 12:31:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (788, 760, '1.16.4', '2022-12-07 12:50:43', '2022-12-07 12:50:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (789, 760, '1.16.4', '2022-12-07 13:10:39', '2022-12-07 13:10:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (790, 760, '1.16.4', '2022-12-07 13:32:56', '2022-12-07 13:32:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (791, 760, '1.16.4', '2022-12-08 07:57:12', '2022-12-08 07:57:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (792, 760, '1.16.4', '2022-12-09 05:31:29', '2022-12-09 05:31:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (793, 760, '1.16.4', '2022-12-09 05:51:25', '2022-12-09 05:51:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (794, 760, '1.16.4', '2022-12-09 06:53:54', '2022-12-09 06:53:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (795, 760, '1.16.4', '2022-12-09 07:17:29', '2022-12-09 07:17:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (796, 760, '1.16.4', '2022-12-09 08:49:37', '2022-12-09 08:49:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (797, 760, '1.16.4', '2022-12-12 06:24:29', '2022-12-12 06:24:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (798, 760, '1.16.4', '2022-12-12 06:41:28', '2022-12-12 06:41:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (799, 760, '1.16.4', '2022-12-12 07:03:02', '2022-12-12 07:03:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (800, 760, '1.16.4', '2022-12-12 07:28:03', '2022-12-12 07:28:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (801, 760, '1.16.4', '2022-12-12 08:32:46', '2022-12-12 08:32:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (802, 760, '1.16.4', '2022-12-12 09:44:50', '2022-12-12 09:44:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (803, 760, '1.16.4', '2022-12-12 10:03:34', '2022-12-12 10:03:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (804, 760, '1.16.4', '2022-12-13 02:55:18', '2022-12-13 02:55:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (805, 760, '1.16.4', '2022-12-13 03:23:14', '2022-12-13 03:23:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (806, 760, '1.16.4', '2022-12-13 03:23:53', '2022-12-13 03:23:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (807, 760, '1.16.4', '2022-12-13 03:23:31', '2022-12-13 03:23:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (808, 760, '1.16.4', '2022-12-13 03:23:02', '2022-12-13 03:23:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (809, 760, '1.16.4', '2022-12-13 03:43:57', '2022-12-13 03:43:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (810, 760, '1.16.4', '2022-12-13 04:04:44', '2022-12-13 04:04:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (811, 760, '1.16.4', '2022-12-13 04:30:39', '2022-12-13 04:30:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (812, 760, '1.16.4', '2022-12-13 04:47:31', '2022-12-13 04:47:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (813, 760, '1.16.4', '2022-12-13 06:14:57', '2022-12-13 06:14:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (814, 760, '1.16.4', '2022-12-13 16:06:32', '2022-12-13 16:06:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (815, 760, '1.16.4', '2022-12-13 16:09:33', '2022-12-13 16:09:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (816, 760, '1.16.4', '2022-12-13 16:52:41', '2022-12-13 16:52:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (817, 760, '1.16.4', '2022-12-14 07:04:23', '2022-12-14 07:04:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (818, 760, '1.16.4', '2022-12-14 07:27:19', '2022-12-14 07:27:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (819, 760, '1.16.4', '2022-12-14 11:05:17', '2022-12-14 11:05:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (820, 760, '1.16.4', '2022-12-14 14:05:17', '2022-12-14 14:05:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (821, 760, '1.16.4', '2022-12-15 03:09:18', '2022-12-15 03:09:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (822, 760, '1.16.4', '2022-12-15 03:25:01', '2022-12-15 03:25:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (823, 760, '1.16.4', '2022-12-15 05:52:30', '2022-12-15 05:52:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (824, 760, '1.16.4', '2022-12-15 06:36:23', '2022-12-15 06:36:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (825, 2, '1.16.4', '2022-12-15 09:21:37', '2022-12-15 09:21:37');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (826, 760, '1.16.4', '2022-12-15 10:11:30', '2022-12-15 10:11:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (827, 760, '1.16.4', '2022-12-15 10:11:30', '2022-12-15 10:11:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (828, 760, '1.16.4', '2022-12-15 10:11:30', '2022-12-15 10:11:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (829, 760, '1.16.4', '2022-12-15 10:28:30', '2022-12-15 10:28:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (830, 760, '1.16.4', '2022-12-15 10:28:30', '2022-12-15 10:28:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (831, 760, '1.16.4', '2022-12-15 10:28:30', '2022-12-15 10:28:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (832, 760, '1.16.4', '2022-12-15 10:43:55', '2022-12-15 10:43:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (833, 760, '1.16.4', '2022-12-15 11:04:36', '2022-12-15 11:04:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (834, 760, '1.16.4', '2022-12-15 11:30:42', '2022-12-15 11:30:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (835, 760, '1.16.4', '2022-12-15 11:52:18', '2022-12-15 11:52:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (836, 760, '1.16.4', '2022-12-15 12:37:13', '2022-12-15 12:37:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (837, 760, '1.16.4', '2022-12-15 13:47:42', '2022-12-15 13:47:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (838, 760, '1.16.4', '2022-12-15 13:50:17', '2022-12-15 13:50:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (839, 760, '1.16.4', '2022-12-15 13:47:30', '2022-12-15 13:47:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (840, 760, '1.16.4', '2022-12-15 13:47:57', '2022-12-15 13:47:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (841, 760, '1.16.4', '2022-12-15 13:50:21', '2022-12-15 13:50:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (842, 760, '1.16.4', '2022-12-15 14:10:22', '2022-12-15 14:10:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (843, 760, '1.16.4', '2022-12-16 07:14:15', '2022-12-16 07:14:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (844, 760, '1.16.4', '2022-12-16 07:14:31', '2022-12-16 07:14:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (845, 760, '1.16.4', '2022-12-16 07:37:25', '2022-12-16 07:37:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (846, 2, '1.16.4', '2022-12-16 10:26:19', '2022-12-16 10:26:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (847, 760, '1.16.4', '2022-12-20 07:05:39', '2022-12-20 07:05:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (848, 2, '1.16.4', '2022-12-21 04:11:41', '2022-12-21 04:11:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (849, 2, '1.16.4', '2022-12-21 04:29:08', '2022-12-21 04:29:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (850, 2, '1.16.4', '2022-12-21 17:14:03', '2022-12-21 17:14:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (851, 2, '1.16.4', '2022-12-22 07:27:45', '2022-12-22 07:27:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (852, 2, '1.16.4', '2022-12-22 08:51:36', '2022-12-22 08:51:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (853, 2, '1.16.4', '2022-12-22 09:29:33', '2022-12-22 09:29:33');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (854, 760, '1.16.4', '2022-12-22 10:14:40', '2022-12-22 10:14:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (855, 760, '1.16.4', '2022-12-22 10:54:50', '2022-12-22 10:54:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (856, 760, '1.16.4', '2022-12-22 11:19:13', '2022-12-22 11:19:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (857, 2, '1.16.4', '2022-12-23 07:02:01', '2022-12-23 07:02:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (858, 2, '1.16.4', '2022-12-23 07:17:15', '2022-12-23 07:17:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (859, 2, '1.16.4', '2022-12-23 07:33:50', '2022-12-23 07:33:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (860, 760, '1.16.4', '2022-12-23 07:53:48', '2022-12-23 07:53:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (861, 2, '1.16.4', '2022-12-23 08:02:24', '2022-12-23 08:02:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (862, 760, '1.16.4', '2022-12-23 08:09:38', '2022-12-23 08:09:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (863, 2, '1.16.4', '2022-12-23 09:05:42', '2022-12-23 09:05:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (864, 2, '1.16.4', '2022-12-23 09:21:02', '2022-12-23 09:21:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (865, 760, '1.16.4', '2022-12-23 09:24:44', '2022-12-23 09:24:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (866, 2, '1.16.4', '2022-12-23 09:36:39', '2022-12-23 09:36:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (867, 760, '1.16.4', '2022-12-23 09:41:03', '2022-12-23 09:41:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (868, 760, '1.16.4', '2022-12-23 10:11:29', '2022-12-23 10:11:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (869, 760, '1.16.4', '2022-12-23 18:29:20', '2022-12-23 18:29:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (870, 760, '1.16.4', '2022-12-26 07:23:59', '2022-12-26 07:23:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (871, 795, '1.16.4', '2022-12-26 07:27:39', '2022-12-26 07:27:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (872, 796, '1.16.4', '2022-12-26 11:24:17', '2022-12-26 11:24:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (873, 2, '1.16.4', '2022-12-27 07:02:57', '2022-12-27 07:02:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (874, 2, '1.16.4', '2022-12-27 07:38:44', '2022-12-27 07:38:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (875, 796, '1.16.4', '2022-12-27 07:54:30', '2022-12-27 07:54:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (876, 2, '1.16.4', '2022-12-27 08:07:19', '2022-12-27 08:07:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (877, 2, '1.16.4', '2022-12-27 08:35:55', '2022-12-27 08:35:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (878, 796, '1.16.4', '2022-12-27 12:40:41', '2022-12-27 12:40:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (879, 796, '1.16.4', '2022-12-28 04:37:47', '2022-12-28 04:37:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (880, 796, '1.16.4', '2022-12-28 04:59:38', '2022-12-28 04:59:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (881, 796, '1.16.4', '2022-12-28 05:23:22', '2022-12-28 05:23:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (882, 796, '1.16.4', '2022-12-28 06:11:32', '2022-12-28 06:11:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (883, 128, '1.16.1', '2022-12-28 08:10:08', '2022-12-28 08:10:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (884, 128, '1.16.1', '2022-12-28 08:29:54', '2022-12-28 08:29:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (885, 796, '1.16.4', '2022-12-28 10:09:04', '2022-12-28 10:09:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (886, 796, '1.16.4', '2022-12-28 10:58:56', '2022-12-28 10:58:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (887, 796, '1.16.4', '2022-12-28 11:49:00', '2022-12-28 11:49:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (888, 796, '1.16.4', '2022-12-28 12:06:57', '2022-12-28 12:06:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (889, 2, '1.16.4', '2022-12-28 12:54:12', '2022-12-28 12:54:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (890, 796, '1.16.4', '2022-12-28 12:55:44', '2022-12-28 12:55:44');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (891, 796, '1.16.4', '2022-12-29 05:28:45', '2022-12-29 05:28:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (892, 796, '1.16.4', '2022-12-29 06:09:22', '2022-12-29 06:09:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (893, 796, '1.16.4', '2022-12-29 06:48:14', '2022-12-29 06:48:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (894, 797, '0.3.6', '2022-12-29 07:00:43', '2022-12-29 07:00:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (895, 798, '0.3.6', '2022-12-29 07:01:23', '2022-12-29 07:01:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (896, 798, '0.3.6', '2022-12-29 15:06:55', '2022-12-29 15:06:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (897, 799, '0.3.6', '2022-12-29 15:08:09', '2022-12-29 15:08:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (898, 799, '0.3.6', '2022-12-29 15:23:43', '2022-12-29 15:23:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (899, 799, '0.3.6', '2022-12-29 17:04:56', '2022-12-29 17:04:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (900, 799, '0.3.6', '2022-12-30 07:42:28', '2022-12-30 07:42:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (901, 2, '1.16.4', '2022-12-30 07:43:48', '2022-12-30 07:43:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (902, 2, '1.16.4', '2022-12-30 09:24:20', '2022-12-30 09:24:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (903, 799, '0.3.6', '2022-12-30 12:19:13', '2022-12-30 12:19:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (904, 2, '1.16.4', '2022-12-30 12:28:40', '2022-12-30 12:28:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (905, 14, '1.16.4', '2022-12-30 12:29:29', '2022-12-30 12:29:29');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (906, 14, '1.16.4', '2022-12-30 12:48:06', '2022-12-30 12:48:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (907, 2, '1.16.4', '2022-12-30 12:51:38', '2022-12-30 12:51:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (908, 799, '0.3.6', '2022-12-30 12:54:01', '2022-12-30 12:54:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (909, 19, '1.16.4', '2022-12-30 13:05:57', '2022-12-30 13:05:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (910, 799, '1.16.1', '2022-12-30 13:21:13', '2022-12-30 13:21:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (911, 799, '1.16.1', '2022-12-30 15:32:45', '2022-12-30 15:32:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (912, 799, '1.16.1', '2022-12-30 16:05:07', '2022-12-30 16:05:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (913, 799, '1.16.1', '2022-12-30 16:24:58', '2022-12-30 16:24:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (914, 799, '1.16.1', '2022-12-30 16:53:43', '2022-12-30 16:53:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (915, 799, '1.16.1', '2022-12-30 17:36:50', '2022-12-30 17:36:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (916, 799, '1.16.1', '2022-12-30 18:08:50', '2022-12-30 18:08:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (917, 799, '1.16.1', '2022-12-31 14:01:50', '2022-12-31 14:01:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (918, 799, '1.16.1', '2023-01-02 04:59:32', '2023-01-02 04:59:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (919, 799, '1.16.1', '2023-01-02 07:34:54', '2023-01-02 07:34:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (920, 128, '1.16.1', '2023-01-02 21:10:59', '2023-01-02 21:10:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (921, 799, '1.16.1', '2023-01-03 03:26:46', '2023-01-03 03:26:46');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (922, 799, '1.16.1', '2023-01-03 04:23:31', '2023-01-03 04:23:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (923, 799, '1.16.1', '2023-01-03 05:22:15', '2023-01-03 05:22:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (924, 799, '1.16.1', '2023-01-03 05:54:23', '2023-01-03 05:54:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (925, 799, '1.16.1', '2023-01-03 06:27:01', '2023-01-03 06:27:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (926, 799, '1.16.1', '2023-01-03 07:11:19', '2023-01-03 07:11:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (927, 19, '1.16.4', '2023-01-03 07:11:23', '2023-01-03 07:11:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (928, 19, '1.16.4', '2023-01-03 07:46:23', '2023-01-03 07:46:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (929, 19, '1.16.4', '2023-01-03 08:03:26', '2023-01-03 08:03:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (930, 799, '1.16.1', '2023-01-03 08:30:16', '2023-01-03 08:30:16');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (931, 799, '1.17.1', '2023-01-03 10:04:32', '2023-01-03 10:04:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (932, 799, '1.17.1', '2023-01-03 10:53:23', '2023-01-03 10:53:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (933, 799, '1.17.0', '2023-01-03 11:10:14', '2023-01-03 11:10:14');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (934, 799, '1.17.0', '2023-01-03 12:29:41', '2023-01-03 12:29:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (935, 799, '1.12.6', '2023-01-03 12:45:41', '2023-01-03 12:45:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (936, 799, '1.17.1', '2023-01-03 16:09:34', '2023-01-03 16:09:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (937, 19, '1.16.4', '2023-01-04 06:56:38', '2023-01-04 06:56:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (938, 19, '1.16.4', '2023-01-04 07:13:10', '2023-01-04 07:13:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (939, 799, '1.17.1', '2023-01-04 07:18:21', '2023-01-04 07:18:21');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (940, 19, '1.16.4', '2023-01-04 07:39:58', '2023-01-04 07:39:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (941, 19, '1.16.4', '2023-01-04 08:08:53', '2023-01-04 08:08:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (942, 19, '1.16.4', '2023-01-04 16:24:24', '2023-01-04 16:24:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (943, 799, '1.12.6', '2023-01-04 18:09:15', '2023-01-04 18:09:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (944, 799, '1.12.6', '2023-01-05 04:38:43', '2023-01-05 04:38:43');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (945, 799, '1.17.1', '2023-01-05 04:54:04', '2023-01-05 04:54:04');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (946, 799, '1.17.0', '2023-01-05 06:58:42', '2023-01-05 06:58:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (947, 799, '1.17.1', '2023-01-05 08:13:57', '2023-01-05 08:13:57');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (948, 799, '1.17.1', '2023-01-05 10:51:30', '2023-01-05 10:51:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (949, 799, '1.17.1', '2023-01-05 11:24:08', '2023-01-05 11:24:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (950, 19, '1.16.3', '2023-01-05 13:38:47', '2023-01-05 13:38:47');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (951, 19, '1.11.0', '2023-01-05 13:53:54', '2023-01-05 13:53:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (952, 799, '1.17.1', '2023-01-06 05:23:20', '2023-01-06 05:23:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (953, 799, '1.17.1', '2023-01-06 06:31:23', '2023-01-06 06:31:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (954, 19, '1.10.0', '2023-01-06 06:32:51', '2023-01-06 06:32:51');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (955, 799, '1.17.1', '2023-01-06 07:17:34', '2023-01-06 07:17:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (956, 125, '1.10.0', '2023-01-06 07:32:22', '2023-01-06 07:32:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (957, 799, '1.17.1', '2023-01-06 08:43:36', '2023-01-06 08:43:36');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (958, 799, '1.17.1', '2023-01-06 09:00:19', '2023-01-06 09:00:19');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (959, 799, '1.17.1', '2023-01-06 09:19:52', '2023-01-06 09:19:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (960, 799, '1.17.1', '2023-01-06 09:34:54', '2023-01-06 09:34:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (961, 799, '1.17.1', '2023-01-06 10:20:12', '2023-01-06 10:20:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (962, 799, '1.17.1', '2023-01-06 10:40:07', '2023-01-06 10:40:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (963, 799, '1.17.1', '2023-01-06 10:55:08', '2023-01-06 10:55:08');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (964, 125, '1.10.0', '2023-01-06 13:46:02', '2023-01-06 13:46:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (965, 125, '1.11.0', '2023-01-09 08:40:59', '2023-01-09 08:40:59');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (966, 125, '1.13.0', '2023-01-09 08:56:30', '2023-01-09 08:56:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (967, 800, '1.16.3', '2023-01-09 09:06:54', '2023-01-09 09:06:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (968, 799, '1.17.1', '2023-01-09 09:11:02', '2023-01-09 09:11:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (969, 801, '1.16.3', '2023-01-09 09:17:18', '2023-01-09 09:17:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (970, 799, '1.17.1', '2023-01-09 09:44:03', '2023-01-09 09:44:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (971, 802, '1.16.3', '2023-01-09 09:53:28', '2023-01-09 09:53:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (972, 799, '1.17.1', '2023-01-09 10:04:48', '2023-01-09 10:04:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (973, 799, '1.17.1', '2023-01-09 11:05:02', '2023-01-09 11:05:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (974, 799, '1.17.1', '2023-01-09 15:02:42', '2023-01-09 15:02:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (975, 799, '1.17.1', '2023-01-09 16:06:39', '2023-01-09 16:06:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (976, 799, '1.17.1', '2023-01-09 16:33:17', '2023-01-09 16:33:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (977, 799, '1.17.1', '2023-01-09 16:48:32', '2023-01-09 16:48:32');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (978, 799, '1.17.1', '2023-01-09 18:02:26', '2023-01-09 18:02:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (979, 799, '1.17.1', '2023-01-10 04:10:40', '2023-01-10 04:10:40');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (980, 799, '1.17.1', '2023-01-10 06:06:39', '2023-01-10 06:06:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (981, 799, '1.17.1', '2023-01-10 06:22:12', '2023-01-10 06:22:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (982, 128, '1.16.1', '2023-01-10 17:57:12', '2023-01-10 17:57:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (983, 802, '1.17.0', '2023-01-12 11:00:45', '2023-01-12 11:00:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (984, 808, '1.10.0', '2023-01-12 11:01:58', '2023-01-12 11:01:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (985, 808, '1.15.0', '2023-01-12 11:56:17', '2023-01-12 11:56:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (986, 814, '1.17.0', '2023-01-13 05:38:48', '2023-01-13 05:38:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (987, 799, '1.17.1', '2023-01-13 17:38:23', '2023-01-13 17:38:23');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (988, 799, '1.17.1', '2023-01-13 17:54:17', '2023-01-13 17:54:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (989, 799, '1.17.1', '2023-01-13 18:09:26', '2023-01-13 18:09:26');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (990, 799, '1.17.1', '2023-01-16 06:36:53', '2023-01-16 06:36:53');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (991, 799, '1.17.1', '2023-01-16 06:53:52', '2023-01-16 06:53:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (992, 799, '1.17.1', '2023-01-17 04:27:03', '2023-01-17 04:27:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (993, 799, '1.17.1', '2023-01-17 04:42:09', '2023-01-17 04:42:09');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (994, 799, '1.17.1', '2023-01-17 08:15:06', '2023-01-17 08:15:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (995, 799, '1.17.1', '2023-01-17 10:51:38', '2023-01-17 10:51:38');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (996, 799, '1.17.1', '2023-01-17 11:09:56', '2023-01-17 11:09:56');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (997, 814, '1.17.0', '2023-01-18 03:47:12', '2023-01-18 03:47:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (998, 814, '1.17.0', '2023-01-18 04:11:18', '2023-01-18 04:11:18');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (999, 799, '1.17.1', '2023-01-18 05:18:10', '2023-01-18 05:18:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1000, 799, '1.17.1', '2023-01-18 06:31:24', '2023-01-18 06:31:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1001, 799, '1.17.1', '2023-01-18 07:24:30', '2023-01-18 07:24:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1002, 799, '1.17.1', '2023-01-18 10:41:10', '2023-01-18 10:41:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1003, 799, '1.17.1', '2023-01-18 10:56:27', '2023-01-18 10:56:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1004, 815, '1.16.1', '2023-01-18 22:38:42', '2023-01-18 22:38:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1005, 799, '1.17.1', '2023-01-19 03:20:07', '2023-01-19 03:20:07');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1006, 815, '1.16.1', '2023-01-19 18:42:30', '2023-01-19 18:42:30');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1007, 799, '1.17.1', '2023-01-20 02:37:28', '2023-01-20 02:37:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1008, 799, '1.17.1', '2023-01-20 03:31:48', '2023-01-20 03:31:48');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1009, 799, '1.17.1', '2023-01-20 03:49:02', '2023-01-20 03:49:02');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1010, 799, '1.17.1', '2023-01-20 04:17:17', '2023-01-20 04:17:17');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1011, 799, '1.17.1', '2023-01-20 04:35:55', '2023-01-20 04:35:55');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1012, 799, '1.17.1', '2023-01-20 06:32:20', '2023-01-20 06:32:20');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1013, 799, '1.17.1', '2023-01-20 09:34:42', '2023-01-20 09:34:42');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1014, 799, '1.17.1', '2023-01-20 14:40:54', '2023-01-20 14:40:54');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1015, 799, '1.17.1', '2023-01-20 15:28:06', '2023-01-20 15:28:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1016, 799, '1.17.1', '2023-01-21 02:09:00', '2023-01-21 02:09:00');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1017, 799, '1.17.1', '2023-01-21 02:28:34', '2023-01-21 02:28:34');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1018, 799, '1.17.1', '2023-01-22 10:37:50', '2023-01-22 10:37:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1019, 1, '1.17.0', '2023-01-23 04:10:41', '2023-01-23 04:10:41');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1020, 799, '1.17.1', '2023-01-23 06:17:49', '2023-01-23 06:17:49');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1021, 799, '1.17.1', '2023-01-23 06:55:24', '2023-01-23 06:55:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1022, 125, '1.17.0', '2023-01-23 11:05:27', '2023-01-23 11:05:27');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1023, 456, '1.17.0', '2023-01-23 11:15:11', '2023-01-23 11:15:11');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1024, 1, '1.17.0', '2023-01-23 11:17:50', '2023-01-23 11:17:50');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1025, 1, '1.17.0', '2023-01-23 12:13:12', '2023-01-23 12:13:12');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1144, 799, '1.17.1', '2023-01-24 07:43:13', '2023-01-24 07:43:13');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1145, 799, '1.17.1', '2023-01-24 08:07:06', '2023-01-24 08:07:06');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1146, 1, '1.17.0', '2023-01-24 09:31:25', '2023-01-24 09:31:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1174, 1, '1.17.0', '2023-01-24 10:37:31', '2023-01-24 10:37:31');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1175, 799, '1.17.1', '2023-01-24 10:42:01', '2023-01-24 10:42:01');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1188, 1, '1.17.0', '2023-01-24 10:53:22', '2023-01-24 10:53:22');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1195, 1, '1.17.3', '2023-01-24 11:17:28', '2023-01-24 11:17:28');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1196, 1, '1.17.3', '2023-01-24 11:36:24', '2023-01-24 11:36:24');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1197, 799, '1.17.1', '2023-01-24 11:39:05', '2023-01-24 11:39:05');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1198, 1, '1.17.3', '2023-01-24 12:09:52', '2023-01-24 12:09:52');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1199, 799, '1.17.1', '2023-01-24 12:10:25', '2023-01-24 12:10:25');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1200, 799, '1.17.1', '2023-01-24 12:31:03', '2023-01-24 12:31:03');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1201, 1, '1.17.3', '2023-01-24 12:37:58', '2023-01-24 12:37:58');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1202, 799, '1.17.1', '2023-01-24 12:47:39', '2023-01-24 12:47:39');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1203, 1, '1.17.3', '2023-01-24 12:57:10', '2023-01-24 12:57:10');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1204, 799, '1.17.1', '2023-01-24 13:03:45', '2023-01-24 13:03:45');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1205, 799, '1.17.1', '2023-01-24 15:20:15', '2023-01-24 15:20:15');
INSERT IGNORE INTO circle_api_development.user_token_usages (id, user_token_id, app_version, created_at, updated_at)
VALUES (1303, 799, '1.17.1', '2023-01-25 06:49:43', '2023-01-25 06:49:43');