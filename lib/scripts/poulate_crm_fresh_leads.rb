# frozen_string_literal: true

json_data = [{"user_id":274602,"lead_type":"RLT_Outbound"}]

json_data.each do |data|
  premium_pitch = User.find(data[:user_id])&.premium_pitch

  if premium_pitch.blank?
    premium_pitch = PremiumPitch.create(user_id: data[:user_id], lead_type: data[:lead_type])
  end

  premium_pitch.shown_interest! if premium_pitch.may_shown_interest?
  Floww::AddLead.perform_async(data[:user_id], data[:lead_type])
end;0
