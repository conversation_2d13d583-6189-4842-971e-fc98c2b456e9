require 'csv'

premium_pitch_absent_users = []
count = 0
users_with_crm_stage = CSV.parse(File.read('./lib/scripts/crm_stage_data.csv'), headers: true)
users_with_crm_stage.each do |crm_stage_data|
  user_id = crm_stage_data["user_id"].to_i
  crm_stage = crm_stage_data["crm_stage"]
  pp = PremiumPitch.find_by(user_id: user_id)
  if pp.blank?
    premium_pitch_absent_users << user_id
    next
  else
    if pp.crm_stage.blank?
      pp.update(crm_stage: crm_stage)
      SyncMixpanelUser.perform_async(user_id)
      count += 1
    end
  end
  sleep(0.5) if count % 300 == 0
end
puts "Processed #{count} users"
