#Few users don't have location circles so we populated location circles based on their village Id
users_ids_without_location_circles = User.where.not(id: UserCircle.joins(:circle)
                                                                  .where(circles: { circle_type: 0 })
                                                                  .pluck(:user_id))
                                         .active.pluck(:id)

users_ids_without_location_circles.each do |uid|
  user = User.find uid
  user.circles << Circle.find(user.village_id) if user.village_id.present?
end
