require 'csv'
data = CSV.parse(File.read('./lib/scripts/corporations_and_mla_constituencies.csv'), headers: true)

mla_and_madal_data = []
data.each do |list|
  corporation_id = list["corporation_id"].to_i
  mla_constituency_ids = list["mla_constituency_ids"].split(',').map(&:strip).map(&:to_i)
  if corporation_id.present?
    corporation = Circle.find_by(id: corporation_id)
    mandal_id = corporation.parent_circle_id
  end
  if mla_constituency_ids.present? && mandal_id.present?
    mla_and_madal_data << { mla_constituency_ids: mla_constituency_ids, mandal_id: mandal_id }
  end
end

mla_and_madal_data.each do |data|
  mla_constituency_ids = data[:mla_constituency_ids]
  mandal_id = data[:mandal_id]
  if mla_constituency_ids.present? && mandal_id.present?
    mla_constituency_ids.each do |mla_constituency_id|
      CirclesRelation.where(first_circle_id: mandal_id, second_circle_id: mla_constituency_id, relation: "Mandal2MLA").first_or_create
    end
  end
end
