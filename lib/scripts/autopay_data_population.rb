# frozen_string_literal: true

status_frames_counts = [0, 1, 2, 3, 4]
status_frames_counts.each do |status_frame_count|
  # 1 month
  total_amount = 299 + 100 * status_frame_count
  discount_amount = 0
  p = Plan.new(name: "Praja Premium - Monthly",
               total_amount: total_amount,
               discount_amount: discount_amount,
               amount: total_amount - discount_amount,
               duration_in_months: 1)
  p.plan_products.build(product_id: Constants.get_poster_product_id, quantity: 1)
  p.plan_products.build(product_id: Constants.get_premium_frame_product_id, quantity: 10)
  p.plan_products.build(product_id: Constants.get_status_frame_product_id, quantity: status_frame_count)
  p.save

  # 6 months
  total_amount = 2999 + 600 * status_frame_count
  discount_amount = 1000
  p = Plan.new(name: "Praja Premium - Half yearly",
               total_amount: total_amount,
               discount_amount: discount_amount,
               amount: total_amount - discount_amount,
               duration_in_months: 6)
  p.plan_products.build(product_id: Constants.get_poster_product_id, quantity: 1)
  p.plan_products.build(product_id: Constants.get_premium_frame_product_id, quantity: 10)
  p.plan_products.build(product_id: Constants.get_status_frame_product_id, quantity: status_frame_count)
  p.save

  # 12 months
  total_amount = 3599 + 1000 * status_frame_count
  discount_amount = 600
  p = Plan.new(name: "Praja Premium - Yearly",
               total_amount: total_amount,
               discount_amount: discount_amount,
               amount: total_amount - discount_amount,
               duration_in_months: 12)
  p.plan_products.build(product_id: Constants.get_poster_product_id, quantity: 1)
  p.plan_products.build(product_id: Constants.get_premium_frame_product_id, quantity: 10)
  p.plan_products.build(product_id: Constants.get_status_frame_product_id, quantity: status_frame_count)
  p.save
end

migrated_user_ids = (UserPlan.pluck(:user_id) + UserMetadatum.where(key: Constants.yet_to_start_trial_key).pluck(:user_id)).uniq
no_order_user_ids = []
no_eligible_plan_user_ids = []
blank_frames_user_ids = []
user_plan_creation_failed_user_ids = []
upl = nil
user = nil

UserPosterLayout.where(entity_type: "User", status: :active).where.not(entity_id: migrated_user_ids).find_each(batch_size: 1000) do |upl|
  user = upl.entity
  next if migrated_user_ids.include?(user.id)

  ################ START - Migrate user frames ################
  is_poster_subscribed = UserProductSubscription
                           .where(user_id: user.id, item_type: "Product", item_id: 1, active: true)
                           .where("start_date <= ? AND end_date >= ?", Time.zone.now, Time.zone.now)
                           .exists?
  has_ever_subscribed = UserProductSubscription
                          .where(user_id: user.id, item_type: "Product", item_id: 1, active: true)
                          .exists?
  if is_poster_subscribed
    last_order = user.orders.joins(:order_items).where(order_items: { item_type: "Product" }).successful.last

    if last_order.blank?
      no_order_user_ids << user.id
      next
    end

    frame_ids = UserProductSubscription.where("end_date > ?", Time.zone.now).where(user_id: user.id, item_type: "Frame").pluck(:item_id)
  elsif has_ever_subscribed
    last_order = user.orders.joins(:order_items).where(order_items: { item_type: "Product" }).successful.last
    if last_order.blank?
      no_order_user_ids << user.id
      next
    end

    frame_ids = last_order.order_items.where(item_type: "Frame").pluck(:item_id)
  else
    last_order = user.orders.last
    if last_order.blank?
      no_order_user_ids << user.id
      next
    end

    frame_ids = last_order.order_items.where(item_type: "Frame").pluck(:item_id)
  end

  if frame_ids.blank?
    blank_frames_user_ids << user.id
    next
  end

  user_frames = []
  frame_ids.each do |frame_id|
    user_frames << { user_id: user.id, frame_id: frame_id, status: :active }
  end
  UserFrame.insert_all(user_frames)

  ################ END - Migrate user frames ################

  ################ START - Populate user plans ################
  last_ups = UserProductSubscription.where(user_id: user.id, item_type: "Product", item_id: Constants.get_poster_product_id).last
  trial_end_date = Metadatum.get_trial_end_date_incld_extensions(user_id: user.id)
  user_plan_end_date = if last_ups.present?
                         last_ups.end_date.end_of_day
                       elsif trial_end_date
                         Time.zone.parse(trial_end_date.to_s + " 23:59:59").end_of_day
                       end

  if user_plan_end_date.present?
    duration_in_months = last_order.order_items.where(item_type: "Product").first.duration_in_months
    frame_type_counts = UserFrame.frame_type_counts(user.id)
    premium_frames_count = 10
    status_frames_count = frame_type_counts['status'] || 0
    user_eligible_plan = Plan.joins(:plan_products)
                             .group('plans.id')
                             .where(duration_in_months: duration_in_months)
                             .having('SUM(CASE WHEN plan_products.product_id = ? THEN plan_products.quantity ELSE 0 END) = ?',
                                     Constants.get_premium_frame_product_id, premium_frames_count)
                             .having('SUM(CASE WHEN plan_products.product_id = ? THEN plan_products.quantity ELSE 0 END) = ?',
                                     Constants.get_status_frame_product_id, status_frames_count)
                             .last
    if user_eligible_plan.blank?
      no_eligible_plan_user_ids << user.id
      next
    end

    begin
      UserPlan.create!(user_id: user.id,
                       plan_id: user_eligible_plan.id,
                       amount: user_eligible_plan.amount,
                       end_date: user_plan_end_date)
    rescue
      user_plan_creation_failed_user_ids << user.id
      next
    end
  else
    Metadatum.where(entity: user, key: Constants.user_poster_trial_duration_key)
             .first_or_create(value: Constants.poster_trial_default_duration)
    user.set_yet_to_start_trial_key
  end
  ################ END - Populate user plans ################

  migrated_user_ids << user.id
end

################ START - Migrate User Referral ################
failed_referral_order_ids = []
Order.where.not(referred_by: nil).where(status: :successful).find_each(batch_size: 1000) do |order|
  begin
    UserReferral.create!(user_id: order.referred_by, referred_user_id: order.user_id, status: :redeemed)
  rescue
    failed_referral_order_ids << order.id
  end
end
Order.where("referred_by IS NOT NULL AND status != 2").find_each(batch_size: 1000) do |order|
  begin
    UserReferral.create!(user_id: order.referred_by, referred_user_id: order.user_id, status: :created)
  rescue
    failed_referral_order_ids << order.id
  end
end
################ END - Migrate User Referral ################
