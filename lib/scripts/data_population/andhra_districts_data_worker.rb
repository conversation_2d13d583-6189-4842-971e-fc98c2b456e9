require 'net/http'
require_relative './andhra_get_district_mandals_worker'

class AndhraDistrictsDataWorker
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    file = File.read('app/workers/scrapers/andhra/andhra_districts.json')
    districts = JSON.parse(file)

    requested_url = 'http://www.google.com/inputtools/request?ime=transliteration_en_te&num=2&cp=0&cs=0&ie=utf-8&oe=utf-8&text='

    names = []
    districts.each do |district|
      district["name_en"] = district["name_en"].gsub("_", " ")
      name_dirty = district["name_en"]
      name_splits = name_dirty.split("#")
      name = name_splits[0]
      names << name

      district['name'] = name
      district['level'] = :district
    end

    uri = URI.parse(requested_url + names.join(","))
    g_res = Net::HTTP.get_response(uri)

    if g_res.code == "200"
      resp = JSON.parse(g_res.body)

      if resp[0] == "SUCCESS"
        count = 0
        resp[1].each_with_index do |e, index|
          if !e[1].nil? && e[1].count > 1
            districts[index]['name'] = e[1][0]
          end

          external_id = districts[index]['id']
          districts[index]['id'] = nil
          c = Circle.create(districts[index])

          if !c.nil? && c.id > 0
            count = count + 1
            AndhraGetDistrictMandalsWorker.perform_async(external_id, c.id)
          end
        end
      end
    end
  end
end