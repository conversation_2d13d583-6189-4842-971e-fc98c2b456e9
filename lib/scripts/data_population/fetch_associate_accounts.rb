class FetchAssociateAccounts
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    used_photos = []

    UserGroupMember.where(active: true).each do |user_group_member|
      similar_user_groups = UserGroup.where(name: user_group_member.user_group.name, active: true).all

      # Join all circles joined by head user of the group
      head_user = user_group_member.user_group.user
      insert_objects = head_user.circles.where("circles.circle_type = 1 AND circles.level IN (6, 7)").map { |c| {circle_id: c.id, user_id: user_group_member.user_id, created_at: Time.now, updated_at: Time.now} }
      UserCircle.insert_all(insert_objects) if insert_objects.length > 0

      # Follow all heads, who are in the same group name
      insert_objects = similar_user_groups.map { |ug| {user_id: ug.user_id, follower_id: user_group_member.user_id, created_at: Time.now, updated_at: Time.now} }
      UserFollower.insert_all(insert_objects) if insert_objects.length > 0

      # Follow this member by 10% - 20% of other users from similar groups
      all_user_group_member_ids = []
      similar_user_groups.map do |ug|
        all_user_group_member_ids += ug.user_group_members.map { |m| m.user.id }
      end
      if all_user_group_member_ids.length > 0
        all_user_group_member_ids = all_user_group_member_ids.uniq {|x| x}

        members_count = all_user_group_member_ids.length
        number_to_sample = rand((members_count * 0.1).floor..(members_count * 0.2).ceil)
        followed_by_user_ids = all_user_group_member_ids.sample(number_to_sample)

        insert_objects = followed_by_user_ids.map { |id| {user_id: user_group_member.user_id, follower_id: id, created_at: Time.now, updated_at: Time.now} }
        UserFollower.insert_all(insert_objects) if insert_objects.length > 0
      end

      head_user_party_circle = head_user.circles.where("circles.circle_type = 1 AND circles.level = 6").first
      u = user_group_member.user

      # add profile pic
      if !head_user_party_circle.nil? && u.photo.nil? && u.id % 3 != 0
        error = false
        photo_file_name = ""
        Dir.chdir("app/workers/associate_account_photos") do
          Dir.glob('*').select do |f|
            if File.directory? f
              circle_id = f.to_s
              circle_id = circle_id.gsub("c_", "").to_i
              if circle_id == head_user_party_circle.id
                available_photos = []
                used_photos_index = used_photos.index(used_photos.find { |l| l[:circle_id] == circle_id })
                if used_photos_index.nil? || used_photos_index == -1
                  used_photos_index = used_photos.count
                  used_photos << {
                      circle_id: circle_id,
                      photos: []
                  }
                end

                Dir.chdir("c_#{circle_id}") do
                  Dir.glob('*').select do |pf|
                    unless used_photos[used_photos_index][:photos].include? pf.to_s
                      available_photos << pf.to_s
                    end
                  end
                end

                if available_photos.length <= 0
                  used_photos[used_photos_index][:photos] = []

                  Dir.chdir("c_#{circle_id}") do
                    Dir.glob('*').select do |f|
                      available_photos << f.to_s
                    end
                  end
                end

                if available_photos.length > 0
                  used_photo = available_photos.sample
                  photo_file_name = "#{circle_id}/#{used_photo}"
                  used_photos[used_photos_index][:photos] << used_photo
                else
                  error = true
                  photo_file_name = ""
                  logger.error("PHOTO FILE NAME NOT FOUND")
                  break
                end
              end
            end
          end
        end

        if !error && photo_file_name != ""
          url = "https://cdn.thecircleapp.in/production/associate-accounts-photos/#{photo_file_name}"
          photo = Photo.where(url: url, active: true).first
          if photo.nil?
            if Rails.env.development?
              user = User.first
            else
              user = User.find_by_phone(**********)
            end
            photo = Photo.create(user: user, url: url, service: :aws)
            SavePhotoDimensionsWorker.perform_in(2.seconds, photo.id)
          end

          u.photo = photo
          u.save
        end
      end
    end
  end
end