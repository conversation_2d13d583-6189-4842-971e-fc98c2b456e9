class HcDataCsv
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    districts = []

    csv_row_data = CSV.read(
        "app/workers/ap_master.csv",
        {headers: :first_row, skip_blanks: true}
    )

    dup_count = 0
    village_count = 0
    mandal_count = 0
    district_count = 0

    csv_row_data.each do |row|
      # if row["DISTRICT EN"] == "Khammam" || row["DISTRICT EN"] == "Bhadradri Kothagudem"
      if row["MANDAL EN"] == "Other" || row["DISTRICT EN"] == "Other" || row["VILLAGE EN"] == "Other"
        next
      end

      district_index = districts.index(districts.find { |l| l[:name_en] == row["DISTRICT EN"] })
      if district_index.nil? || district_index == -1
        district = {
            name: row["DISTRICT"],
            name_en: row["DISTRICT EN"],
            type: 'district',
            children: []
        }

        district_index = districts.count

        districts << district
        district_count = district_count + 1
      end

      mandal_index = districts[district_index][:children].index(districts[district_index][:children].find { |l| l[:name_en] == row["MANDAL EN"] })
      if mandal_index.nil? || mandal_index == -1
        mandal = {
            name: row["MANDAL"],
            name_en: row["MANDAL EN"],
            type: 'mandal',
            children: []
        }

        mandal_index = districts[district_index][:children].count

        districts[district_index][:children] << mandal
        mandal_count = mandal_count + 1
      end

      village_index = districts[district_index][:children][mandal_index][:children].index(districts[district_index][:children][mandal_index][:children].find { |l| l[:name_en] == row["VILLAGE EN"] })
      if village_index.nil? || village_index == -1
        village = {
            name: row["VILLAGE"],
            name_en: row["VILLAGE EN"],
            type: 'village'
        }

        village_index = districts[district_index][:children][mandal_index][:children].count

        districts[district_index][:children][mandal_index][:children] << village
        village_count = village_count + 1
      else
        dup_count = dup_count + 1
        puts districts[district_index][:name_en].to_s + " - " + districts[district_index][:children][mandal_index][:name_en].to_s + " - " + districts[district_index][:children][mandal_index][:children][village_index][:name_en]
      end
    end

    count = 0
    districts.each do |d|
      count = count + 1
      d[:children].each do |m|
        count = count + 1
        count = count + m[:children].count
      end
    end

    send = false
    byebug

    if send
      districts.each do |d|
        AddCirclesData.perform_async(d)
      end
    end
  end
end