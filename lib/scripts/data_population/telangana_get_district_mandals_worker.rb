require 'net/http'
require_relative './telangana_get_mandal_villages_worker'

class TelanganaGetDistrictMandalsWorker
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    district_id = args[0].to_i
    id = args[1].to_i

    requested_url = 'http://elecroll.tsec.gov.in/gpWardWiseElecrolls.do?mode=getMandal&district_id=' +
        district_id.to_s.rjust(2, '0')

    uri = URI.parse(requested_url)
    res = Net::HTTP.post_form(uri, {})

    body = res.body.to_s

    if res.code == "200"
      body = body.gsub("<option value=\"", "{\"id\": \"")
      body = body.gsub("\">", "\", \"name_en\": \"")
      body = body.gsub("</option>", "\"},")
      body = body.gsub("\n", "")
      body = body.chomp(",")
      body = "["+body+"]"

      mandals = JSON.parse(body)
      mandals.shift

      names = []
      mandals.each do |mandal|
        mandal["name_en"] = mandal["name_en"].gsub("_", " ")
        name_dirty = mandal["name_en"]
        name_splits = name_dirty.split("#")
        name = name_splits[0]
        names << name

        mandal['name'] = name
        mandal['parent_circle_id'] = id
        mandal['level'] = :mandal
      end

      requested_url = 'http://www.google.com/inputtools/request?ime=transliteration_en_te&num=2&cp=0&cs=0&ie=utf-8&oe=utf-8&text='
      uri = URI.parse(requested_url + names.join(","))
      res = Net::HTTP.get_response(uri)

      if res.code == "200"
        resp = JSON.parse(res.body)

        if resp[0] == "SUCCESS"
          resp[1].each_with_index do |e, index|
            if !e[1].nil? && e[1].count > 1
              mandals[index]['name'] = e[1][0]
            end

            mandal_id = mandals[index]['id']
            mandals[index]['id'] = nil
            c = Circle.create(mandals[index])

            if !c.nil? && c.id > 0
              TelanganaGetMandalVillagesWorker.perform_async(district_id, mandal_id, c.id)
            end
          end

          ## OTHERS
        end
      end
    end
  end
end
