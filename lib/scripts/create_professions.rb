RAJAKIYA_NAYAKULU = 'రాజకీయ నాయకులు'
SVACHCHANDA_SAMSTHA_NAYAKULU = 'స్వచ్ఛంద సంస్థ నాయకులు'
PRESS = 'ప్రెస్స్'
PRABHUTHVA_ADHIKARI = 'ప్రభుత్వ అధికారి'
TOURS_AND_TRAVELS = 'టూర్స్ & ట్రావెల్స్'
REAL_ESTATE = 'రియల్ ఎస్టేట్'
VAIDYA_RANGAM = 'వైద్య రంగం'
VYAVASAYAM = 'వ్యవసాయం'
MULTI_MEDIA = 'మల్టీ మీడియా'
FINANCE_OR_INSURANCE = 'ఫైనాన్స్ / ఇన్సూరెన్స్'
SHOPTS_OR_SEVALU = 'షాప్స్ / సేవలు'
CHADUVU = 'చదువు'
ITHARA = 'ఇతర'

PRAJA_PRATHINIDI = 'ప్రజా ప్రతినిధి'
PARTY_NAYAKULU = 'పార్టీ నాయకులు'
PARTY_KARYAKARTHA = 'పార్టీ కార్యకర్త'

SANGHA_NAYAKULU = 'సంఘ నాయకులు'
UNION_NAYAKULU = 'యూనియన్ నాయకులు'

JARNALIST = 'జర్నలిస్ట్'
REPORTER = 'రిపోర్టర్'
CAMERA_OPERATOR = 'కెమెరా ఆపరేటర్'
EDITOR = 'ఎడిటర్'
NEWS_ANCHOR = 'న్యూస్ యాంకర్'

POLICE = 'పోలీసు'
UPADHYAYULU = 'ఉపాధ్యాయులు'
REVENUE_SHAAKHA_ADHIKARI = 'రెవెన్యూ శాఖ అధికారి'
DAAKTORS = 'డాక్టర్స్'
BANK_UDYOGULU = 'బ్యాంక్ ఉద్యోగులు'

NARS = 'నర్స్'
NIRVAAHAKULU = 'నిర్వాహకులు'
MEDICAL_SHOP = 'మెడికల్ షాప్'

GRAPHIC_DESIGNERS = 'గ్రాఫిక్ డిసైనర్స్'
VIDEO_EDITOR = 'వీడియో ఎడిటర్'
VISUAL_EFFECTS_ANIMATION = 'విజ్యువల్ ఎఫెక్ట్స్ & ఆనిమేషన్'

ACCOUNTANT = 'అకౌంటెంట్'
AADITOR = 'ఆడిటర్'
INSURANCE_AGENT = 'ఇన్సూరెన్స్ ఏజెంట్'

KIRAANA_SHOP = 'కిరాణా షాప్'
BATTALA_VYAPARAM = 'బట్టల వ్యాపారం'
CELL_POINT = 'సెల్ పాయింట్'
RESTAARENT = 'రెస్టారెంట్'
AUTO_MOBILES = 'ఆటోమొబైల్స్'

VIDYARDHI = 'విద్యార్థి'

political_leader = Profession.create(name: RAJAKIYA_NAYAKULU, name_en: 'Political Leader', admin_medium_id: 163179)
independent_organisation_leader = Profession.create(name: SVACHCHANDA_SAMSTHA_NAYAKULU, name_en: 'Independent Organisation Leader', admin_medium_id: 164332)
press = Profession.create(name: PRESS, name_en: 'Press', admin_medium_id: 162203)
government_officer = Profession.create(name: PRABHUTHVA_ADHIKARI, name_en: 'Government Officer', admin_medium_id: 162204)
tours_and_travels = Profession.create(name: TOURS_AND_TRAVELS, name_en: 'Tours and Travels', admin_medium_id: 162205)
real_estate = Profession.create(name: REAL_ESTATE, name_en: 'Real Estate', admin_medium_id: 162206)
medicine = Profession.create(name: VAIDYA_RANGAM, name_en: 'Medicine', admin_medium_id: 163181)
agriculture = Profession.create(name: VYAVASAYAM, name_en: 'Agriculture', admin_medium_id: 163190)
multi_media = Profession.create(name: MULTI_MEDIA, name_en: 'Multi Media', admin_medium_id: 163191)
finance_or_insurance = Profession.create(name: FINANCE_OR_INSURANCE, name_en: 'Finance or Insurance', admin_medium_id: 163200)
shops_or_services = Profession.create(name: SHOPTS_OR_SEVALU, name_en: 'Shops or Services', admin_medium_id: 163205)
education = Profession.create(name: CHADUVU, name_en: 'Education', admin_medium_id: 163208)
others = Profession.create(name: ITHARA, name_en: 'Others', admin_medium_id: 163209)

SubProfession.create(name: PRAJA_PRATHINIDI, name_en: 'Praja Prathinidi', profession: political_leader)
SubProfession.create(name: PARTY_NAYAKULU, name_en: 'Party Nayakulu', profession: political_leader)
SubProfession.create(name: PARTY_KARYAKARTHA, name_en: 'Party Karyakartha', profession: political_leader)
SubProfession.create(name: SANGHA_NAYAKULU, name_en: 'Community Leader', profession: political_leader)

SubProfession.create(name: SANGHA_NAYAKULU, name_en: 'Community Leader', profession: independent_organisation_leader)
SubProfession.create(name: UNION_NAYAKULU, name_en: 'Union Leader', profession: independent_organisation_leader)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: independent_organisation_leader)

SubProfession.create(name: JARNALIST, name_en: 'Journalist', profession: press)
SubProfession.create(name: REPORTER, name_en: 'Reporter', profession: press)
SubProfession.create(name: CAMERA_OPERATOR, name_en: 'Camera Operator', profession: press)
SubProfession.create(name: EDITOR, name_en: 'Editor', profession: press)
SubProfession.create(name: NEWS_ANCHOR, name_en: 'News Anchor', profession: press)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: press)

SubProfession.create(name: POLICE, name_en: 'Police', profession: government_officer)
SubProfession.create(name: UPADHYAYULU, name_en: 'Teachers', profession: government_officer)
SubProfession.create(name: REVENUE_SHAAKHA_ADHIKARI, name_en: 'Revenue Department Officer', profession: government_officer)
SubProfession.create(name: DAAKTORS, name_en: 'Doctors', profession: government_officer)
SubProfession.create(name: BANK_UDYOGULU, name_en: 'Bank Employees', profession: government_officer)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: government_officer)

SubProfession.create(name: DAAKTORS, name_en: 'Doctors', profession: medicine)
SubProfession.create(name: NARS, name_en: 'Nurse', profession: medicine)
SubProfession.create(name: NIRVAAHAKULU, name_en: 'Administrators', profession: medicine)
SubProfession.create(name: MEDICAL_SHOP, name_en: 'Medical Shop', profession: medicine)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: medicine)

SubProfession.create(name: GRAPHIC_DESIGNERS, name_en: 'Graphic Designers', profession: multi_media)
SubProfession.create(name: VIDEO_EDITOR, name_en: 'Video Editor', profession: multi_media)
SubProfession.create(name: VISUAL_EFFECTS_ANIMATION, name_en: 'Visual Effects and Animation', profession: multi_media)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: multi_media)

SubProfession.create(name: BANK_UDYOGULU, name_en: 'Bank Employees', profession: finance_or_insurance)
SubProfession.create(name: ACCOUNTANT, name_en: 'Accountant', profession: finance_or_insurance)
SubProfession.create(name: AADITOR, name_en: 'Auditor', profession: finance_or_insurance)
SubProfession.create(name: INSURANCE_AGENT, name_en: 'Insurance Agent', profession: finance_or_insurance)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: finance_or_insurance)

SubProfession.create(name: KIRAANA_SHOP, name_en: 'Grocery Store', profession: shops_or_services)
SubProfession.create(name: BATTALA_VYAPARAM, name_en: 'Clothing Business', profession: shops_or_services)
SubProfession.create(name: CELL_POINT, name_en: 'Mobile Store', profession: shops_or_services)
SubProfession.create(name: RESTAARENT, name_en: 'Restaurant', profession: shops_or_services)
SubProfession.create(name: AUTO_MOBILES, name_en: 'Automobiles', profession: shops_or_services)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: shops_or_services)

SubProfession.create(name: UPADHYAYULU, name_en: 'Teachers', profession: education)
SubProfession.create(name: VIDYARDHI, name_en: 'Student', profession: education)
SubProfession.create(name: NIRVAAHAKULU, name_en: 'Administrators', profession: education)
SubProfession.create(name: ITHARA, name_en: 'Others', profession: education)