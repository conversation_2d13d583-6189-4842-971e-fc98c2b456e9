hashes = [
  {
    name: "<PERSON>", circle_id: 0,start_time: Time.zone.today.beginning_of_day,
    end_time: Time.zone.today.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/posters/2022-07-13/NTL-1.jpg',
             'https://cdn.thecircleapp.in/production/posters/2022-07-13/NTL-2.jpg',
             'https://cdn.thecircleapp.in/production/posters/2022-07-13/NTL-3.jpg']
  }
]

hashes = [
  {
    name: "YSR Birthday", circle_id: 31403,start_time: Time.zone.today.beginning_of_day,
    end_time: Time.zone.today.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/V-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/V-2.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/V-5.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/V-6.jpg']
  },
  {
    name: "Neutral-Eid", circle_id: 0,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/NTL-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/NTL-2.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/NTL-3.jpg']
  },
  {
    name: "TDP-Eid", circle_id: 31402,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TDP-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TDP-2.jpg']
  },
  {
    name: "YCP-Eid", circle_id: 31403,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/YCP-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/YCP-2.jpg']
  },
  {
    name: "JSP-Eid", circle_id: 31406,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/JSP-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/JSP-2.jpg']
  },
  {
    name: "TRS-Eid", circle_id: 31405,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TRS-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TRS-2.jpg']
  },
  {
    name: "Congress-Eid", circle_id: 31401,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/INC-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/INC-2.jpg']
  },
  {
    name: "BJP-Eid", circle_id: 31398,start_time: Time.zone.tomorrow.beginning_of_day,
    end_time: Time.zone.tomorrow.end_of_day,
    photos: ['https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/BJP-1.jpg',
             'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/BJP-2.jpg']
  }
]

hashes.each do |h|
  poster = Poster.create(
    name: h[:name],
    circle_id: h[:circle_id],
    start_time: h[:start_time],
    end_time: h[:end_time],
    admin_user_id: 1,
    active: true
  )

  h[:photos].each do |ph_url|
    photo = Photo.create(url: ph_url, user_id: 41)
    PosterPhoto.create(poster: poster, photo: photo)
  end
end
