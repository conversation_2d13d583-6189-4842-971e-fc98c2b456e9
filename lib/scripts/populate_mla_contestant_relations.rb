require 'csv'
failed_circle_relations = []
leader_party_relation_missed_ids = []
leader_ids_with_constituency = CSV.parse(File.read('./lib/scripts/mla_constituency_data.csv'), headers: true)
leader_ids_with_constituency.each do |row|
  mla_constituency_id = row['mla_constituency_id'].strip.to_i
  brs_leader_id = row['brs_leader_id'].strip.to_i
  inc_leader_id = row['inc_leader_id'].strip.to_i
  bjp_leader_id = row['bjp_leader_id'].strip.to_i
  begin
    # create MLA_Contestant circle relation
    if brs_leader_id.present?
      # check MLA circle relation is present or not
      mla_circle_relation = CirclesRelation.where(first_circle_id: mla_constituency_id, second_circle_id: brs_leader_id,
                                                  relation: :MLA, active: true).first
      if mla_circle_relation.blank?

        CirclesRelation.find_or_create_by(first_circle_id: mla_constituency_id, second_circle_id: brs_leader_id,
                                          relation: :MLA_Contestant)
      else
        # update MLA circle relation to MLA_Contestant
        mla_circle_relation.update!(relation: :MLA_Contestant)
      end

      # check Leader2Party circle relation
      leader2party_circle_relation = CirclesRelation.where(first_circle_id: brs_leader_id, relation: :Leader2Party).first

      leader_party_relation_missed_ids << brs_leader_id if leader2party_circle_relation.blank?

    end

    if inc_leader_id.present?
      mla_circle_relation = CirclesRelation.where(first_circle_id: mla_constituency_id, second_circle_id: inc_leader_id,
                                                  relation: :MLA, active: true).first
      if mla_circle_relation.blank?
        CirclesRelation.find_or_create_by(first_circle_id: mla_constituency_id, second_circle_id: inc_leader_id,
                                          relation: :MLA_Contestant)
      else
        # update MLA circle relation to MLA_Contestant
        mla_circle_relation.update!(relation: :MLA_Contestant)
      end

      # check Leader2Party circle relation
      leader2party_circle_relation = CirclesRelation.where(first_circle_id: inc_leader_id, relation: :Leader2Party).first
      leader_party_relation_missed_ids << inc_leader_id if leader2party_circle_relation.blank?
    end

    if bjp_leader_id.present?
      mla_circle_relation = CirclesRelation.where(first_circle_id: mla_constituency_id, second_circle_id: bjp_leader_id,
                                                  relation: :MLA, active: true).first
      if mla_circle_relation.blank?
        CirclesRelation.find_or_create_by(first_circle_id: mla_constituency_id, second_circle_id: bjp_leader_id,
                                          relation: :MLA_Contestant)
      else
        # update MLA circle relation to MLA_Contestant
        mla_circle_relation.update!(relation: :MLA_Contestant)
      end

      # check Leader2Party circle relation
      leader2party_circle_relation = CirclesRelation.where(first_circle_id: bjp_leader_id, relation: :Leader2Party).first
      leader_party_relation_missed_ids << bjp_leader_id if leader2party_circle_relation.blank?
    end

  rescue => e
    failed_circle_relations << { first_circle_id: mla_constituency_id, error: e.message }
  end
end
