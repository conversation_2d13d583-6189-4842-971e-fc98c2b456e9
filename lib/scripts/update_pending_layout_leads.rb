# Script to move leads from "Pending layout data" to either "Layout Completed" or "Trial Subscribed" stages

# List of user IDs to process
user_ids = [
  18, 33335, 40286, 41994, 56309, 67492, 76165, 90157, 101774, 107022, 120330, 133297, 135522, 169132, 173405, 186205, 196423, 205558, 207766, 215967, 265784, 267451, 270327, 282459, 282640, 285182, 314057, 322304, 330340, 332646, 345550, 346824, 368560, 444366, 448733, 469341, 491805, 499577, 500274, 519782, 523664, 527462, 533561, 547305, 548709, 551341, 570300, 580323, 596316, 600705, 608817, 610897, 617002, 622551, 628731, 649441, 651722, 661861, 670154, 680506, 688914, 709825, 713749, 720507, 732644, 744615, 766458, 772704, 786295, 787893, 804883, 816931, 819187, 834933, 835898, 858653, 868484, 870896, 873193, 877146, 894171, 898045, 899484, 924389, 928515, 954356, 977482, 984017, 987000, 988541, 994075, 1004242, 1018725, 1033891, 1036187, 1037170, 1044783, 1051182, 1054641, 1057262, 1058217, 1069314, 1074725, 1074893, 1089239, 1095084, 1108392, 1116153, 1119096, 1119800, 1122388, 1127267, 1134428, 1138846, 1145040, 1145680, 1151494, 1151808, 1156285, 1166638, 1168756, 1170309, 1170737, 1171067, 1184210, 1191531, 1194332, 1195468, 1201988, 1207453, 1217710, 1219760, 1227467, 1236699, 1238075, 1259416, 1263994, 1265564, 1288629, 1289144, 1294269, 1295013, 1304368, 1314670, 1317725, 1318806, 1338136, 1344474, 1344600, 1349652, 1351324, 1359885, 1363889, 1366886, 1368656, 1369990, 1372462, 1374167, 1374297, 1383803, 1408257, 1409320, 1409892, 1426092, 1431094, 1432993, 1433202, 1433260, 1435475, 1436660, 1438919, 1440505, 1442820, 1448005, 1452584, 1452839, 1453885, 1454306, 1456049, 1461591, 1462451, 1463127, 1463150, 1464371, 1467988, 1468476, 1469049, 1470209, 1470988, 1471900, 1479234, 1481639, 1483891, 1484176, 1484226, 1491728, 1497441, 1504361, 1504576, 1505424, 1505560, 1506831, 1509246, 1511798, 1512394, 1512526, 1526800, 1529654, 1530593, 1531788, 1537060, 1539682, 1541396, 1545469, 1546431, 1547741, 1548223, 1550689, 1550903, 1552099, 1559210, 1571281, 1571788, 1577964, 1579610, 1579623, 1579626, 1580260, 1583383, 1584343, 1588376, 1589736, 1590748, 1595111, 1596948, 1597576, 1597951, 1599096, 1600262, 1600436, 1601802, 1603069
]

# Initialize counters
total_processed = 0
layout_completed_count = 0
trial_subscribed_count = 0
errors_count = 0
errors = []

# Process each user ID
user_ids.each_with_index do |user_id, index|
  begin
    # Find the user and their premium pitch
    user = User.find_by(id: user_id)

    if user.nil?
      puts "User #{user_id} not found. Skipping."
      errors << { user_id: user_id, error: "User not found" }
      errors_count += 1
      next
    end

    premium_pitch = PremiumPitch.find_by(user_id: user_id)

    if premium_pitch.nil?
      puts "Premium pitch for user #{user_id} not found. Skipping."
      errors << { user_id: user_id, error: "Premium pitch not found" }
      errors_count += 1
      next
    end

    # Check if the user has a premium layout
    has_layout = user.has_premium_layout?
    # if user plan exists, we can assume that user is a trial user subscribed
    is_trial_user = SubscriptionUtils.has_user_ever_subscribed?(user.id)

    if is_trial_user
      if premium_pitch.may_payment_done?
        premium_pitch.payment_done!
      else
        errors << { user_id: user_id, error: "Cannot transition to payment_done state" }
        errors_count += 1
      end
    elsif has_layout
      # Move to "Layout Completed" stage
      if premium_pitch.may_enabled_trial?
        premium_pitch.enabled_trial!
        layout_completed_count += 1
      else
        puts "User #{user_id} cannot be moved to Layout Completed stage due to state machine constraints."
        errors << { user_id: user_id, error: "Cannot transition to enabled_trial state" }
        errors_count += 1
      end
    else
      puts "User #{user_id} has neither premium layout nor trial status. Skipping."
      errors << { user_id: user_id, error: "No premium layout or trial status" }
      errors_count += 1
    end

    total_processed += 1

    # Print progress every 10 users
    if (index + 1) % 10 == 0
      puts "Processed #{index + 1}/#{user_ids.size} users..."
    end

    # Sleep briefly to avoid overwhelming the system
    sleep(0.1)

  rescue => e
    puts "Error processing user #{user_id}: #{e.message}"
    puts e.backtrace.join("\n")
    errors << { user_id: user_id, error: e.message }
    errors_count += 1
  end
end

# Print summary
puts "\n=== Summary ==="
puts "Total users processed: #{total_processed}"
puts "Users moved to Layout Completed stage: #{layout_completed_count}"
puts "Users moved to Trial Subscribed stage: #{trial_subscribed_count}"
puts "Errors encountered: #{errors_count}"

# Print errors if any
if errors.any?
  puts "\n=== Errors ==="
  errors.each do |error|
    puts "User #{error[:user_id]}: #{error[:error]}"
  end
end

# Return success
true
