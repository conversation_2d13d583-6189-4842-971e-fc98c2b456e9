# For corporators in GHMC and Warangal update end_date in user_roles to '31/05/2026'
UserRole.joins(:user)
        .where(purview_circle_id: [34457, 11443])
        .where(active: true, role_id: 10, free_text: nil)
        .update_all(end_date: Time.zone.parse('2026-05-31'))

# For corporators in Karimnagar update end_date in user_roles as '28/01/2025'
UserRole.joins(:user)
        .where(purview_circle_id: [12646])
        .where(active: true, role_id: 10, free_text: nil)
        .update_all(end_date: Time.zone.parse('2025-01-28'))

# For corporators in Telangana, expect Karimnagar, GHMC and Warangal update end_date in user_roles as '26/01/2025'
UserRole.joins(:user)
        .where.not(purview_circle_id: [34457, 11443, 12646])
        .where(users: { state_id: 33010 })
        .where(active: true, role_id: 10, free_text: nil)
        .update_all(end_date: Time.zone.parse('2025-01-26'))

# For Ward Councilors in Telangana, update end_date in user_roles as '26/01/2025'
UserRole.joins(:user)
        .where(users: {state_id: 33010})
        .where(active: true, role_id: 38, free_text: nil)
        .update_all(end_date: Time.zone.parse('2025-01-26'))
