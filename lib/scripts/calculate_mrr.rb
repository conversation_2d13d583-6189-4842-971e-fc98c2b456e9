# frozen_string_literal: true

# This scripts prints out MRR for the last 12 months (including current month)
start_time = Time.now
today = Date.today
start_date = today.beginning_of_month - 11.months # Start 12 months back
end_date = today.end_of_month

# Initialize a hash to store MRR by month
mrr_by_month = Hash.new(0)

# Fetch relevant subscriptions
old_order_logs = UserPlanLog
                 .includes(:entity)
                 .where(entity_type: 'Order')
                 .where('start_date <= ? OR end_date >= ?', end_date, start_date)

orders_count = 0
old_order_logs.find_each do |log|
  orders_count += 1
  order = log.entity
  next unless order.payable_amount > 100 # < 100 orders are mostly internal test orders

  total_revenue = order.payable_amount.to_f
  total_days = (log.end_date.to_date - log.start_date.to_date).to_i
  next unless total_days.positive?

  daily_revenue = total_revenue / total_days

  loop_start_date = [start_date, log.start_date].max
  loop_end_date = [end_date, log.end_date].min

  next if loop_start_date > loop_end_date

  current_month = loop_start_date.beginning_of_month
  last_month = loop_end_date.end_of_month

  while current_month <= last_month
    days_in_month = ([loop_end_date, current_month.end_of_month].min.to_date - [loop_start_date, current_month].max.to_date).to_i + 1
    mrr_by_month[current_month.to_date] += days_in_month * daily_revenue

    current_month = current_month.next_month
  end
end

new_charges_logs = UserPlanLog
                   .includes(:entity)
                   .where(entity_type: 'SubscriptionCharge')
                   .where(active: true)
                   .where('start_date <= ? OR end_date >=?', end_date, start_date)

charges_count = 0
new_charges_logs.find_each do |log|
  charges_count += 1
  sub_start_date = log.start_date
  sub_end_date = log.end_date
  charge = log.entity

  next unless charge.present?
  next unless charge.charge_amount > 1

  total_revenue = charge.amount.to_f
  total_days = (sub_end_date.to_date - sub_start_date.to_date).to_i
  next unless total_days.positive?

  daily_revenue = total_revenue / total_days

  loop_start_date = [start_date, sub_start_date].max
  loop_end_date = [end_date, sub_end_date].min

  next if loop_start_date > loop_end_date

  current_month = loop_start_date.beginning_of_month
  last_month = loop_end_date.end_of_month

  while current_month <= last_month
    days_in_month = ([loop_end_date, current_month.end_of_month].min.to_date - [loop_start_date, current_month].max.to_date).to_i + 1
    mrr_by_month[current_month.to_date] += days_in_month * daily_revenue

    current_month = current_month.next_month
  end
end

end_time = Time.now

def format_duration(duration)
  seconds = duration.to_i # Convert duration to seconds
  hours = seconds / 3600
  minutes = (seconds % 3600) / 60
  seconds_in_output = seconds % 60

  formatted = []
  formatted << "#{hours}h" if hours.positive?
  formatted << "#{minutes}m" if minutes.positive? || hours.positive?
  formatted << "#{seconds_in_output}s"

  formatted.join
end

puts "Processed #{orders_count} Orders and #{charges_count} SubscriptionCharges in #{format_duration(end_time - start_time)}"
puts 'MRR:'
mrr_by_month.each do |month, revenue|
  # Sep 2024 - ₹8.24L - ₹824321.12
  puts "#{month.strftime('%b %Y')} - ₹#{(revenue / 1_00_000).round(2)}L - ₹#{revenue.round(2)}"
end;nil
