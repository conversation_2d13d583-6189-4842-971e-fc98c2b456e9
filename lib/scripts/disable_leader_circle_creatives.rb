distinct_creative_ids = PosterCreative.joins(poster_creative_circles: :circle)
                                      .where(circles: { level: :political_leader, circle_type: :interest })
                                      .where('end_time <= ?', Time.zone.parse('2024-07-31'))
                                      .distinct.pluck(:id)

PosterCreative.where(id: distinct_creative_ids).update_all(active: :false)
