# format of data is [ex_role_id, role_id] i.e. ['ex-sarpanch', 'sarpanch']
roles_data = [[126, 66], [124, 127], [120, 128], [111, 61], [108, 56], [104, 14], [102, 81], [101, 84], [98, 97],
              [88, 7], [87, 6], [71, 12], [70, 11], [48, 36], [47, 46], [39, 38], [28, 129], [27, 15], [26, 13],
              [25, 10], [24, 9], [23, 8], [22, 5], [21, 20], [19, 4], [18, 3], [17, 2], [16, 1]]

roles_data.each do |role_data|
  UserRole.where(role_id: role_data[0]).update_all(role_id: role_data[1], start_date: Date.parse("2000-01-01"),
                                                   end_date: Date.parse("2005-01-01"))
  sleep(1)
end
