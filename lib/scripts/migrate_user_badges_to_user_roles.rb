def test(last_id)
  failed_cases = []

  begin
    UserBadge.where(active: 1).where("id >= ?", last_id).each do |ub|
      puts "USER BADGE ID - #{ub.id}"

      role = Role.where(name: ub.badge_role.text).first
      secondary_circle_id = nil

      #Primary circle id
      if [69, 57, 64, 93].include? ub.badge_role_id
        primary_circle_id = User.find(ub.user_id).district_id
      elsif ub.badge_role_id == 65 #Leader
        if ub.political_party_id.present?
          primary_circle_id = ub.political_party_id
        elsif ub.badge_icon.present?
          primary_circle_id = ub.get_badge_icon_circle_id
        else
          role = Role.where(name: ub.badge_role.text, primary_circle_type: :location, primary_circle_level: :village).last
          primary_circle_id = User.find(ub.user_id).village_id
        end
      else
        if role.location_primary_circle_type?
          primary_circle_id = ub.location_circle_id
        elsif role.interest_primary_circle_type?
          if ub.political_party_id.present?
            primary_circle_id = ub.political_party_id
          else
            primary_circle_id = ub.get_badge_icon_circle_id
          end
        end
      end

      #secondary circle id
      if role.has_secondary_circle
        if role.location_secondary_circle_type?
          secondary_circle_id = ub.location_circle_id
        elsif role.interest_secondary_circle_type?
          if ub.political_party_id.present?
            secondary_circle_id = ub.political_party_id
          else
            secondary_circle_id = ub.get_badge_icon_circle_id
          end
        end
      end

      #Badge ring
      user_role_badge_ring = nil
      user_badge_ring = nil
      if ["SILVER_RING", "GOLD_RING"].include? ub.badge_ring
        user_badge_ring = true
      elsif ["NO_RING"].include? ub.badge_ring
        user_badge_ring = false
      end
      if user_badge_ring != role.badge_ring
        user_role_badge_ring = user_badge_ring
      end

      #Badge color
      user_role_badge_color = nil
      user_badge_color = ub.badge_banner
      if role.badge_color != user_badge_color
        user_role_badge_color = user_badge_color
      end

      #grade level
      user_role_grade_level = nil
      user_badge_grade_level = ub.grade_level
      if role.grade_level != user_badge_grade_level
        user_role_grade_level = user_badge_grade_level
      end

      hash = {
        "user_id" => ub.user_id,
        "role_id" => role.id,
        "primary_circle_id" => primary_circle_id,
        "secondary_circle_id" => secondary_circle_id,
        "badge_ring" => user_role_badge_ring,
        "badge_color" => user_role_badge_color,
        "grade_level" => user_role_grade_level,
        "primary_role" => true,
        "is_celebrated" => ub.is_celebrated
      }

      begin
        UserRole.create!(user_id: hash["user_id"], role_id: hash["role_id"], primary_circle_id: hash["primary_circle_id"],
                         secondary_circle_id: hash["secondary_circle_id"], badge_ring: hash["badge_ring"],
                         badge_color: hash["badge_color"], grade_level: hash["grade_level"], primary_role: hash["primary_role"],
                         is_celebrated: hash["is_celebrated"])
      rescue => e
        failed_cases << "#{ub.id}, #{ub.badge_role.text}, #{e}"
      end
    end
  rescue
    failed_cases.each do |c|
      puts c
    end
  end
end
