posters = Poster.all
AdminMedium.skip_callback(:save, :before, :set_media_type)
AdminMedium.skip_callback(:save, :before, :upload)
posters.each do |poster|
  poster_photos = poster.poster_photos
  poster_photos.each do |poster_photo|
    admin_medium = AdminMedium.new(url: poster_photo.new_version_photo_url, media_type: "photo", admin_user_id: poster.admin_user_id)
    admin_medium.save(:validate => false)

    poster_photo.update_columns(photo_id: admin_medium.id, photo_type: "AdminMedium")
  end
end
AdminMedium.set_callback(:save, :before, :set_media_type)
AdminMedium.set_callback(:save, :before, :upload)
