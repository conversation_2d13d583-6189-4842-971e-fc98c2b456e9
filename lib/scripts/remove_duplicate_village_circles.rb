# remove duplicate village circles
require 'csv'

village_circles = CSV.parse(File.read('./lib/scripts/duplicate_village_circles.csv'), headers: true)

# take village circles hash key as duplicate circle id and value as original circle id
village_circles_hash = {}
village_circles.each do |list|
  duplicate_circle_id = list["duplicate_village_id"].to_i
  original_circle_id = list["original_village_id"].to_i
  village_circles_hash.store(duplicate_circle_id, original_circle_id)
end

# post circles clean up
village_circles_hash.each do |duplicate, original|
  PostCircle.where(circle_id: duplicate).update(circle_id: original)
end

# members clean up
village_circles_hash.each do |duplicate, original|
  # all users joined in duplicate circle
  duplicate_circle_joined_user_ids = UserCircle.where(circle_id: duplicate).pluck(:user_id)

  # update duplicate circle to original circle for users who only joined in duplicate circle
  UserCircle.where(circle_id: duplicate).update(circle_id: original)

  duplicate_circle_joined_user_ids.each do |user_id|
    user = User.find_by(id: user_id)
    if user.present?
      user.update(village_id: original)
      user.update_location
    end
  end
end

# user roles clean up
village_circles_hash.each do |duplicate, original|
  UserRole.where(purview_circle_id: duplicate).update(purview_circle_id: original)
end

# post bounces clean up
PostBounce.where(circle_id: village_circles_hash.keys).destroy_all

# clear cache of posts
post_ids = PostCircle.where(circle_id: village_circles_hash.values).pluck(:post_id)
post_ids.each do |post_id|
  Rails.cache.delete([Post::CACHE_KEY, post_id])
end

# delete duplicate circles
Circle.where(id: village_circles_hash.keys).destroy_all

