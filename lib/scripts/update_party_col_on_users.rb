uid = $redis.get('indexing_last_user_id_for_user_data_update').to_i
user_id=nil
count=0
User.active.where(id: uid..uid+10000).order(id: :asc).each do |user|
  user_id = user.id
  #adding user exclusive party  to user data
  UpdateAffiliatedPartyOnUser.perform_async(user_id)

  count += 1
  sleep 1 if (count % 100).zero?
end

$redis.set('indexing_last_user_id_for_user_data_update', user_id.to_s) if count > 0
