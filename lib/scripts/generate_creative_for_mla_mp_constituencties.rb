mla_mp_contestants = Circle.joins("INNER JOIN circles_relations AS lp ON lp.second_circle_id = circles.id",
                                  "INNER JOIN circles_relations AS ps ON ps.first_circle_id = lp.second_circle_id",
                                  "INNER JOIN circles_relations AS mmc ON mmc.second_circle_id = lp.first_circle_id")
                           .where("ps.second_circle_id = ? AND mmc.relation IN ('MP Contestant')", 33010)
                           .where(lp: { relation: 'Leader2Party' })
                           .where(ps: { relation: 'Party2State' })
                           .distinct
                           .pluck('mmc.second_circle_id')

require 'csv'

mla_mp_relations = CSV.parse(File.read('lib/scripts/mla_mp_relations.csv'), headers: true)
mla_mp_relations.each do |mmr|
  constituency_id = mmr['constituency_id'].to_i
  leader_id = mmr['leader_id'].to_i
  contestant_relation = mmr['contestant_relation'].to_sym
  party_id = mmr['party_id'].to_i
  party_relation = mmr['party_relation'].to_sym

  leader2party_v1 = CirclesRelation.where(relation: 'Leader2Party', first_circle_id: leader_id).last
  if leader2party_v1.present?
    if leader2party_v1.second_circle_id != party_id
      leader2party_v1.update(second_circle_id: party_id)
    end
  else
    CirclesRelation.create(first_circle_id: leader_id, second_circle_id: party_id, relation: party_relation)
  end
  if CirclesRelation.where(first_circle_id: constituency_id, second_circle_id: leader_id).present?
    CirclesRelation.where(first_circle_id: constituency_id, second_circle_id: leader_id).last.update(relation: contestant_relation)
  else
    CirclesRelation.create(first_circle_id: constituency_id, second_circle_id: leader_id, relation: contestant_relation)
  end
end
