# frozen_string_literal: true

json_data = [{ "user_id": 333605, "stage": "Layout Completed", "lead_type": "LT_Inbound" }]

user_ids = json_data.map { |data| data[:user_id] }

PremiumPitch.where(user_id: user_ids).group(:status).count

json_data.each do |data|
  user_id = data[:user_id]
  lead_type = data[:lead_type]
  premium_pitch = PremiumPitch.where(user_id: user_id).last

  if premium_pitch.blank?
    premium_pitch = PremiumPitch.create(user_id: user_id, lead_type: lead_type, source: :MANUAL)
    premium_pitch.shown_interest! if premium_pitch.may_shown_interest?
  else
    if premium_pitch.may_shown_interest?
      premium_pitch.shown_interest!
    elsif premium_pitch.interested?
      Floww::AddLead.perform_async(user_id, lead_type)
    end
  end
end; 0

# After Sidekiq queue clearing
json_data.each do |data|
  premium_pitch = PremiumPitch.where(user_id: data[:user_id]).last
  premium_pitch.enabled_trial! if premium_pitch.may_enabled_trial?
  Floww::TrialEnabled.perform_async(data[:user_id])
end; 0

# After Sidekiq queue clearing
json_data.each do |data|
  premium_pitch = PremiumPitch.where(user_id: data[:user_id]).last
  premium_pitch.reached_milestone_1! if premium_pitch.may_reached_milestone_1?
  Floww::TrialUsed.perform_async(data[:user_id])
end; 0

# After Sidekiq queue clearing
json_data.each do |data|
  premium_pitch = PremiumPitch.where(user_id: data[:user_id]).last
  premium_pitch.reached_milestone_2! if premium_pitch.may_reached_milestone_2?
  Floww::QualifiedPitch.perform_async(data[:user_id])
end; 0
