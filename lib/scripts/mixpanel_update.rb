file = File.open "/path/to/your/file.json"
user_ids = JSON.load file

User.where(id: user_ids).find_each(batch_size: 100) do |u|
  MixpanelIntegration.perform_async(
    u.id,
    {"$name": u.name,
     "$phone": "+91#{u.phone}",
     "$created": (u.created_at - 5.hours - 30.minutes).strftime("%Y-%m-%dT%TZ"),
     "is_internal": (u.internal == 1 ? true : false),
     "village_id": u.village&.id,
     "mandal_id": u.mandal&.id,
     "district_id": u.district&.id,
     "default_feed_backend": u.my_feed_enabled? ? 'my_feed' : 'trending_feed'})
end
