# Steps to follow
# Step-1: Upload the fan poster photo into AdminMedium from Admin dashboard and get the id of the record
# Step-2: initialise the other attributes
# creative kind - info
# send_dm_message_notification - `true` (to send msg with notification) `false` (to send msg without notification)
# dm_text_message - Text to show with poster in the message (optional can be empty string)
# creator - replace with your admin user id
# circle_id - replace with the circle id
# leader_circle_ids - get the leader circle ids associated with circle id
# admin_medium - get the admin medium id and replace here
# Step-3: Check the aspect ratio of the fan poster uploaded
creative_kind = :info
send_dm_message_notification = true
dm_text_message = ""
creator = AdminUser.find_by(id: 19)
circle_id = 5
leader_circle_ids = CirclesRelation.where(second_circle_id: circle_id, relation: 'Leader2Party').pluck(:first_circle_id)
admin_medium = AdminMedium.find_by(id: 1)
if admin_medium.present?
  aspect_ratio_2 = 4.0 / 5.0
  w, h = FastImage.size(admin_medium.url)
  if ((w.to_f / h.to_f) - aspect_ratio_2).abs <= 0.01
    poster_creative = PosterCreative.new(creative_kind: creative_kind,
                                         photo_v3: admin_medium,
                                         paid: false,
                                         primary: false,
                                         h1_leader_photo_ring_type: :light,
                                         h2_leader_photo_ring_type: :sticker,
                                         active: true,
                                         creator: creator,
                                         send_dm_message: true,
                                         send_dm_message_notification: send_dm_message_notification,
                                         dm_text_message: dm_text_message)
    leader_circle_ids.each do |leader_circle_id|
      poster_creative.poster_creative_circles.build(circle_id: leader_circle_id)
    end

    poster_creative.save(validate: false)
  end
end
