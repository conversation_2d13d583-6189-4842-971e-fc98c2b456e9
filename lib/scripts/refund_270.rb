# frozen_string_literal: true

user_ids = [1283987, 307691, 789190, 389658, 928620, 138676, 1222077, 305968]

start_time = Time.zone.strptime("2024-10-10 15:00:00", '%Y-%m-%d %H:%M:%S')
end_time = Time.zone.strptime("2024-10-13 00:00:00", '%Y-%m-%d %H:%M:%S')
user_id = user_ids.pop
user_ids.each do |user_id|
  SubscriptionCharge.where(user_id: user_id)
  charge = SubscriptionCharge.where(user_id: user_id, status: :success).where("amount > 30").last
  if charge.subscription.cashfree?
    merchant_txn_id = charge.pg_json.dig("cf_merchantTxnId")
    CashfreePaymentUtils.cashfree_post_v1(
      "/subscriptions/create-refund",
      {
        "merchantTxnId": merchant_txn_id,
        "refundAmount": charge.amount - 29,
        "refundNote": "Partial refund",
        "merchantRefundId": "p_refund_#{merchant_txn_id}",
        "requestedSpeed": "STANDARD"
      }
    )
  elsif charge.subscription.juspay?
    JuspayPaymentUtils.post(
      "/orders/#{charge.pg_id}/refunds",
      {
        unique_request_id: "R-#{charge.pg_id}",
        amount: charge.amount - 29,
      }
    )
  end

  charge.update(amount: 29)
end
