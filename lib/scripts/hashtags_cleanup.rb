Hashtag.group(:identifier).having("COUNT(id) > 1").count.each do |hh|
  identifier = hh[0]
  hashtags = Hashtag.where(identifier: identifier).order(id: :desc)
  original_hashtag = hashtags.last
  original_hashtag_liked_user_ids = original_hashtag.likes.pluck(:user_id)
  original_hashtag_post_ids = original_hashtag.post_hashtags.pluck(:post_id)

  hashtags[0..-2].each do |h|
    h.likes.where(user_id: original_hashtag_liked_user_ids).destroy_all
    h.post_hashtags.where(post_id: original_hashtag_post_ids).destroy_all
    h.likes.update_all(hashtag_id: original_hashtag.id)
    h.post_hashtags.update_all(hashtag_id: original_hashtag.id)

    $redis.hkeys("hashtag_whatsapp_shares_#{h.id}").each do |user_id|
      $redis.hincrby("hashtag_whatsapp_shares_#{original_hashtag.id}", user_id, 1)
    end
    $redis.del("hashtag_whatsapp_shares_#{h.id}")

    h.destroy
  end

  original_hashtag.queue_for_index
end

Hashtag.find_in_batches(batch_size: 1000) do |hashtags|
  hashtags.each do |h|
    h.queue_for_index
  end
end
