post_ids = []
posts_es = ES_CLIENT.search index: EsUtil.get_new_posts_index, body: {
  "query": {
    "bool": {
      "must_not": {
        "exists": {
          "field": "user_state_id"
        }
      }
    }
  },
  "size": 10000
}

posts_es['hits']['hits'].each do |hit|
  post_es_item = hit['_source']
  post_ids << post_es_item['id']
end

post_ids.each_slice(250) do |group|
  group.each do |post_id|
    IndexPostNew.perform_async(post_id)
  end
  sleep(1)
end