require 'csv'

# This script is used to populate the lead_admin_users_for_layouts table
# with the users who are lead admins for the layouts.

csv_data = CSV.parse(File.read('./lib/scripts/lead_admin_user_update.csv'), headers: true)
csv_data.each do |row|
  layout_id = row["layout_id"].to_i
  admin_user_id = row["lead_admin_user_id"].to_i
  UserPosterLayout.where(id: layout_id).update(lead_admin_user_id: admin_user_id) if admin_user_id > 0
end
