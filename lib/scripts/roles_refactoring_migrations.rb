failed_cases = []
role_changes = CSV.parse(File.read('./lib/scripts/roles_parent_circle_migration_data.csv'), headers: true)
role_changes.each do |role|
  begin
    role_id = role['role_id'].to_i
    parent_circle_id = role['parent_circle_id'].blank? ? nil : role['parent_circle_id'].to_i
    parent_circle_level = role['parent_circle_level'].blank? ? nil : role['parent_circle_level'].to_i
    role = Role.find(role_id)
    role.parent_circle_id = parent_circle_id
    parent_circle_level = Circle.levels.key(parent_circle_level)
    role.parent_circle_level = parent_circle_level

    if role.primary_circle_level != role.parent_circle_level
      role.has_purview = true
      role.purview_level = role.primary_circle_level
      if role.primary_circle_to_badge_text
        role.purview_circle_to_badge_text = true
        role.parent_circle_to_badge_text = false
      end
    else
      if role.secondary_circle_level.present?
        role.has_purview = true
        role.purview_level = role.secondary_circle_level
      else
        role.has_purview = false
        role.purview_level = nil
      end
      role.parent_circle_to_badge_text = role.primary_circle_to_badge_text
      role.purview_circle_to_badge_text = role.secondary_circle_to_badge_text
    end
    role.permission_group_id = 1
    role.has_badge_icon = true

    # display name order
    display_name_order = []
    display_name_order << 'purview' if role.purview_circle_to_badge_text
    if role.prefix
      display_name_order << 'parent' if role.parent_circle_to_badge_text
      display_name_order << 'role'
    else
      display_name_order << 'role'
      display_name_order << 'parent' if role.parent_circle_to_badge_text
    end
    role.display_name_order = display_name_order.join(',')
    role.save!
  rescue => e
    failed_cases << "role_id: #{role.id} error: #{e}"
  end
end
