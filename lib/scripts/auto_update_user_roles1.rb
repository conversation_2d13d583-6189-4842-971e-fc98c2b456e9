require 'csv'

not_signed_up_users = CSV.parse(File.read('lib/scripts/auto_badge_creation_data.csv'), headers: true)
failed_phones = []
not_signed_up_users.each do |nsu|
  begin
    name = nsu['name']
    phone = nsu['phone']
    village_id = nsu['village_id']
    role_id = nsu['role_id']
    start_date = nsu['start_date']
    end_date = nsu['end_date']
    purview_circle_id = nsu['purview_circle_id']
    user = User.create(phone: phone)
    user.village_id = village_id
    user.populate_location
    user.name = name
    user.user_circles.build(circle_id: village_id)
    user.status = :auto_signed_up
    user.save!

    UserRole.create(user_id: user.id, role_id: role_id, purview_circle_id: purview_circle_id, primary_role: true, start_date: Time.zone.parse(start_date), end_date: Time.zone.parse(end_date))

    EventTracker.perform_async(user.id, "signup_backend", { "source" => "auto_badge_creation" })
  rescue => e
    failed_phones << phone
  end
end
