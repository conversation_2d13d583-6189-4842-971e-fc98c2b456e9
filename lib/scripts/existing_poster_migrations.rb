poster_photos = [
  { id: 1, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/V-1.jpg', poster_id: 1},
  { id: 2, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/V-2.jpg', poster_id: 1},
  { id: 3, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/V-5.jpg', poster_id: 1},
  { id: 4, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/V-6.jpg', poster_id: 1},
  { id: 5, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/NTL-1.jpg', poster_id: 2},
  { id: 6, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/NTL-2.jpg', poster_id: 2},
  { id: 7, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/NTL-3.jpg', poster_id: 2},
  { id: 8, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TDP-1.jpg', poster_id: 3},
  { id: 9, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TDP-2.jpg', poster_id: 3},
  { id: 10, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/YCP-1.jpg', poster_id: 4},
  { id: 11, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/YCP-2.jpg', poster_id: 4},
  { id: 12, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/JSP-1.jpg', poster_id: 5},
  { id: 13, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/JSP-2.jpg', poster_id: 5},
  { id: 14, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TRS-1.jpg', poster_id: 6},
  { id: 15, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/TRS-2.jpg', poster_id: 6},
  { id: 16, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/INC-1.jpg', poster_id: 7},
  { id: 17, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/INC-2.jpg', poster_id: 7},
  { id: 18, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/BJP-1.jpg', poster_id: 8},
  { id: 19, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/bakrid-posters/BJP-2.jpg', poster_id: 8},
  { id: 20, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/ysr-posters/Plenary-1.jpg', poster_id: 9},
  { id: 21, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/ysr-posters/Plenary-2.jpg', poster_id: 9},
  { id: 22, photo_url: 'https://cdn.thecircleapp.in/production/adhoc/ysr-posters/Plenary-3.jpg', poster_id: 9},
  { id: 23, photo_url: 'https://cdn.thecircleapp.in/production/posters/2022-07-11/BJP-1-compressed.jpg', poster_id: 10},
  { id: 24, photo_url: 'https://cdn.thecircleapp.in/production/posters/2022-07-11/JSP-1-compressed.jpg', poster_id: 11},
  { id: 25, photo_url: 'https://cdn.thecircleapp.in/production/posters/2022-07-13/NTL-1-compressed.jpg', poster_id: 12},
  { id: 26, photo_url: 'https://cdn.thecircleapp.in/production/posters/2022-07-13/NTL-2-compressed.jpg', poster_id: 12},
  { id: 27, photo_url: 'https://cdn.thecircleapp.in/production/posters/2022-07-13/NTL-3-compressed.jpg', poster_id: 12}
]

poster_photos.each do |poster_photo|
  pp = PosterPhoto.find(poster_photo[:id])
  pp.update_columns(photo_url: poster_photo[:photo_url], poster_id: poster_photo[:poster.id])
end

Photo.delete([275466, 275467, 275468,275469,284667,275470,275471,275472,275473,275474,275475,275476,275477,275478,
              275479,275480,275481,275482,275483,275484,275575,275576,275577,284666])

