#Here we can check duplicate mandal ids mapped to various mla constituencies

mandal_ids = CirclesRelation.where(relation: 'Mandal2MLA').all.map(&:first_circle).map(&:id)
duplicate_mandal_ids = mandal_ids.select{|element| mandal_ids.count(element) > 1 }
duplicate_mandal_ids = duplicate_mandal_ids.uniq
mla_constituencies_ = CirclesRelation.where(first_circle: duplicate_mandal_ids.each do |c|
    c end,active:true,relation: 'Mandal2MLA').all.map(&:second_circle).map(&:id)