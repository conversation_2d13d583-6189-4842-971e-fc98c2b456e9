# frozen_string_literal: true

queue = Sidekiq::Queue.new("critical")
counter = {}
queue.each do |job|
  counter[job.klass] = 0 unless counter[job.klass].present?
  counter[job.klass] += 1

  if job.klass == "UserSubCirclesRevaluation" || job.klass == "SendOtpJob"
    begin
      Object.const_get(job.klass).new.perform(*job.args)
      job.delete
    rescue => e
      puts "Error: #{e.message}"
    end
  elsif job.klass == "QueueUserPostViews"
    job.klass.constantize.new.perform(*job.args)
  end
end;0
