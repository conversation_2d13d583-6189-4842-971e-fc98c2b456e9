# create fonts
Font.create(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
Font.create(name_font: "Anek Telugu", badge_font: "Noto Sans Telugu")

# create frames
font_1 = Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
Frame.all.update_all(font_id: font_1.id, user_position_back: false)

font_2 = Font.find_by(name_font: "Anek Telugu", badge_font: "Noto Sans Telugu")
Frame.where(active: true).each do |frame|
  # create another frame with same data but font_id of font_2
  new_frame = frame.dup
  new_frame.font_id = font_2.id
  new_frame.user_position_back = true
  new_frame.save!
  # create item prices for new frame
  frame.item_prices.each do |item_price|
    new_item_price = item_price.dup
    new_item_price.item_id = new_frame.id
    new_item_price.save
  end
end

# update existing order items with new frame_id if is_user_position is true
Order.where(status: [:opened, :pending, :last_transaction_failed, :successful]).each do |order|
  user = order.user
  is_user_position_back = user.get_is_user_position_back_for_posters_tab_v2
  order.order_items.each do |order_item|
    if order_item.item_type == "Frame"
      frame = Frame.find_by(id: order_item.item_id)
      if frame.present? && is_user_position_back
        similar_frame = Frame.where(
          frame.attributes.except('id', 'font_id', 'created_at', 'updated_at', 'user_position_back')
        ).find_by(font_id: font_2.id, user_position_back: true)

        order_item.update(item_id: similar_frame.id)
      end
    end
  end
end

# update existing user product subscriptions with new frame_id if is_user_position is true
UserProductSubscription.where(active: true).each do |user_product_subscription|
  user = user_product_subscription.user
  is_user_position_back = user.get_is_user_position_back_for_posters_tab_v2
  if user_product_subscription.item_type == "Frame"
    frame = Frame.find_by(id: user_product_subscription.item_id)
    if frame.present? && is_user_position_back
      similar_frame = Frame.where(
        frame.attributes.except('id', 'font_id', 'created_at', 'updated_at', 'user_position_back')
      ).find_by(font_id: font_2.id, user_position_back: true)

      user_product_subscription.update(item_id: similar_frame.id)
    end
  end
end
