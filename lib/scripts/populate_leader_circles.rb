# Populate leader circles and their circle relations too if exists
require 'csv'
failed_circle_ids = []
failed_circle_relations = []
photo_failed_circle_ids = []

leader_circles = CSV.parse(File.read('./lib/scripts/mlc_rajyasabha_mp_circles_data.csv'), headers: true)
leader_circles.each do |row|
  name = row['name'].strip
  name_en = row['name_en'].strip
  circle_type = :interest
  level = :political_leader
  short_info = row['short_info'].strip
  photo_url = row['photo_url']&.strip
  party_id = row['party_id'].strip.to_i
  district_ids = row['district_ids'].gsub(/[\[\]]/, '').split(',').map(&:to_i)

  begin
    # create leader circle
    leader_circle = Circle.create!(name: name, name_en: name_en, circle_type: circle_type, level: level,
                                   short_info: short_info)

    # create leader circle photo
    if photo_url.present?
      upload_circle_photo_to_s3(photo_url, leader_circle.id)
      leader_circle.reload
      photo_failed_circle_ids << leader_circle.id if leader_circle.photo_id.nil?
    end
    # create circles relations related to it
    # create leader2party circle relation if party_id present
    unless party_id.blank?
      begin
        CirclesRelation.create!(first_circle_id: leader_circle.id, second_circle_id: party_id, relation: :Leader2Party)
      rescue => e
        failed_circle_relations << { first_circle_id: leader_circle.id, second_circle_id: party_id,
                                     relation: :Leader2Party, error: e.message }
      end
    end
    if short_info == "ఎమ్మెల్సీ"
      district_ids.each do |district_id|
        begin
          CirclesRelation.create!(first_circle_id: district_id, second_circle_id: leader_circle.id, relation: :MLC)
        rescue => e
          failed_circle_relations << { first_circle_id: district_id, second_circle_id: leader_circle.id,
                                       relation: :MLC, error: e.message }
        end
      end
    elsif short_info == "రాజ్యసభ సభ్యులు"
      district_ids.each do |district_id|
        begin
          CirclesRelation.create!(first_circle_id: district_id, second_circle_id: leader_circle.id, relation: :MP_Rajyasabha)
        rescue => e
          failed_circle_relations << { first_circle_id: district_id, second_circle_id: leader_circle.id,
                                       relation: :MP_Rajyasabha, error: e.message }
        end
      end
    else
      failed_circle_relations << { first_circle_id: district_ids, second_circle_id: leader_circle.id,
                                   error: "short_info is not MLC or MP_Rajyasabha" }
    end
  rescue => e
    failed_circle_ids << { name: name, error: e.message }
  end
end

def upload_circle_photo_to_s3(image_url, circle_id)
  circle = Circle.find_by(id: circle_id)
  user = User.find_by(id: Constants.praja_account_user_id)
  return if circle.nil? || user.nil? || image_url.nil?

  resource = Aws::S3::Resource.new(
    region: 'ap-south-1',
    credentials: Aws::Credentials.new(
      Rails.application.credentials[:aws_access_key_id],
      Rails.application.credentials[:aws_secret_access_key]
    )
  )

  # Extract the file_id from the URL
  file_id = image_url.match(%r{/file/d/(.*?)/})[1]
  image_url = "https://drive.google.com/uc?id=#{file_id}&export=download"
  image_file = URI.open(image_url)
  # get extension of the image of google drive url
  file_extension = image_file.content_type.split('/').last
  file_name = "#{circle.name_en.strip.downcase.gsub('.', '').gsub(' ', '-')}" + Time.zone.now.to_i.to_s + ".#{file_extension}"

  s3_object_path = Rails.env + '/politician-photos/' + file_name

  begin
    resource
      .bucket(Rails.application.credentials[:aws_s3_bucket_name])
      .object(s3_object_path)
      .put(body: image_file)

    photo = Photo.new(
      url: 'https://cdn.thecircleapp.in/' + s3_object_path,
      user: user,
      service: :aws
    )

    if photo.save
      circle.photo = photo
    else
      circle.photo = nil
    end
    circle.save!
  rescue => exception
    Honeybadger.notify(exception)
  end
end
