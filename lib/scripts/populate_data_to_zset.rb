require 'csv'

failed_user_ids = []
update_special_offer_data = CSV.parse(File.read('lib/scripts/x.csv'), headers: true)
user_data = update_special_offer_data.map { |row| [row["user_id"].to_i, row["no_of_days"]&.to_i] }
missing_users = user_data.select { |user_id, no_of_days| user_id.nil? || no_of_days.nil? }
failed_user_ids += missing_users.map(&:first)
final_users = user_data.reject { |user_id, _| failed_user_ids.include?(user_id) }
final_users.each_slice(1000) do |batch|
  user_ids = batch.map(&:first)
  existing_charges = SubscriptionCharge.where(charge_amount: 1, status: 'success', user_id: user_ids).pluck(:user_id)
  failed_user_ids += existing_charges

  valid_users = batch.reject { |user_id, _| failed_user_ids.include?(user_id) }
  redis_data = valid_users.map do |user_id, no_of_days|
    expire_time = Time.current.end_of_day.to_i + (no_of_days * 86_400)
    [expire_time, user_id]
  end
  
  $redis.zadd(Constants.premium_1_rs_user_redis_key, redis_data) unless redis_data.empty?
end
