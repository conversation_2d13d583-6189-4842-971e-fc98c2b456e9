known_faulty_user_ids = [1389068, 1299953, 565397, 180124, 950975, 956121, 163694, 693438, 1238597, 789190, 1300518, 116496, 767789, 276148]
error_user_ids = []
count = 0
UserPlan.where.not(source: nil)
  .find_each do |user_plan|
  next if known_faulty_user_ids.include? user_plan.user_id
  next if user_plan.source&.include?('charge-refund-') && user_plan.end_date < Time.zone.now
  next if (user_plan.user.internal || user_plan.user.is_test_user?)
  latest_log = UserPlanLog.where(user_id: user_plan.user_id).order(end_date: :desc).first
  has_issue = (((user_plan.end_date.end_of_day - (latest_log&.end_date&.end_of_day || Time.zone.parse("2024-01-01 00:00:00"))).abs > 1.day && user_plan.end_date < Time.zone.now) ||
        user_plan.end_date != latest_log&.end_date && user_plan.end_date > Time.zone.now)
  error_user_ids << user_plan.user_id if has_issue
  count += 1
  sleep(0.5) if count % 100 == 0
  break if error_user_ids.count > 3
end;0

error_user_ids
