GRACE_PERIOD_START_DATE = Time.zone.parse("2024-11-08 00:00:00")
LOGS_DEPLOY_TIME = Time.zone.parse('06-12-2024 18:21:58')

UserPlan
  .where("updated_at > ?", GRACE_PERIOD_START_DATE).find_each do |user_plan|
  versions = PaperTrail::Version.where(item: user_plan)
              .where("created_at >= ?", GRACE_PERIOD_START_DATE)
              .where("created_at <= ?", LOGS_DEPLOY_TIME)
              .order(created_at: :asc)

  logs = []

  # puts "User ID: #{user_plan.user_id}"
  if versions.blank? && user_plan.updated_at < LOGS_DEPLOY_TIME
    source = user_plan.source
    if source.present? && source.match?(/^charge-(\d+)$/)
      charge_id = source.match(/^charge-(\d+)$/)[1].to_i
      charge = SubscriptionCharge.find(charge_id)
      end_date = user_plan.end_date
      start_date = (end_date - charge.subscription.plan.duration_in_months.months).beginning_of_day
      logs << {
        :start_date => start_date,
        :end_date => end_date,
        :entity => charge,
        :plan_id => charge.subscription.plan_id
      } if charge.status == 'success'
    elsif source.present? && source.match?(/^referral-(\d+)$/)
      referral_id = source.match(/^referral-(\d+)$/)[1].to_i
      referral = UserReferral.find(referral_id)
      end_date = user_plan.end_date
      start_date = (end_date - 1.month).beginning_of_day
      logs << {
        :start_date => start_date,
        :end_date => end_date,
        :entity => referral,
        :plan_id => user_plan.plan_id
      }
    elsif source.present?
      # puts "Unknown source format: #{source}"
    end
  elsif versions.count == 1 && versions.first.event == 'create'
    current = versions.first.reify || user_plan
    source = current.source
    if source.present? && source.match?(/^charge-(\d+)$/)
      charge_id = source.match(/^charge-(\d+)$/)[1].to_i
      charge = SubscriptionCharge.find(charge_id)
      end_date = current.end_date
      start_date = (end_date - charge.subscription.plan.duration_in_months.months).beginning_of_day
      logs << {
        :start_date => start_date,
        :end_date => end_date,
        :entity => charge,
        :plan_id => charge.subscription.plan_id
      } if charge.status == 'success'
    elsif source.present? && source.match?(/^referral-(\d+)$/)
      referral_id = source.match(/^referral-(\d+)$/)[1].to_i
      referral = UserReferral.find(referral_id)
      end_date = current.end_date
      start_date = (end_date - 1.month).beginning_of_day
      logs << {
        :start_date => start_date,
        :end_date => end_date,
        :entity => referral,
        :plan_id => user_plan.plan_id
      }
    elsif source.present?
      # puts "Unknown source format: #{source}"
    end
  else
    versions.each do |version|
      next if version.event == 'create'
      previous = version.reify
      current = version.next&.reify || user_plan

      next if current.updated_at < GRACE_PERIOD_START_DATE
      next if previous.source == current.source

      source = current.source
      if source.present? && source.match?(/^charge-(\d+)$/)
        charge_id = source.match(/^charge-(\d+)$/)[1].to_i
        charge = SubscriptionCharge.find(charge_id)
        end_date = current.end_date
        start_date = (end_date - charge.subscription.plan.duration_in_months.months).beginning_of_day
        logs << {
          :start_date => start_date,
          :end_date => end_date,
          :entity => charge,
          :plan_id => charge.subscription.plan_id
        } if charge.status == 'success'
      elsif source.present? && source.match?(/^referral-(\d+)$/)
        referral_id = source.match(/^referral-(\d+)$/)[1].to_i
        referral = UserReferral.find(referral_id)
        end_date = current.end_date
        start_date = (end_date - 1.month).beginning_of_day
        logs << {
          :start_date => start_date,
          :end_date => end_date,
          :entity => referral,
          :plan_id => user_plan.plan_id
        }
      elsif source.present?
        # puts "Unknown source format: #{source}"
      end
    end
  end

  # logs.each do |log|
  #   puts "start_date: #{log[:start_date]}, end_date: #{log[:end_date]}, entity: #{log[:entity].class}-#{log[:entity].id}"
  # end
  # puts "----------------------------"
  entries = logs
      .reject { |log| UserPlanLog.where(entity: log[:entity]).exists? }
      .map { |log|
    UserPlanLog.new(
      user_id: user_plan.user_id,
      plan_id: log[:plan_id],
      start_date: log[:start_date],
      end_date: log[:end_date],
      entity: log[:entity]
    )
  }
  # puts "Entries: #{entries.count}"
  # puts "---------------------"
  UserPlanLog.import(entries)
  sleep(0.01)
end;0

