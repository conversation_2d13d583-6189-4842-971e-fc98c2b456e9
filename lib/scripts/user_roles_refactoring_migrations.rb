last_id = 1
failed_cases = []
failed_user_ids = []
failed_role_ids = []
failed_inactive_user_role_ids = []
failed_inactive_user_role_cases = []
UserRole.where("id >= ?", last_id).each do |user_role|
  next if user_role.blank?
  begin
    # to populate parent_circle_id and purview_circle_id
    if user_role.role.parent_circle_id.blank?
      primary_circle = Circle.find_by(id: user_role.primary_circle_id)
      if primary_circle.present? && Circle.level_options_for("location").include?(primary_circle.level.to_sym)
        user_role.parent_circle_id = user_role.secondary_circle_id
        user_role.purview_circle_id = user_role.primary_circle_id
      else
        user_role.parent_circle_id = user_role.primary_circle_id
        user_role.purview_circle_id = user_role.secondary_circle_id
      end
    else
      primary_circle = Circle.find_by(id: user_role.primary_circle_id)
      if primary_circle.present? && Circle.level_options_for("location").include?(primary_circle.level.to_sym)
        user_role.parent_circle_id = nil
        user_role.purview_circle_id = user_role.primary_circle_id
      else
        user_role.parent_circle_id = nil
        user_role.purview_circle_id = user_role.secondary_circle_id
      end
    end

    # to populate badge icon id
    if user_role.role.has_badge_icon
      circle_id = nil
      badge_icon_id = nil
      get_badge_color = user_role.badge_color.blank? ? user_role.role.badge_color : user_role.badge_color
      get_badge_icon_ribbon = user_role.badge_icon_ribbon.blank? ? user_role.role.badge_icon_ribbon : user_role.badge_icon_ribbon
      badge_icon_color = user_role.get_badge_icon_color(get_badge_color, get_badge_icon_ribbon)
      if user_role.role.primary_circle_id_badge_icon?
        circle_id = user_role.primary_circle_id
      elsif user_role.role.secondary_circle_id_badge_icon?
        circle_id = user_role.secondary_circle_id
      elsif user_role.role.badge_icon_group_id_badge_icon?
        badge_icon_id = BadgeIcon.where(badge_icon_group_id: user_role.role.badge_icon_group_id, color:
          badge_icon_color).first.id
      end
      if circle_id.present?
        badge_icon_id = BadgeIconGroup.includes(:badge_icons).where(circle_id: circle_id, badge_icons: { color: badge_icon_color })&.first&.badge_icons&.first&.id
      end
      user_role.badge_icon_id = badge_icon_id
    end
    user_role.save!
  rescue => e
    if user_role.active == false
      failed_inactive_user_role_ids << user_role.id
      failed_inactive_user_role_cases << "user_role_id: #{user_role.id}, error: #{e}"
    else
      failed_role_ids << user_role.role_id
      failed_cases << "user_role_id: #{user_role.id}, error: #{e}"
      failed_user_ids << user_role.user_id
    end
  end
end; 0




