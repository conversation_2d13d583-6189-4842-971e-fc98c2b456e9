require 'rails_helper'

RSpec.describe Toast, type: :model do
  describe 'Toast' do
    context '#is_district_circle_id?' do
      it 'should return true if circle_id is 0' do
        toast = FactoryBot.create(:toast, circle_id: 0)
        expect(toast).to be_valid
      end

      it 'should return true if circle is district level' do
        state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
        toast = FactoryBot.create(:toast, circle_id: circle.id)
        expect(toast).to be_valid
      end

      it 'should return false if circle is not district level' do
        circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @toast = FactoryBot.build(:toast, circle_id: circle.id)
        expect{ @toast.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Circle Circle can only be district")
      end
    end
  end
end
