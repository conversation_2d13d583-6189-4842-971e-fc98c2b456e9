require 'rails_helper'

RSpec.describe Video, type: :model do
  describe "video record" do
    context "service being gcp" do
      before :each do
        # As gcp service data comes from APP, we made this flow wrt to data attribute
        # So any video create in terms of gcp should happen wrt data attribute
        video =
          {
            "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
            "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "original_file_name": "sample_video.mp4",
            "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "thumbnail": {
              "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
              "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
            }
          }

        @video = FactoryBot.create(:video, ms_data: video, service: :gcp)
      end

      it "doesn't update if makes service null" do
        @video.service = nil
        expect { @video.presence_of_service }.to raise_error(RuntimeError, "Video service is blank")
      end

      it "doesn't update if makes url null" do
        @video.source_url = nil
        expect { @video.presence_of_url }.to raise_error(RuntimeError, "Video url is blank")
      end

      it "doesn't update if makes path null" do
        @video.path = nil
        expect { @video.presence_of_path }.to raise_error(RuntimeError, "Video path is blank")
      end

      it "gives default thumbnail url if video thumbnail_url is nil" do
        @video.thumbnail_url = nil
        @video.save!
        expect(@video.get_thumbnail_url).to eq(Constants.default_video_thumbnail_url)
      end

      it "gives default thumbnail url if video thumbnail_path is nil" do
        @video.thumbnail_path = nil
        @video.save!
        expect(@video.get_thumbnail_url).to eq(Constants.default_video_thumbnail_url)
      end

      it "gives default thumbnail url if video thumbnail_url and thumbnail_path are nil" do
        @video.thumbnail_url = nil
        @video.thumbnail_path = nil
        @video.save!
        expect(@video.get_thumbnail_url).to eq(Constants.default_video_thumbnail_url)
      end
    end

    context "service being aws" do
      before :each do
        @video = FactoryBot.create(:video)
      end

      it "doesn't update if makes service null" do
        @video.service = nil
        expect { @video.presence_of_service }.to raise_error(RuntimeError, "Video service is blank")
      end

      it "does update if makes path null" do
        @video.path = nil
        @video.save!
        expect(@video.path).to be_nil
      end

      it "gives default thumbnail url if video thumbnail_url is nil" do
        @video.thumbnail_url = nil
        @video.save!
        expect(@video.get_thumbnail_url).to eq(Constants.default_video_thumbnail_url)
      end

      it "gives thumbnail url even if video thumbnail_path is nil" do
        @video.thumbnail_path = nil
        @video.save!
        expect(@video.get_thumbnail_url).to eq(@video.thumbnail_url)
      end
    end
  end

  describe "video" do
    context "#upload" do
      it "calls upload_v2 method if user_id id 5" do
        user = FactoryBot.create(:user)
        allow(user).to receive(:id).and_return(5)
        allow(Video).to receive(:upload_v2)

        Video.upload("video.mp4", "thumbnail.jpg", user)
        expect(Video).to have_received(:upload_v2)
      end

      it "calls upload_v1 method if user_id id is not 5 or 50" do
        user = FactoryBot.create(:user)
        allow(user).to receive(:id).and_return(10)
        allow(Video).to receive(:upload_v1)

        Video.upload("video.mp4", "thumbnail.jpg", user)
        expect(Video).to have_received(:upload_v1)
      end
    end

    context "#upload_new_v2" do
      let(:user) { FactoryBot.create(:user) }
      let(:s3_resource_mock) { instance_double(Aws::S3::Resource) }
      let(:s3_bucket_mock) { instance_double(Aws::S3::Bucket) }
      let(:s3_object_mock) { instance_double(Aws::S3::Object) }
      let(:video) { fixture_file_upload("app/assets/videos/sample_video.mp4", "video/mp4") }
      let(:thumbnail) { fixture_file_upload("app/assets/images/logo.png", "image/png") }

      it "raises exception if video_object is no nil" do
        # Stubbing the credentials
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test_access_key_id")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test_secret_access_key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test_bucket_name")

        # Stubbing the MD5 hash
        allow(Digest::MD5).to receive(:hexdigest).and_return("hashed-logo")

        # Stubbing the S3 upload
        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)
        allow(s3_resource_mock).to receive(:bucket).with("test_bucket_name").and_return(s3_bucket_mock)
        allow(s3_bucket_mock).to receive(:object).and_return(s3_object_mock)
        allow(s3_object_mock).to receive(:put).and_raise(StandardError)
        allow(Honeybadger).to receive(:notify)

        expect { FactoryBot.create(:video, blob_data: video, thumbnail: thumbnail) }.to raise_error(StandardError)
        expect(Honeybadger).to have_received(:notify)
      end
    end

    context "#get_s3_resource" do
      it "returns s3 resource" do
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test_aws_access_key_id")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test_aws_secret_access_key")

        expect(Video.get_s3_resource).to be_a(Aws::S3::Resource)
      end
    end

    context "#upload_v1" do
      let(:user) { FactoryBot.create(:user) }
      let(:s3_resource_mock) { instance_double(Aws::S3::Resource) }
      let(:s3_bucket_mock) { instance_double(Aws::S3::Bucket) }
      let(:s3_object_mock) { instance_double(Aws::S3::Object) }
      let(:video) { fixture_file_upload("app/assets/videos/sample_video.mp4", "video/mp4") }
      let(:thumbnail) { fixture_file_upload("app/assets/images/logo.png", "image/png") }

      before :each do
        # Stubbing the credentials
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test_access_key_id")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test_secret_access_key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test_bucket_name")

        # Stubbing the MD5 hash
        allow(Digest::MD5).to receive(:hexdigest).and_return("hashed-logo")

        # Stubbing the S3 upload
        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)
        allow(s3_resource_mock).to receive(:bucket).with("test_bucket_name").and_return(s3_bucket_mock)
        allow(s3_bucket_mock).to receive(:object).and_return(s3_object_mock)
        allow(s3_object_mock).to receive(:put)
      end

      it "returns nil if raises exception while thumbnail setting" do
        allow(s3_object_mock).to receive(:put).and_raise(StandardError)
        expect(Video.upload_v1(video, thumbnail, user)).to be_nil
      end

      it "returns nil if raises exception while video setting" do
        allow(Video).to receive(:new).and_raise(StandardError)
        expect(Video.upload_v1(video, thumbnail, user)).to be_nil
      end

      it "creates new video record" do
        allow(Video).to receive(:new)
        Video.upload_v1(video, thumbnail, user)
        expect(Video).to have_received(:new)
      end
    end

    context "#upload_v2" do
      let(:user) { FactoryBot.create(:user) }
      let(:s3_resource_mock) { instance_double(Aws::S3::Resource) }
      let(:s3_bucket_mock) { instance_double(Aws::S3::Bucket) }
      let(:s3_object_mock) { instance_double(Aws::S3::Object) }
      let(:video) { fixture_file_upload("app/assets/videos/sample_video.mp4", "video/mp4") }
      let(:thumbnail) { fixture_file_upload("app/assets/images/logo.png", "image/png") }

      before :each do
        # Stubbing the credentials
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test_access_key_id")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test_secret_access_key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test_bucket_name")

        # Stubbing the MD5 hash
        allow(Digest::MD5).to receive(:hexdigest).and_return("hashed-logo")

        # Stubbing the S3 upload
        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)
        allow(s3_resource_mock).to receive(:bucket).and_return(s3_bucket_mock)
        allow(s3_bucket_mock).to receive(:object).and_return(s3_object_mock)
        allow(s3_object_mock).to receive(:put)
      end

      it "returns nil if raises exception while thumbnail setting" do
        allow(s3_object_mock).to receive(:put).and_raise(StandardError)
        expect(Video.upload_v2(video, thumbnail, user)).to be_nil
      end

      it "returns nil if raises exception while video setting" do
        allow(Video).to receive(:new).and_raise(StandardError)
        expect(Video.upload_v2(video, thumbnail, user)).to be_nil
      end

      it "creates new video record" do
        allow(Video).to receive(:new)
        Video.upload_v2(video, thumbnail, user)
        expect(Video).to have_received(:new)
      end
    end
  end
end
