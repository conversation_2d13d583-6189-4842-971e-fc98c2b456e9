require 'rails_helper'

RSpec.describe UserPosterLayout, type: :model do
  describe UserPosterLayout, :type => :model do
    context 'validate UserPosterLayout' do
      before :each do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, h1_count: 0, h2_count: 0)
      end
      it 'should be valid' do
        expect(@user_poster_layout).to be_valid
      end
      it 'should be invalid without h1_count' do
        @user_poster_layout.h1_count = nil
        expect(@user_poster_layout).to_not be_valid
      end
      it 'should be invalid without h2_count' do
        @user_poster_layout.h2_count = nil
        expect(@user_poster_layout).to_not be_valid
      end
    end
  end

  describe '#validate_user_poster_layout' do
    context 'validate layout' do
      before :each do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, h1_count: 0, h2_count: 0)
      end

      it 'should be valid' do
        expect(@user_poster_layout).to be_valid
      end

      it 'should be invalid with layout not supported' do
        @user_poster_layout.h1_count = 3
        @user_poster_layout.h2_count = 2
        expect(@user_poster_layout).to_not be_valid
      end
    end
  end

  describe '#set_wati_msg_key_on_layout_creation' do
    context 'when entity is a User and has never subscribed' do
      it 'sends a Wati message' do
        user = FactoryBot.create(:user)
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).and_return(false)
        FactoryBot.create(:user_poster_layout, entity: user)
        expect(UserMetadatum.where(user_id: user.id, key: Constants.send_wati_msg_key).exists?).to be_truthy
      end
    end

    context 'when entity is a User and has subscribed' do
      it 'does not send a Wati message' do
        user = FactoryBot.create(:user)
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).and_return(true)
        FactoryBot.build(:user_poster_layout, entity: user)
        expect(UserMetadatum.where(user_id: user.id, key: Constants.send_wati_msg_key).exists?).to be_falsey
      end
    end

    context 'when entity is not a User' do
      it 'does not send a Wati message' do
        non_user_entity = FactoryBot.create(:circle)
        FactoryBot.build(:user_poster_layout, entity: non_user_entity)
        expect(UserMetadatum.where(user_id: non_user_entity.id, key: Constants.send_wati_msg_key).exists?)
          .to be_falsey
      end
    end
  end

  describe "#get_latest_remark_of_layout" do
    let(:user_poster_layout) { create(:user_poster_layout) }

    it "returns the latest remark if remarks are present" do
      FactoryBot.create(:poster_layout_remark, user_poster_layout: user_poster_layout, remarks: "First remark")
      FactoryBot.create(:poster_layout_remark, user_poster_layout: user_poster_layout, remarks: "Latest remark")
      result = user_poster_layout.get_latest_remark_of_layout
      expect(result).to eq("Latest remark")
    end

    it "returns nil if no remarks are present" do
      result = user_poster_layout.get_latest_remark_of_layout
      expect(result).to be_nil
    end
  end
end
