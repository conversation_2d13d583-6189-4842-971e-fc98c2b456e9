require 'rails_helper'

RSpec.describe PollOption, type: :model do
  describe 'poll' do
    before :each do
      @post = FactoryBot.create(:post)
      @poll = FactoryBot.create(:poll, post: @post)
      @poll_option = FactoryBot.create(:poll_option, poll: @poll)

      @user_poll_option = FactoryBot.create(:user_poll_option, poll: @poll, poll_option: @poll_option, user: @post.user)
    end

    context 'votes_count' do
      it 'returns the number of votes' do
        expect(@poll_option.votes_count).to eq(1)
      end
    end

    context 'is_selected' do
      it 'returns true if the user has selected the poll option' do
        expect(@poll_option.is_selected(@post.user)).to eq(true)
      end

      it 'returns false if the user has not selected the poll option' do
        user = FactoryBot.create(:user)
        expect(@poll_option.is_selected(user)).to eq(false)
      end
    end
  end
end
