require 'rails_helper'

RSpec.describe User, type: :model do
  it 'should return user last login time' do
    @user = FactoryBot.create(:user)
    last_login_time = FactoryBot.create(:metadatum, entity: @user, key: Constants.last_login_time_key,
                                        value: Time.zone.now).value
    expect(@user.get_last_login_time).to eq(last_login_time)
  end

  it 'should return user last contacts uploaded time' do
    @user = FactoryBot.create(:user)
    last_contacts_uploaded_time = FactoryBot.create(:metadatum, entity: @user, key: Constants.last_contacts_uploaded_time_key,
                                                    value: Time.zone.now).value
    expect(@user.get_last_contacts_uploaded_time).to eq(last_contacts_uploaded_time)
  end

  it 'should return show_contacts_screen value' do
    @user = FactoryBot.create(:user)
    FactoryBot.create(:metadatum, entity: @user, key: Constants.last_login_time_key,
                      value: Time.zone.now - 7.hours).value
    FactoryBot.create(:metadatum, entity: @user, key: Constants.last_contacts_uploaded_time_key,
                      value: Time.zone.now - 7.hours).value
    Current.app_version = Gem::Version.new('2304.01.01')
    5.times do
      fake_number = "91#{Faker::Number.unique.number(digits: 8)}"
      @user_contact = FactoryBot.create(:user,
                                        phone: fake_number.to_i)

      @user_contact_suggestion = FactoryBot.create(:user_contact_suggestion, name: @user_contact.name, phone:
        @user_contact.phone,
                                                   user: @user, phone_user_id: @user_contact.id)
    end
    expect(@user.should_show_follow_contacts).to eq(true)
  end

  it 'should return show_contacts_screen value as nil if poster present' do
    @user = FactoryBot.create(:user)
    FactoryBot.create(:metadatum, entity: @user, key: Constants.last_login_time_key,
                      value: Time.zone.now).value
    FactoryBot.create(:metadatum, entity: @user, key: Constants.last_contacts_uploaded_time_key,
                      value: Time.zone.now).value
    Current.app_version = Gem::Version.new('2304.01.01')
    @party_circle = FactoryBot.create(:circle)
    FactoryBot.create(:user_circle, user: @user, circle: @party_circle)
    # send photo data to pass validations
    normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
    @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo), leader_photo_ring_color: :light)])
    expect(@user.should_show_follow_contacts).to eq(nil)
  end

  it 'should return show_contacts_screen value as nil if last login time is not less than uploaded_contacts' do
    @user = FactoryBot.create(:user)
    FactoryBot.create(:metadatum, entity: @user, key: Constants.last_login_time_key,
                      value: Time.zone.now).value
    FactoryBot.create(:metadatum, entity: @user, key: Constants.last_contacts_uploaded_time_key,
                      value: 5.minutes.ago).value
    Current.app_version = Gem::Version.new('2304.01.01')
    expect(@user.should_show_follow_contacts).to eq(nil)
  end

  describe "blocking & unblocking for tagging" do
    context "user is unblocked for tagging" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "block_for_commenting should return true" do
        expect(@user.block_for_tagging).to be_truthy
      end
      it "unblock_for_commenting should return false" do
        expect(@user.unblock_for_tagging).to be_falsey
      end
      it "is_blocked_for_tagging? should return false" do
        expect(@user.is_blocked_for_tagging?).to be_falsey
      end
    end
    context "user is blocked for tagging" do
      before :each do
        @user = FactoryBot.create(:user)
        @user.block_for_tagging
      end
      it "unblock_for_commenting should return true" do
        expect(@user.unblock_for_tagging).to be_truthy
      end
      it "block_for_commenting should return false" do
        expect(@user.block_for_tagging).to be_falsey
      end
      it "is_blocked_for_tagging? should return true" do
        expect(@user.is_blocked_for_tagging?).to be_truthy
      end
    end
  end

  describe "contact notification worker method" do
    context "trigger it when pass the requirements" do
      it "should trigger contact notification worker when app_version is > 2304.01.0" do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2304.01.01')
        @user.trigger_contact_notification_worker
        expect(SendContactNotification.jobs.size).to eq(1)
      end

      it "should not trigger contact notification worker when app_version is < 2304.01.0" do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2304.01.0')
        @user.trigger_contact_notification_worker
        expect(SendContactNotification.jobs.size).to eq(0)
      end
    end
  end

  describe "get_user_from_jwt_token" do
    context "check user is present or not" do
      it "should return user when user is present" do
        @user = FactoryBot.create(:user)
        @token = @user.generate_jwt_token
        expect(User.get_user_from_jwt_token(@token)).to eq(@user)
      end
      it "should return nil when token is not present" do
        @user = FactoryBot.create(:user)
        @token = nil
        expect(User.get_user_from_jwt_token(@token)).to eq(nil)
      end
    end
  end

  describe "user follow" do
    context "check user follow" do
      before :each do
        @user = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user)
      end

      it "should return true when user following is saved with source_of_follow" do
        expect(@user.follow(@user2.id, "members_tab")).to eq(true)
      end

      it "should return true when user following is saved without source_of_follow" do
        expect(@user.follow(@user2.id, nil)).to eq(true)
      end

      it "should return false when source_of_follow is unknown" do
        expect(@user.follow(@user2.id, "unknown")).to eq(false)
      end

      it "should return false when user does not exists" do
        expect(@user.follow(100, "members_tab")).to eq(false)
      end

      it "should return false if user blocked other user" do
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @user2)
        expect(@user.follow(@user2.id, "members_tab")).to eq(false)
      end

      it "should return false if user is blocked by other user" do
        FactoryBot.create(:blocked_user, user: @user2, blocked_user: @user)
        expect(@user.follow(@user2.id, "members_tab")).to eq(false)
      end
    end
  end

  describe "check set_contact_screen_shown_count_from_redis" do
    context "check contact screen shown count value" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "should return 0 if we don't set the contact screen shown count" do
        expect(@user.get_contact_screen_shown_count_of_user).to eq(0)
      end
      it "should return count after calling the set contact screen show count" do
        @user.set_contact_screen_shown_count_from_redis
        expect(@user.get_contact_screen_shown_count_of_user).to eq(1)
      end
      it "should return count after calling the set contact screen show count twice" do
        @user.set_contact_screen_shown_count_from_redis
        @user.set_contact_screen_shown_count_from_redis
        expect(@user.get_contact_screen_shown_count_of_user).to eq(2)
      end
    end
  end

  it 'should fetch all the blocked user ids of a user' do
    @user = FactoryBot.create(:user)
    @blocked_user = FactoryBot.create(:user)
    FactoryBot.create(:blocked_user, user: @user, blocked_user: @blocked_user)
    expect(@user.get_blocked_user_ids.include?(@blocked_user.id)).to eq(true)
  end

  it 'should fetch all the blocked by user ids of a user' do
    @user = FactoryBot.create(:user)
    @blocked_user = FactoryBot.create(:user)
    FactoryBot.create(:blocked_user, user: @user, blocked_user: @blocked_user)
    expect(@blocked_user.get_blocked_by_user_ids.include?(@user.id)).to eq(true)
  end

  describe "#eligible_for_monthly_campaign_for_yearly_users?" do
    it 'returns true when user is in the Redis zset with future expiry' do
      user = FactoryBot.create(:user)
      future_time = (Time.zone.now + 1.day).to_i
      $redis.zadd(Constants.monthly_campaign_for_yearly_users_key, future_time, user.id)
      expect(user.eligible_for_monthly_campaign_for_yearly_users?).to eq(true)
      # Clean up
      $redis.zrem(Constants.monthly_campaign_for_yearly_users_key, user.id)
    end

    it 'returns false when user is in the Redis zset with expired time' do
      user = FactoryBot.create(:user)
      past_time = (Time.zone.now - 1.day).to_i
      $redis.zadd(Constants.monthly_campaign_for_yearly_users_key, past_time, user.id)
      expect(user.eligible_for_monthly_campaign_for_yearly_users?).to eq(false)
      # Clean up
      $redis.zrem(Constants.monthly_campaign_for_yearly_users_key, user.id)
    end

    it 'returns false when user is not in the Redis zset' do
      user = FactoryBot.create(:user)
      expect(user.eligible_for_monthly_campaign_for_yearly_users?).to eq(false)
    end
  end

  describe "#get_suggested_contacts_feed_object" do
    let(:user) { FactoryBot.create(:user) }
    let(:user2) { FactoryBot.create(:user) }
    let(:user3) { FactoryBot.create(:user) }
    let(:user4) { FactoryBot.create(:user) }
    let(:user5) { FactoryBot.create(:user) }
    let(:user6) { FactoryBot.create(:user) }

    context "when new suggested users list is enabled" do
      it "should return the users and users_count if enabled contacts screen" do
        AppVersionSupport.new('2305.04.01')
        allow_any_instance_of(User).to receive(:should_show_contact_suggestions?).and_return(true)
        allow_any_instance_of(User).to receive(:not_yet_followed_signed_up_user_ids_of_user_contacts).and_return([user2.id, user3.id, user4.id, user5.id, user6.id])

        result = user.get_suggested_contacts_feed_object
        expect(result[:feed_item_id]).to eq("CONTACTS_LIST")
        expect(result[:users].count).to eq(5)
        expect(result[:users][0][:id]).to eq(user2.id)
        expect(result[:users][1][:id]).to eq(user3.id)
        expect(result[:users][2][:id]).to eq(user4.id)
        expect(result[:users][3][:id]).to eq(user5.id)
        expect(result[:users][4][:id]).to eq(user6.id)
        expect(result[:users][0].length).to eq(6)
        expect(result[:users_count].present?).to be_truthy
        expect(result[:users_count]).to eq(0)
      end
    end

    context "when new suggested users list is disabled" do
      it "should return the users" do
        AppVersionSupport.new('2305.03.01')
        allow_any_instance_of(User).to receive(:should_show_contact_suggestions?).and_return(true)
        allow_any_instance_of(User).to receive(:not_yet_followed_signed_up_user_ids_of_user_contacts).and_return([user2.id, user3.id, user4.id, user5.id, user6.id])

        result = user.get_suggested_contacts_feed_object
        expect(result[:feed_item_id]).to eq("CONTACTS_LIST")
        expect(result[:users].count).to eq(5)
        expect(result[:users][0][:id]).to eq(user2.id)
        expect(result[:users][1][:id]).to eq(user3.id)
        expect(result[:users][2][:id]).to eq(user4.id)
        expect(result[:users][3][:id]).to eq(user5.id)
        expect(result[:users][4][:id]).to eq(user6.id)
        expect(result[:sub_header].blank?).to be_truthy
        expect(result[:users][0].is_a?(ActiveRecord::Base)).to be_truthy
      end
    end

    it "should return nil if disabled contacts screen" do
      AppVersionSupport.new('2305.04.01')
      allow_any_instance_of(User).to receive(:should_show_contact_suggestions?).and_return(false)
      allow_any_instance_of(User).to receive(:not_yet_followed_signed_up_user_ids_of_user_contacts).and_return([user2.id, user3.id, user4.id, user5.id, user6.id])

      result = user.get_suggested_contacts_feed_object
      expect(result).to eq([])
    end
  end

  describe "#generate_unique_otp & validate otp" do
    context "praja account" do
      before :each do
        @user = User.find_by_phone(Constants.praja_account_phone)
        @user = User.find_by_phone(Constants.praja_account_phone) if @user.nil?
        @counter, @otp_str = @user.get_unique_otp_with_counter
      end

      it "counter is 1" do
        expect(@counter).to eq(1)
      end
      it "no counter increment on regenerate" do
        counter_new, _ = @user.get_unique_otp_with_counter
        expect(counter_new).to eq(1)
      end
      it "returns 6 digit otp" do
        expect(@otp_str).to be_present
        expect(@otp_str.length).to eq(6)
      end
      it "validates otp if correct otp is sent" do
        valid = @user.validate_otp(@otp_str)
        expect(valid).to eq(true)
      end
      it "validation fails if wrong otp is sent" do
        valid = @user.validate_otp((@otp_str.to_i + 1).to_s.rjust(6, "0"))
        expect(valid).to eq(false)
      end
    end

    context "app store account" do
      before :each do
        @user = User.find_by_phone(Constants.app_store_account_phone)
        @user = FactoryBot.create(:user, phone: Constants.app_store_account_phone) if @user.nil?
        @counter, @otp_str = @user.get_unique_otp_with_counter
      end
      it "counter is 1" do
        expect(@counter).to eq(1)
      end
      it "no counter increment on regenerate" do
        counter_new, _ = @user.get_unique_otp_with_counter
        expect(counter_new).to eq(1)
      end
      it "returns 6 digit otp" do
        expect(@otp_str).to be_present
        expect(@otp_str.length).to eq(5)
      end
      it "returns OTP from credentials" do
        expect(@otp_str).to be_present
        expect(@otp_str).to eq("12345")
      end
      it "validates otp if correct otp is sent" do
        valid = @user.validate_otp(@otp_str)
        expect(valid).to eq(true)
      end
      it "validation fails if wrong otp is sent" do
        valid = @user.validate_otp("12346")
        expect(valid).to eq(false)
      end
    end

    context "fantom account" do
      before :each do
        @user = FactoryBot.create(:user, phone: "20#{Faker::Number.unique.number(digits: 8)}".to_i)
        @counter, @otp_str = @user.get_unique_otp_with_counter
      end
      it "counter is 1" do
        expect(@counter).to eq(1)
      end
      it "no counter increment on regenerate" do
        counter_new, _ = @user.get_unique_otp_with_counter
        expect(counter_new).to eq(1)
      end
      it "returns 6 digit otp" do
        expect(@otp_str).to be_present
        expect(@otp_str.length).to eq(6)
      end
      it "validates otp if correct otp is sent" do
        valid = @user.validate_otp(@otp_str)
        expect(valid).to eq(true)
      end
      it "validation fails if wrong otp is sent" do
        valid = @user.validate_otp((@otp_str.to_i + 1).to_s.rjust(6, "0"))
        expect(valid).to eq(false)
      end
    end

    context "normal user account" do
      before :each do
        @user = FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}".to_i)
        @counter, @otp_str = @user.get_unique_otp_with_counter
      end
      it "counter is 1" do
        expect(@counter).to eq(1)
      end
      it "counter is incremented on regenerate" do
        counter_new, _ = @user.get_unique_otp_with_counter
        expect(counter_new).to eq(2)
      end
      it "returns 5 digit otp" do
        expect(@otp_str).to be_present
        expect(@otp_str.length).to eq(5)
      end
      it "otp expires in 10 minutes" do
        remaining_time = $redis.ttl(@user.otp_key)
        # Check that the remaining time is within a reasonable range
        # of the original expiration time. You might need to adjust
        # the delta based on the precision you need.
        expect(remaining_time).to be_within(3).of(600)
      end
      it "validates otp if correct otp is sent" do
        valid = @user.validate_otp(@otp_str)
        expect(valid).to eq(true)
      end
      it "validation fails if wrong otp is sent" do
        valid = @user.validate_otp((@otp_str.to_i + 1).to_s.rjust(5, "0"))
        expect(valid).to eq(false)
      end
    end
  end

  describe "#get_user_joined_state_level_leader_ids" do
    context "get user joined state level leader ids" do
      it "returns user joined state level leader ids" do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 100,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user, state_id: @state_level_circle.id)
        @state_level_leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @state_level_leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @normal_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])

        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader1.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)
        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader2.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader2.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @normal_leader.id)

        joined_state_level_leader_ids = @user.get_user_joined_state_level_leader_ids
        expect(joined_state_level_leader_ids).to be_present
        expect(joined_state_level_leader_ids.count).to eq(2)
        expect(joined_state_level_leader_ids).to include(@state_level_leader1.id, @state_level_leader2.id)
      end
    end
  end

  describe "#get_user_joined_leader_ids" do
    context "get user joined leader ids" do
      it "returns user joined leader ids" do
        @user = FactoryBot.create(:user)
        @leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader2.id)

        joined_leader_ids = @user.get_user_joined_leader_ids
        expect(joined_leader_ids).to be_present
        expect(joined_leader_ids.count).to eq(2)
        expect(joined_leader_ids).to include(@leader1.id, @leader2.id)
      end
    end
  end

  describe "#get_user_joined_circles_owner_ids" do
    context "get user joined circles owner ids" do
      it "returns user joined circles owner ids" do
        @user = FactoryBot.create(:user)
        @leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader2.id)

        @owner = FactoryBot.create(:user)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @owner,
                                                          circle: @leader1,
                                                          permission_group_id: Constants.owner_permission_group_id)

        joined_circles_owner_ids = @user.get_user_joined_circles_owner_ids
        expect(joined_circles_owner_ids).to be_present
        expect(joined_circles_owner_ids.count).to eq(1)
        expect(joined_circles_owner_ids).to include(@owner.id)
      end
    end
  end

  describe "#is_circle_owner?" do
    context "check if user is any circle owner" do
      it "returns true if user is any circle owner" do
        @owner = FactoryBot.create(:user)
        @leader_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @owner,
                                                          circle: @leader_circle,
                                                          permission_group_id: Constants.owner_permission_group_id)
        expect(@owner.is_circle_owner?).to eq(true)
      end
      it "returns false if user is not any circle owner" do
        @user = FactoryBot.create(:user)
        expect(@user.is_circle_owner?).to eq(false)
      end
    end
  end

  describe "#get_following_weight" do
    context "get following weight of the user for feed" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "when user has user role and following count is < 150" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id, user_id: @user.id)
        allow(@user).to receive_message_chain(:following_count).and_return(149)
        expect(@user.get_following_weight).to eq(8)
      end

      it "when user has no user role and following count is < 150" do
        allow(@user).to receive_message_chain(:following_count).and_return(149)
        expect(@user.get_following_weight).to eq(8)
      end

      it "when user has user role and following count is > 150 and < 300" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id, user_id: @user.id)
        allow(@user).to receive_message_chain(:following_count).and_return(151)
        expect(@user.get_following_weight).to eq(5)
      end

      it "when user has no user role and following count is > 150 and < 300" do
        allow(@user).to receive_message_chain(:following_count).and_return(151)
        expect(@user.get_following_weight).to eq(5)
      end

      it "when user has user role and following count is > 300  and < 450" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id, user_id: @user.id)
        allow(@user).to receive_message_chain(:following_count).and_return(301)
        expect(@user.get_following_weight).to eq(5)
      end

      it "when user has no user role and following count is > 300 and < 450" do
        allow(@user).to receive_message_chain(:following_count).and_return(301)
        expect(@user.get_following_weight).to eq(5)
      end

      it "when user has user role and following count is > 450 and < 600" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role,
                                       parent_circle_id: @parent_circle.id, user_id: @user.id)
        allow(@user).to receive_message_chain(:following_count).and_return(451)
        expect(@user.get_following_weight).to eq(1)
      end

      it "when user has no user role and following count is > 450 and < 600" do
        allow(@user).to receive_message_chain(:following_count).and_return(451)
        expect(@user.get_following_weight).to eq(3)
      end

      it "following count is > 600" do
        allow(@user).to receive_message_chain(:following_count).and_return(601)
        expect(@user.get_following_weight).to eq(1)
      end
    end
  end

  describe "#get_local_leaders_weight" do
    context "get local leaders weight of the user for feed" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "when joined leaders count is 1" do
        @leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader1.id)

        expect(@user.get_local_leaders_weight).to eq(33)
      end
      it "when joined leaders count is 2" do
        @leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader2.id)
        expect(@user.get_local_leaders_weight).to eq(11)
      end
      it "when joined leaders count is > 2" do
        @leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
        @leader3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader2.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader3.id)
        expect(@user.get_local_leaders_weight).to eq(4)
      end
      it "when joined leaders count is 0" do
        expect(@user.get_local_leaders_weight).to eq(0)
      end
    end
  end

  describe "#get_state_leaders_weight" do
    context "get state leaders weight of the user for feed" do
      it "when joined state leaders count is 1" do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 100,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user, state_id: @state_level_circle.id)
        @state_level_leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @normal_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])

        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader1.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @normal_leader.id)

        expect(@user.get_state_leaders_weight).to eq(15)
      end
      it "when joined state leaders count is 2" do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 100,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user, state_id: @state_level_circle.id)
        @state_level_leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @state_level_leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @normal_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])

        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader1.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)
        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader2.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader2.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @normal_leader.id)

        expect(@user.get_state_leaders_weight).to eq(5)
      end
      it "when joined state leaders count is > 2" do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 100,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user, state_id: @state_level_circle.id)
        @state_level_leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @state_level_leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @state_level_leader3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @normal_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])

        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader1.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)
        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader2.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)
        FactoryBot.create(:circles_relation, first_circle_id: @state_level_leader3.id, second_circle_id:
          @state_level_circle.id, relation: :Leader2State)

        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader2.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @state_level_leader3.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @normal_leader.id)

        expect(@user.get_state_leaders_weight).to eq(2)
      end
      it "when joined state leaders count is 0" do
        @user = FactoryBot.create(:user)
        expect(@user.get_state_leaders_weight).to eq(0)
      end
    end
  end

  describe "#get_party_weight" do
    context "get party weight of the user for feed" do
      it "when joined party count is 1" do
        @user = FactoryBot.create(:user)
        @party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party.id)
        expect(@user.get_party_weight).to eq(1.1)
      end
      it "when joined party count is 2" do
        @user = FactoryBot.create(:user)
        @party1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @party2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party2.id)
        expect(@user.get_party_weight).to eq(1.5)
      end
      it "when joined party count is > 2" do
        @user = FactoryBot.create(:user)
        @party1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @party2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @party3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party1.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party2.id)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party3.id)
        expect(@user.get_party_weight).to eq(1.1)
      end
      it "when joined party count is 0" do
        @user = FactoryBot.create(:user)
        expect(@user.get_party_weight).to eq(0)
      end
    end
  end
  describe "#to_honeybadger_context" do
    context "honey badger context for user" do
      it "return user_id json" do
        user = FactoryBot.create(:user)
        user_honey_badger_context = user.to_honeybadger_context

        expect(user_honey_badger_context).to eq({ user_id: user.id })
      end
    end
  end

  describe '#check_truecaller_image' do
    context "check truecaller image" do
      before :each do
        @user = FactoryBot.create(:user)
        @photo = FactoryBot.create(:photo, url: "https://storage.googleapis.com/example.jpg")
        @user.photo_id = @photo.id
      end
      it "updates photo_id to nil if response code is not 200" do
        stub_request(:any, "https://storage.googleapis.com/example.jpg").to_return(status: 404)

        expect(@user.check_truecaller_image).to be_nil
      end

      it "does not update photo_id if response code is 200" do
        stub_request(:any, "https://storage.googleapis.com/example.jpg").to_return(status: 200)

        @user.check_truecaller_image

        expect(@user.photo_id).not_to be_nil
      end

      it "does not update photo_id if photo_id is not present" do
        @user.update_column(:photo_id, nil)

        @user.check_truecaller_image

        expect(@user.photo_id).to be_nil
      end

      it "does not update photo_id if photo.url does not include 'https://storage.googleapis.com'" do
        @user.photo.update_column(:url, "https://example.com/photo.jpg")

        @user.check_truecaller_image

        expect(@user.photo_id).not_to be_nil
      end
    end
  end

  describe '#should_show_circle_suggestions?' do
    context "should show circle suggestions" do
      it "returns true if user session count < 3" do
        user = FactoryBot.create(:user)

        expect(user.should_show_circle_suggestions?.first).to eq(true)
      end
      it "returns false if user session count > 3" do
        user = FactoryBot.create(:user)
        allow(UserTokenUsage).to receive_message_chain(:where, :where, :count).and_return(4)

        expect(user.should_show_circle_suggestions?.first).to eq(false)
      end
    end
  end

  describe '#should_show_contact_suggestions?' do
    context "should show contact suggestions" do
      it "returns true if user sessions in different days < 2" do
        user = FactoryBot.create(:user)

        expect(user.should_show_contact_suggestions?).to eq(true)
      end
      it "returns false if user sessions in different days > 2" do
        user = FactoryBot.create(:user)
        marker_dates = [Time.zone.now - 1.day, Time.zone.now - 2.days, Time.zone.now - 3.days]

        allow(UserTokenUsage).to receive_message_chain(:where, :pluck).and_return(marker_dates)
        expect(user.should_show_contact_suggestions?).to eq(false)
      end
    end
  end

  describe '#should_show_badge_card' do
    context "should show badge card" do
      it "returns true if user session count < 3" do
        user = FactoryBot.create(:user)

        expect(user.should_show_badge_card?).to eq(true)
      end
      it "returns false if user session count > 2" do
        user = FactoryBot.create(:user)
        allow(UserTokenUsage).to receive_message_chain(:where, :where, :count).and_return(3)

        expect(user.should_show_badge_card?).to eq(false)
      end
    end
  end

  describe '#update_location' do
    context "update location" do
      it "updates user location" do
        user = FactoryBot.create(:user)
        user.update_location
      end
    end
  end

  describe "#get_location_circles" do
    context "get location circles" do
      it "returns location circles" do
        @user = FactoryBot.create(:user)
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)

        FactoryBot.create(:user_circle, user: @user, circle: @village_circle)
        user_joined_location_circles = @user.get_location_circles
        expect(user_joined_location_circles.first.id).to eq(@village_circle.id)
      end
    end
  end

  describe "#get_followers" do
    context "get followers of a user" do
      it "returns followers records" do
        user = FactoryBot.create(:user)
        5.times do
          follower = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: user.id, follower_id: follower.id, active: true)
        end
        followers = user.get_followers(user, 0, 10)
        expect(followers.count).to eq(5)
      end

      it "returns followers records with pagination" do
        user = FactoryBot.create(:user)
        12.times do
          follower = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: user.id, follower_id: follower.id, active: true)
        end
        followers = user.get_followers(user, 10, 2)
        expect(followers.count).to eq(2)
      end

      it "check if user is following other user" do
        user = FactoryBot.create(:user)
        logged_in_user = FactoryBot.create(:user)
        FactoryBot.create(:user_follower, user_id: user.id, follower_id: logged_in_user.id, active: true)
        5.times do
          follower = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: user.id, follower_id: follower.id, active: true)
          FactoryBot.create(:user_follower, user_id: follower.id, follower_id: logged_in_user.id, active: true)
        end
        followers = user.get_followers(logged_in_user, 0, 10)
        expect(followers.count).to eq(6)
        expect(followers.first.follows).to eq(false)
        expect(followers.last.follows).to eq(true)
      end
    end
  end

  describe "#get_following of user" do
    context "get following of a user" do
      it "returns following records" do
        user = FactoryBot.create(:user)
        5.times do
          following = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: following.id, follower_id: user.id, active: true)
        end
        following = user.get_following(user, 0, 10)
        expect(following.count).to eq(5)
      end
      it "returns all following records when offset 0 and count 0" do
        user = FactoryBot.create(:user)
        5.times do
          following = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: following.id, follower_id: user.id, active: true)
        end
        following = user.get_following(user)
        expect(following.count).to eq(5)
      end

      it "returns following records with pagination" do
        user = FactoryBot.create(:user)
        12.times do
          following = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: following.id, follower_id: user.id, active: true)
        end
        following = user.get_following(user, 10, 2)
        expect(following.count).to eq(2)
      end

      it "check if user is following other user" do
        user = FactoryBot.create(:user)
        logged_in_user = FactoryBot.create(:user)
        FactoryBot.create(:user_follower, user_id: logged_in_user.id, follower_id: user.id, active: true)
        5.times do
          following = FactoryBot.create(:user)
          FactoryBot.create(:user_follower, user_id: following.id, follower_id: user.id, active: true)
          FactoryBot.create(:user_follower, user_id: following.id, follower_id: logged_in_user.id, active: true)
        end
        following = user.get_following(logged_in_user, 0, 10)
        expect(following.count).to eq(6)
        expect(following.first.follows).to eq(false)
        expect(following.last.follows).to eq(true)
      end
    end
  end

  describe "#get_posts_count" do
    context "get posts count of a user" do
      it "returns posts count" do
        user = FactoryBot.create(:user)
        5.times do
          FactoryBot.create(:post, user_id: user.id, active: true)
        end
        posts_count = user.get_posts_count
        expect(posts_count).to eq(5)
      end
      it "returns posts count of a user which are active only" do
        user = FactoryBot.create(:user)
        5.times do
          FactoryBot.create(:post, user_id: user.id, active: true)
        end
        FactoryBot.create(:post, user_id: user.id, active: false)
        posts_count = user.get_posts_count
        expect(posts_count).to eq(5)
      end
    end
  end

  describe "#get_likes_count" do
    context "get likes count of all post" do
      it "returns likes count" do
        user = FactoryBot.create(:user)
        post = FactoryBot.create(:post, user_id: user.id, active: true)
        post1 = FactoryBot.create(:post, user_id: user.id, active: true)

        FactoryBot.create(:post_like, post_id: post.id, user_id: user.id, active: true)
        FactoryBot.create(:post_like, post_id: post1.id, user_id: user.id, active: true)
        likes_count = user.get_likes_count
        expect(likes_count).to eq(2)
      end
    end
  end

  describe "#get_posts of user" do
    context "get posts" do
      it "returns posts of a user who is logged in" do
        user = FactoryBot.create(:user)
        5.times do
          FactoryBot.create(:post, user_id: user.id, active: true)
        end
        posts = user.get_posts(user, 0, 10, Gem::Version.new('5.2.0'))
        expect(posts.count).to eq(5)
        expect(posts.first[:user][:id]).to eq(user.id)
        expect(posts.first[:user][:follows]).to eq(nil)
        expect(posts.first[:user_liked]).to eq(false)
        expect(posts.first[:likes_count]).to eq(0)
        expect(posts.first[:comments_count]).to eq(0)
      end

      it "returns posts of a user who is not logged in" do
        logged_in_user = FactoryBot.create(:user)
        user = FactoryBot.create(:user)
        5.times do
          FactoryBot.create(:post, user_id: user.id, active: true)
        end
        posts = user.get_posts(logged_in_user, 0, 10, Gem::Version.new('5.2.0'))
        expect(posts.count).to eq(5)
        expect(posts.first[:user][:id]).to eq(user.id)
        expect(posts.first[:user][:follows]).to eq(false)
        expect(posts.first[:user_liked]).to eq(false)
        expect(posts.first[:likes_count]).to eq(0)
        expect(posts.first[:comments_count]).to eq(0)
      end

      it "returns posts of a user with pagination" do
        user = FactoryBot.create(:user)
        12.times do
          FactoryBot.create(:post, user_id: user.id, active: true)
        end
        posts = user.get_posts(user, 10, 2, Gem::Version.new('5.2.0'))
        expect(posts.count).to eq(2)
      end

      it "returns posts of a user with all details" do
        user = FactoryBot.create(:user)
        user_posts = []
        5.times do
          user_posts << FactoryBot.create(:post, user_id: user.id, active: true)
        end
        liked_user = FactoryBot.create(:user)
        FactoryBot.create(:post_like, post_id: user_posts.first.id, user_id: user.id, active: true)
        FactoryBot.create(:post_like, post_id: user_posts.first.id, user_id: liked_user.id, active: true)
        commented_user = FactoryBot.create(:user)
        FactoryBot.create(:post_comment, post_id: user_posts.first.id, user_id: commented_user.id, active: true)
        posts_response = user.get_posts(user, 0, 10, Gem::Version.new('5.2.0'))

        expect(posts_response.count).to eq(5)
        expect(posts_response.last[:user][:id]).to eq(user.id)
        expect(posts_response.last[:user][:follows]).to eq(nil)
        expect(posts_response.last[:user_liked]).to eq(true)
        expect(posts_response.last[:likes_count]).to eq(2)
        expect(posts_response.last[:comments_count]).to eq(1)
      end
    end
  end

  describe "#get_liked_posts of a user" do
    context "get liked posts" do
      it "returns liked posts of a user" do
        user = FactoryBot.create(:user)
        liked_user = FactoryBot.create(:user)
        5.times do
          post = FactoryBot.create(:post, user_id: user.id, active: true)
          FactoryBot.create(:post_like, post_id: post.id, user_id: liked_user.id, active: true)
        end
        posts = liked_user.get_liked_posts(user, 0, 10, Gem::Version.new('5.2.0'))
        expect(posts.count).to eq(5)
      end

      it "returns liked posts of a user with pagination" do
        user = FactoryBot.create(:user)
        liked_user = FactoryBot.create(:user)
        12.times do
          post = FactoryBot.create(:post, user_id: user.id, active: true)
          FactoryBot.create(:post_like, post_id: post.id, user_id: liked_user.id, active: true)
        end
        posts = liked_user.get_liked_posts(user, 10, 2, Gem::Version.new('5.2.0'))
        expect(posts.count).to eq(2)
      end

      it "returns all liked posts of a user when offset 0 and count 0" do
        user = FactoryBot.create(:user)
        liked_user = FactoryBot.create(:user)
        5.times do
          post = FactoryBot.create(:post, user_id: user.id, active: true)
          FactoryBot.create(:post_like, post_id: post.id, user_id: liked_user.id, active: true)
        end
        posts = liked_user.get_liked_posts(user, 0, 0, Gem::Version.new('5.2.0'))
        expect(posts.count).to eq(5)
      end

      it "returns liked posts of a user with all details" do
        logged_in_user = FactoryBot.create(:user)
        liked_user = FactoryBot.create(:user)
        created_posts = []
        5.times do
          post = FactoryBot.create(:post, user_id: logged_in_user.id, active: true)
          created_posts << post
          FactoryBot.create(:post_like, post_id: post.id, user_id: liked_user.id, active: true)
        end

        FactoryBot.create(:post_like, post_id: created_posts.first.id, user_id: logged_in_user.id, active: true)
        posts = liked_user.get_liked_posts(logged_in_user, 0, 10, Gem::Version.new('5.2.0'))

        expect(posts.count).to eq(5)
        expect(posts.first[:user][:id]).to eq(logged_in_user.id)
        expect(posts.first[:user][:follows]).to eq(nil)
        expect(posts.last[:user_liked]).to eq(true)
        expect(posts.first[:user_liked]).to eq(false)
        expect(posts.first[:likes_count]).to eq(1)
        expect(posts.first[:comments_count]).to eq(0)
      end
    end
  end

  describe "#mark_suggested_as_seen of a user" do
    context "mark suggested as seen" do
      it "updates suggested user views when user is not in the hash" do
        user = FactoryBot.create(:user)
        viewed_user = FactoryBot.create(:user)
        redis_key = "suggested_user_views_#{viewed_user.id}"
        allow($redis).to receive(:hexists).and_return(false)
        allow($redis).to receive(:hset)
        viewed_user.mark_suggested_as_seen(user.id)
        expect($redis).to have_received(:hexists).with(redis_key, user.id.to_s)
        expect($redis).to have_received(:hset).with(redis_key, user.id.to_s, anything)
      end

      it 'does not update suggested user views when user is already in the hash' do
        user = FactoryBot.create(:user)
        viewed_user = FactoryBot.create(:user)
        redis_key = "suggested_user_views_#{viewed_user.id}"
        allow($redis).to receive(:hexists).and_return(true)
        allow($redis).to receive(:hset)

        viewed_user.mark_suggested_as_seen(user.id)

        expect($redis).to have_received(:hexists).with(redis_key, user.id.to_s)
        expect($redis).not_to have_received(:hset)
      end
    end
  end

  describe "#party_ids_with_score" do
    context "returns political circle ids with score" do
      it "returns user state political parties ids along with other state political parties" do
        @other_state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)
        user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                 state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                 mp_constituency_id: @mp_constituency.id)
        joined_circle_ids = []
        political_party_ids = []
        # user state parties
        5.times do
          political_party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
          political_party_ids << political_party.id
          FactoryBot.create(:circles_relation, first_circle_id: political_party.id, second_circle_id: @state.id,
                            relation: :Party2State, active: true)
        end
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: political_party_ids.first)
        joined_circle_ids << political_party_ids.first
        # other state parties
        5.times do
          political_party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
          political_party_ids << political_party.id
          FactoryBot.create(:circles_relation, first_circle_id: political_party.id, second_circle_id: @other_state.id,
                            relation: :Party2State, active: true)
        end
        expect(user.party_ids_with_score(joined_circle_ids, 0).first.keys).to eq(political_party_ids - joined_circle_ids)
      end
    end
  end

  describe "#state_level_leader_ids_with_score_for_affiliated_users" do
    context "returns state level leader ids with score for affiliated users" do
      it "returns user state level leader ids" do
        @other_state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)
        user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                 state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                 mp_constituency_id: @mp_constituency.id)
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: @party_circle.id)
        leader_ids = []
        joined_circle_ids = []
        # user state level leaders
        5.times do
          leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
          leader_ids << leader.id
          FactoryBot.create(:circles_relation, first_circle_id: leader.id, second_circle_id: @state.id,
                            relation: :Leader2State, active: true)
        end
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: leader_ids.last)
        joined_circle_ids << leader_ids.last

        # pick 3 random leader ids for affiliated party
        affiliated_party_state_level_leader_ids = leader_ids.sample(3)
        # other state level leaders
        5.times do
          leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
          leader_ids << leader.id
          FactoryBot.create(:circles_relation, first_circle_id: leader.id, second_circle_id: @other_state.id,
                            relation: :Leader2State, active: true)
        end
        affiliated_party_state_level_leader_ids.each do |leader_id|
          FactoryBot.create(:circles_relation, first_circle_id: leader_id, second_circle_id: @party_circle.id,
                            relation: :Leader2Party, active: true)
        end
        expect(user.state_level_leader_ids_with_score_for_affiliated_user(
          [@party_circle.id], [@party_circle.id] + joined_circle_ids, {}, 0).first.keys).to eq(affiliated_party_state_level_leader_ids - joined_circle_ids)

      end
    end
  end

  describe "#mp_and_mla_with_score_for_affiliated_user" do
    context "returns mp and mla with score for affiliated users" do
      it "returns user mp and mla ids irrespcitve of affiliated party" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)
        user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                 state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                 mp_constituency_id: @mp_constituency.id)
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: @party_circle.id)
        leader_ids = []
        # user mp and mla
        mp = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                               circle_photos:
                                 [FactoryBot.build(:circle_photo,
                                                   photo: FactoryBot.create(:photo),
                                                   photo_type: :poster,
                                                   photo_order: 1)])
        leader_ids << mp.id
        FactoryBot.create(:circles_relation, first_circle_id: @mp_constituency.id, second_circle_id: mp.id,
                          relation: :MP, active: true)

        mla = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                circle_photos:
                                  [FactoryBot.build(:circle_photo,
                                                    photo: FactoryBot.create(:photo),
                                                    photo_type: :poster,
                                                    photo_order: 1)])
        leader_ids << mla.id
        FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency.id, second_circle_id: mla.id,
                          relation: :MLA, active: true)

        expect(user.mp_and_mla_with_score_for_affiliated_user([@party_circle.id], {}, 0).first.keys).to eq(leader_ids)
      end
    end
  end

  describe "#local_leader_circle_ids_with_score_for_affiliated_user" do
    context "returns local leader circle ids with score for affiliated users" do
      it "returns user local leader circle ids" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)
        user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                 state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                 mp_constituency_id: @mp_constituency.id)
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: @party_circle.id)
        leader_ids = []
        joined_circle_ids = []
        # user affiliated local leaders
        mla_contestant = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency.id, second_circle_id: mla_contestant.id,
                          relation: :MLA_Contestant, active: true)
        FactoryBot.create(:circles_relation, first_circle_id: mla_contestant.id, second_circle_id: @party_circle.id,
                          relation: :Leader2Party, active: true)
        mp_contestant = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])
        FactoryBot.create(:circles_relation, first_circle_id: @mp_constituency.id, second_circle_id: mp_contestant.id,
                          relation: :MP_Contestant, active: true)
        FactoryBot.create(:circles_relation, first_circle_id: mp_contestant.id, second_circle_id: @party_circle.id,
                          relation: :Leader2Party, active: true)
        leader_ids << mp_contestant.id
        leader_ids << mla_contestant.id
        5.times do
          # create other mla constituency under same mp constituency
          mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                               parent_circle: @mp_constituency)
          leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
          leader_ids << leader.id
          FactoryBot.create(:circles_relation, first_circle_id: mla_constituency.id, second_circle_id: leader.id,
                            relation: :MLA_Contestant, active: true)
          FactoryBot.create(:circles_relation, first_circle_id: leader.id, second_circle_id: @party_circle.id,
                            relation: :Leader2Party, active: true)

          # create other party leaders too
          other_party = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
          other_party_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
          FactoryBot.create(:circles_relation, first_circle_id: other_party_leader.id, second_circle_id: other_party.id,
                            relation: :Leader2Party, active: true)
        end

        FactoryBot.create(:user_circle, user_id: user.id, circle_id: leader_ids.last)
        joined_circle_ids << leader_ids.last
        expect(user.local_leader_circle_ids_with_score_for_affiliated_user([@party_circle.id],
                                                                           [@party_circle.id] + joined_circle_ids,
                                                                           {}, 0).first.keys).to eq(leader_ids - joined_circle_ids)

      end
    end
  end

  describe "#district_level_leader_circle_ids_with_score_for_affiliated_user" do
    context "returns district level leader circle ids with score for affiliated users" do
      it "returns user district level leader circle ids" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @user_mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @user_village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @user_mandal)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user_mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @user_mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                                   parent_circle: @user_mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @user_village.id, mandal_id: @user_mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @user_mla_constituency.id,
                                  mp_constituency_id: @user_mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        joined_circle_ids = []
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader_circle.id)
        joined_circle_ids << @leader_circle.id
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party_circle.id)

        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency,
                          active: true, relation: :Mandal2MLA)
        leader_ids = [@leader_circle1.id, @leader_circle.id]
        expect(@user.district_level_leader_circle_ids_with_score_for_affiliated_user(
          [@party_circle.id], [@party_circle.id] + joined_circle_ids, {}, 0).first.keys).to eq(leader_ids - joined_circle_ids)
      end
      it "returns user district level leader circle ids whose don't have another mp constituency" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @user_mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @user_village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @user_mandal)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user_mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @user_mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                                   parent_circle: @user_mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @user_mp_constituency)

        @user = FactoryBot.create(:user, village_id: @user_village.id, mandal_id: @user_mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @user_mla_constituency.id,
                                  mp_constituency_id: @user_mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @party_circle.id)

        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @user_mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @user_mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency,
                          active: true, relation: :Mandal2MLA)
        leader_ids = [@leader_circle.id]
        expect(@user.district_level_leader_circle_ids_with_score_for_affiliated_user(
          [@party_circle.id], [@party_circle.id], {}, 0).first.keys).to eq(leader_ids)
      end
    end
  end

  describe "#party_and_state_leader_ids_with_score" do
    context "returns party and state leader ids with score" do
      it "returns circle ids" do
        @other_state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)
        user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                 state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                 mp_constituency_id: @mp_constituency.id)
        political_party_ids = []
        joined_circle_ids = []
        # user state parties
        2.times do
          political_party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
          political_party_ids << political_party.id
          FactoryBot.create(:circles_relation, first_circle_id: political_party.id, second_circle_id: @state.id,
                            relation: :Party2State, active: true)
        end
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: political_party_ids.first)
        joined_circle_ids << political_party_ids.first
        # other state parties
        2.times do
          political_party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
          political_party_ids << political_party.id
          FactoryBot.create(:circles_relation, first_circle_id: political_party.id, second_circle_id: @other_state.id,
                            relation: :Party2State, active: true)
        end

        leader_ids = []
        # user state level leaders
        2.times do
          leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
          leader_ids << leader.id
          FactoryBot.create(:circles_relation, first_circle_id: leader.id, second_circle_id: @state.id,
                            relation: :Leader2State, active: true)
        end

        FactoryBot.create(:user_circle, user_id: user.id, circle_id: leader_ids.first)
        joined_circle_ids << leader_ids.first
        # other state level leaders
        2.times do
          leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
          leader_ids << leader.id
          FactoryBot.create(:circles_relation, first_circle_id: leader.id, second_circle_id: @other_state.id,
                            relation: :Leader2State, active: true)
        end
        expect(user.party_and_state_leader_ids_with_score(joined_circle_ids, 0).first.keys)
          .to eq(political_party_ids + leader_ids - joined_circle_ids)
      end
    end
  end

  describe "#local_leader_circle_ids_with_score" do
    context "returns local leader circle ids with score" do
      it "returns circle ids" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)
        user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                 state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                 mp_constituency_id: @mp_constituency.id)
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        leader_ids = []
        mp = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                               circle_photos:
                                 [FactoryBot.build(:circle_photo,
                                                   photo: FactoryBot.create(:photo),
                                                   photo_type: :poster,
                                                   photo_order: 1)])
        leader_ids << mp.id
        FactoryBot.create(:circles_relation, first_circle_id: @mp_constituency.id, second_circle_id: mp.id,
                          relation: :MP, active: true)
        mla = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                circle_photos:
                                  [FactoryBot.build(:circle_photo,
                                                    photo: FactoryBot.create(:photo),
                                                    photo_type: :poster,
                                                    photo_order: 1)])
        FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency.id, second_circle_id: mla.id,
                          relation: :MLA, active: true)
        mla_contestant = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency.id, second_circle_id: mla_contestant.id,
                          relation: :MLA_Contestant, active: true)
        FactoryBot.create(:circles_relation, first_circle_id: mla_contestant.id, second_circle_id: @party_circle.id,
                          relation: :Leader2Party, active: true)
        mp_contestant = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])
        FactoryBot.create(:circles_relation, first_circle_id: @mp_constituency.id, second_circle_id: mp_contestant.id,
                          relation: :MP, active: true)
        FactoryBot.create(:circles_relation, first_circle_id: mp_contestant.id, second_circle_id: @party_circle.id,
                          relation: :Leader2Party, active: true)
        leader_ids << mp_contestant.id
        leader_ids << mla.id
        leader_ids << mla_contestant.id
        joined_circle_ids = []
        FactoryBot.create(:user_circle, user_id: user.id, circle_id: leader_ids.last)
        joined_circle_ids << leader_ids.last
        5.times do
          # create other mla constituency under same mp constituency
          mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                               parent_circle: @mp_constituency)
          leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                     circle_photos:
                                       [FactoryBot.build(:circle_photo,
                                                         photo: FactoryBot.create(:photo),
                                                         photo_type: :poster,
                                                         photo_order: 1)])
          leader_ids << leader.id
          FactoryBot.create(:circles_relation, first_circle_id: mla_constituency.id, second_circle_id: leader.id,
                            relation: :MLA_Contestant, active: true)
          FactoryBot.create(:circles_relation, first_circle_id: leader.id, second_circle_id: @party_circle.id,
                            relation: :Leader2Party, active: true)

          # create other party leaders too
          other_party = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
          other_party_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                                 circle_photos:
                                                   [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
          leader_ids << other_party_leader.id
          FactoryBot.create(:circles_relation, first_circle_id: mla_constituency.id, second_circle_id: other_party_leader.id,
                            relation: :MLA_Contestant, active: true)
          FactoryBot.create(:circles_relation, first_circle_id: other_party_leader.id, second_circle_id: other_party.id,
                            relation: :Leader2Party, active: true)
        end
        expect(user.local_leader_circle_ids_with_score(joined_circle_ids, {}, 0).first.keys).to eq(leader_ids - joined_circle_ids)
      end
    end
  end

  describe "#district_level_leader_circle_ids_with_score" do
    context "returns district level leader circle ids with score" do
      it "returns circle ids" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @user_mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @user_village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @user_mandal)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user_mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @user_mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                                   parent_circle: @user_mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @user_village.id, mandal_id: @user_mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @user_mla_constituency.id,
                                  mp_constituency_id: @user_mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        joined_circle_ids = []
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader_circle.id)
        joined_circle_ids << @leader_circle.id

        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency,
                          active: true, relation: :Mandal2MLA)
        leader_ids = [@leader_circle1.id, @leader_circle3.id, @leader_circle.id, @leader_circle2.id]
        expect(@user.district_leader_circle_ids_with_score(
          joined_circle_ids, {}, 0).first.keys).to eq(leader_ids - joined_circle_ids)
      end
      it "user_district_mp_constituency_ids is absent then returns other district level leader circle ids" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @user_mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @user_village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @user_mandal)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user_mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @user_mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                                   parent_circle: @user_mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @user_mp_constituency)

        @user = FactoryBot.create(:user, village_id: @user_village.id, mandal_id: @user_mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @user_mla_constituency.id,
                                  mp_constituency_id: @user_mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        joined_circle_ids = []
        FactoryBot.create(:user_circle, user_id: @user.id, circle_id: @leader_circle.id)
        joined_circle_ids << @leader_circle.id

        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency,
                          active: true, relation: :Mandal2MLA)
        leader_ids = [@leader_circle.id, @leader_circle2.id]
        expect(@user.district_leader_circle_ids_with_score(
          joined_circle_ids, {}, 0).first.keys).to eq(leader_ids - joined_circle_ids)
      end
    end
  end

  describe "#session_based_circle_suggestions" do
    context "suggest circles based on user sessions count" do
      it "returns political circles in session 1 if user not joined any political parties along with other circles" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @party_circle, second_circle: @state, active: true,
                          relation: :Party2State)
        FactoryBot.create(:circles_relation, first_circle: @party_circle1, second_circle: @state, active: true,
                          relation: :Party2State)

        circles = @user.session_based_circle_suggestions(0, 10, 1)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@party_circle.id, @party_circle1.id, @leader_circle.id,
                                           @leader_circle1.id, @leader_circle2.id, @leader_circle3.id])
      end

      it "don't returns political circles in session 1 if user joined any political parties along with other circles" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:user_circle, user: @user, circle: @party_circle)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @party_circle, second_circle: @state, active: true,
                          relation: :Party2State)
        FactoryBot.create(:circles_relation, first_circle: @party_circle1, second_circle: @state, active: true,
                          relation: :Party2State)

        circles = @user.session_based_circle_suggestions(0, 10, 1)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@leader_circle.id, @leader_circle1.id, @leader_circle2.id,
                                           @leader_circle3.id])
      end
      it "returns local leader circles and district and state level leaders in session 2" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @state_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])

        FactoryBot.create(:circles_relation, first_circle_id: @state_leader.id, second_circle_id: @state.id,
                          relation: :Leader2State, active: true)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)

        circles = @user.session_based_circle_suggestions(0, 10, 2)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@state_leader.id, @leader_circle.id, @leader_circle1.id,
                                           @leader_circle2.id, @leader_circle3.id])
      end
      it "returns local and district leader circles first and later state level leaders in session 3" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @user_mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @user_village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @user_mandal)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user_mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @user_mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                                   parent_circle: @user_mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @user_village.id, mandal_id: @user_mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @user_mla_constituency.id,
                                  mp_constituency_id: @user_mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency,
                          active: true, relation: :Mandal2MLA)
        @state_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])

        FactoryBot.create(:circles_relation, first_circle_id: @state_leader.id, second_circle_id: @state.id,
                          relation: :Leader2State, active: true)
        circles = @user.session_based_circle_suggestions(0, 5, 3)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@leader_circle.id, @leader_circle1.id, @leader_circle2.id,
                                           @leader_circle3.id, @state_leader.id])
      end
    end
  end

  describe "#get_suggested_circles_v2" do
    context "suggest circles" do
      it "in page 1" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @party_circle, second_circle: @state, active: true,
                          relation: :Party2State)
        FactoryBot.create(:circles_relation, first_circle: @party_circle1, second_circle: @state, active: true,
                          relation: :Party2State)

        circles = @user.get_suggested_circles_v2(0, 10)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@party_circle.id, @party_circle1.id, @leader_circle.id,
                                           @leader_circle1.id, @leader_circle2.id, @leader_circle3.id])
      end

      it "in page 1 other leader ids along with party ids" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @party_circle, second_circle: @state, active: true,
                          relation: :Party2State)
        FactoryBot.create(:circles_relation, first_circle: @party_circle1, second_circle: @state, active: true,
                          relation: :Party2State)

        circles = @user.get_suggested_circles_v2(0, 10)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@party_circle.id, @party_circle1.id, @leader_circle.id,
                                           @leader_circle1.id, @leader_circle2.id, @leader_circle3.id])
      end
      it "in page 1 only party affiliated leader ids along with other political parties" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @leader_circle1 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @leader_circle3 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @party_circle1 = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        FactoryBot.create(:user_circle, user: @user, circle: @party_circle)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle1, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle2, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle3, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle, active: true,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle1, active: true,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @leader_circle2,
                          active: true, relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mp_constituency, second_circle: @leader_circle3,
                          active: true, relation: :MP_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @party_circle, second_circle: @state, active: true,
                          relation: :Party2State)
        FactoryBot.create(:circles_relation, first_circle: @party_circle1, second_circle: @state, active: true,
                          relation: :Party2State)

        circles = @user.get_suggested_circles_v2(0, 10)
        circle_ids = circles.map(&:id)
        expect(circle_ids).to match_array([@party_circle1.id, @leader_circle.id, @leader_circle1.id,
                                           @leader_circle2.id, @leader_circle3.id])
      end
    end
  end

  describe "#get_is_user_position_back_for_posters_tab_v2" do
    context "get is_user_position_back for posters tab v2" do
      it "when name length is less than 10" do
        @user = FactoryBot.create(:user, name: "testing")
        is_user_position_back = @user.get_is_user_position_back_for_posters_tab_v2
        expect(is_user_position_back).to eq(false)
      end
      it "when name length is greater than 10" do
        @user = FactoryBot.create(:user, name: "testing testing")
        is_user_position_back = @user.get_is_user_position_back_for_posters_tab_v2
        expect(is_user_position_back).to eq(true)
      end
    end
  end

  describe "#get_user_dm_circles" do
    it "returns user dm circles of the given circle ids" do
      user = FactoryBot.create(:user)
      user2 = FactoryBot.create(:user)
      circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
      circle2 = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
      FactoryBot.create(:user_circle, user: user, circle: circle)
      FactoryBot.create(:user_circle, user: user2, circle: circle)

      circle_ids = [circle.id, circle2.id]
      user_dm_circles = user.get_user_dm_circles(circle_ids)
      expect(user_dm_circles.length).to eq(1)
      expect(user_dm_circles[circle.id][:name]).to eq(circle.name)
      expect(user_dm_circles[circle.id][:senders].length).to eq(2)
      expect(user_dm_circles[circle.id][:senders][0][:name]).to eq(user.name)

      FactoryBot.create(:user_circle, user: user, circle: circle2)
      circle_ids = [circle.id]
      user_dm_circles = user.get_user_dm_circles(circle_ids)
      expect(user_dm_circles.length).to eq(1)
      expect(user_dm_circles[circle.id][:name]).to eq(circle.name)
    end
  end

  describe "#get_user_dm_group_circles_info" do
    it "it returns user dm circle ids hashes list" do
      # TODO: Need to uncomment this once channel enabled
      user = FactoryBot.create(:user)
      circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
      circle2 = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
      circle3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party, conversation_type: :channel)

      FactoryBot.create(:user_circle, user: user, circle: circle)
      FactoryBot.create(:user_circle, user: user, circle: circle2)
      FactoryBot.create(:user_circle, user: user, circle: circle3)

      permission_group = FactoryBot.build(:permission_group, name: "test_permission_group")
      permission_group.save(validate: false)
      FactoryBot.create(:permission_group_permission, permission_group: permission_group, permission_identifier: :add_tag)
      FactoryBot.create(:permission_group_permission, permission_group: permission_group, permission_identifier: :remove_tag)
      FactoryBot.create(:user_circle_permission_group, user: user, circle: circle3, permission_group: permission_group)

      user_dm_circle_ids_list = user.get_user_dm_circles_info
      expect(user_dm_circle_ids_list.length).to eq(1)
      expect(user_dm_circle_ids_list[:info][0][:circle_id]).to eq(circle.id)
      expect(user_dm_circle_ids_list[:info][1][:circle_id]).to eq(circle2.id)
      expect(user_dm_circle_ids_list[:info][2][:circle_id]).to eq(circle3.id)
      expect(user_dm_circle_ids_list[:info][2][:permissions][0]).to eq("remove_tag")
      expect(user_dm_circle_ids_list[:info][2][:permissions][1]).to eq("add_tag")
    end
    it "it returns user dm circle ids hashes list without circles which are excluded to user" do
      # TODO: Need to uncomment this once channel enabled
      user = FactoryBot.create(:user)
      circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
      circle2 = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
      FactoryBot.create(:user_circle, user: user, circle: circle)
      FactoryBot.create(:user_circle, user: user, circle: circle2)
      FactoryBot.create(:excluded_user_circle, user: user, circle: circle2, excluded_by_user: user, conversation_type: circle2.conversation_type)
      user_dm_circle_ids_list = user.get_user_dm_circles_info
      expect(user_dm_circle_ids_list.length).to eq(1)
      expect(user_dm_circle_ids_list[:info].length).to eq(1)
      expect(user_dm_circle_ids_list[:info][0][:circle_id]).to eq(circle.id)
    end
  end

  describe "converting basic poster json to premium poster json" do
    context "when user is not subscribed but has poster layouts data whose app version < '2310.24.00' " do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        AppVersionSupport.new('2306.27.01')

        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
      end
      it "should return only basic frames if no frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
        # basic poster json verification
        expect(@user_layouts[0][:layout_type]).to eq("basic")
        expect(@user_layouts[0][:header_1_photos]&.length).to be_nil
        expect(@user_layouts[0][:header_2_photos]&.length).to eq(0)
        expect(@user_layouts[0][:v1]).to be_present
        expect(@user_layouts[0][:identity]).to be_present
      end

      it "should return the user frames if frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :gold_lettered_user)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)
        FactoryBot.create(:user_frame, user: @user, frame: @frame1)
        FactoryBot.create(:user_frame, user: @user, frame: @frame2)
        FactoryBot.create(:user_frame, user: @user, frame: @frame3)
        FactoryBot.create(:user_frame, user: @user, frame: @frame4)

        travel_to(Time.parse("2023-09-20")) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(7)
          expect(@user_layouts[1][:layout_type]).to eq("basic")
          expect(@user_layouts[1][:header_1_photos]&.length).to be_nil
          expect(@user_layouts[1][:header_2_photos]&.length).to eq(0)
          expect(@user_layouts[1][:v1]).to be_present
          expect(@user_layouts[1][:identity]).to be_present
        end
      end
    end

    context "when user is not subscribed and does not have poster layouts data whose app version < '2310.24.00' " do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2306.27.01')
      end
      it "should return the following user layouts when user has affiliated circle id" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
        expect(@user_layouts[0][:layout_type]).to eq("basic")
        expect(@user_layouts[0][:header_1_photos]&.length).to be_nil
        expect(@user_layouts[0][:header_2_photos]&.length).to eq(0)
        expect(@user_layouts[0][:v1]).to be_present
        expect(@user_layouts[0][:identity]).to be_present
      end
    end

    context "when user is not subscribed but has poster layouts data whose app version > '2310.24.00' " do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)

        AppVersionSupport.new('2310.25.0')

        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
      end
      it "should return only one frame if no frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:circle_photo, circle: @circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
        # basic poster json verification
        expect(@user_layouts[0][:layout_type]).to eq("basic")
        expect(@user_layouts[0][:header_1_photos]&.length).to be_nil
        expect(@user_layouts[0][:header_2_photos]&.length).to eq(1)
        expect(@user_layouts[0][:v1]).to be_nil
        expect(@user_layouts[0][:identity]).to be_present
      end

      it "should return the user frames if frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:circle_photo, circle: @circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :gold_lettered_user)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)
        FactoryBot.create(:user_frame, user: @user, frame: @frame1)
        FactoryBot.create(:user_frame, user: @user, frame: @frame2)
        FactoryBot.create(:user_frame, user: @user, frame: @frame3)
        FactoryBot.create(:user_frame, user: @user, frame: @frame4)

        travel_to(Time.parse("2023-09-20")) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(7)
          expect(@user_layouts[1][:layout_type]).to eq("basic")
          expect(@user_layouts[1][:header_1_photos]&.length).to be_nil
          expect(@user_layouts[1][:header_2_photos]&.length).to eq(1)
          expect(@user_layouts[1][:v1]).to be_nil
          expect(@user_layouts[1][:identity]).to be_present
        end
      end
    end

    context "when user is not subscribed and does not have poster layouts data whose app version > '2310.24.00' " do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2310.25.0')
      end
      it "should return the following user layouts when user has affiliated circle id" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        FactoryBot.create(:circle_photo, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
        expect(@user_layouts[0][:layout_type]).to eq("basic")
        expect(@user_layouts[0][:share_text]).not_to be_nil
        expect(@user_layouts[0][:share_text].strip).not_to be_empty
        expect(@user_layouts[0][:header_1_photos]&.length).to be_nil
        expect(@user_layouts[0][:header_2_photos]&.length).to eq(1)
        expect(@user_layouts[0][:v1]).to be_nil
        expect(@user_layouts[0][:identity]).to be_present
        expect(@user_layouts[0][:fonts_config]).to be_present
        expect(@user_layouts[0][:fonts_config]).to eq(@user.default_font_config(nil))
      end
    end
  end

  describe "#get_user_layout" do
    context "should return single layout of the user" do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        AppVersionSupport.new('2306.27.01')

        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
        @premium_frame = FactoryBot.create(:frame, frame_type: :premium)
        @user_premium_frame = FactoryBot.create(:user_frame, user: @user, frame: @premium_frame)
      end
      it "should return the user layout of the given frame id" do
        user_layout = @user.get_user_layout(frame_id: @premium_frame.id)
        expect(user_layout).not_to be_nil
        expect(user_layout[:id]).to eq(@premium_frame.id)
      end
      it "should return the first status layout" do
        @status_frame = FactoryBot.create(:frame, frame_type: :status)
        @user_status_frame = FactoryBot.create(:user_frame, user: @user, frame: @status_frame)
        user_layout = @user.get_user_layout(frame_id: @status_frame.id)
        expect(user_layout).not_to be_nil
        expect(user_layout[:id]).to eq(@status_frame.id)
      end

      it "should return the first premium layout if frame id is not given" do
        user_layout = @user.get_user_layout
        expect(user_layout).not_to be_nil
        expect(user_layout[:id]).to eq(@premium_frame.id)
      end

      it "should skip the glassy frame if exists and return the first premium layout" do
        @glassy_frame = FactoryBot.create(:frame, frame_type: :premium, identity_type: :glassy_user)
        @user_glassy_frame = FactoryBot.create(:user_frame, user: @user, frame: @glassy_frame)
        user_layout = @user.get_user_layout
        expect(user_layout).not_to be_nil
        expect(user_layout[:id]).to eq(@premium_frame.id)
      end
    end
  end

  # TODO: enable when inbounds are enabled
  # describe "#get_pitch_premium_layout" do
  #   context "get the premium pitch layout" do
  #     before :each do
  #       # travel to time to 12'O clock of weekday (working hours)
  #       travel_to Time.zone.now.beginning_of_day.monday + 12.hours
  #       photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
  #       @user = FactoryBot.create(:user)
  #       @user.poster_photo = FactoryBot.create(:admin_medium, data: photo)
  #       @user.poster_photo_with_background = FactoryBot.create(:admin_medium, data: photo)
  #       image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
  #       image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
  #       @admin_medium_1 = FactoryBot.create(:admin_medium, data: image_600x750)
  #       @admin_medium_2 = FactoryBot.create(:admin_medium, data: image_630x940)
  #       @poster_creative = FactoryBot.create(:poster_creative, event: nil, photo_v3: @admin_medium_1,
  #                                            photo_v2: @admin_medium_2)
  #     end
  #
  #     it "show the premium pitch if user is test user in supported app version and has no user poster layout" do
  #       allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
  #       AppVersionSupport.new('2403.10.01')
  #
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       expect(user_layouts).not_to be_empty
  #       expect(user_layouts[0][:premium_pitch]).to be_present
  #     end
  #
  #     it "it should have user's affiliated circle poster photos and dummy photos as h2 and user photo" do
  #       photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
  #       @circle = FactoryBot.create(:circle)
  #       circle_photo = FactoryBot.create(:circle_photo, circle: @circle, photo_type: :poster)
  #       FactoryBot.create(:circle_photo, circle: @circle, photo_type: :poster, photo_order: 2)
  #       leader_dummy_photo = AdminMedium.find_by(id: Constants.get_leader_dummy_cutout_id)
  #       if leader_dummy_photo.blank?
  #         leader_dummy_photo = FactoryBot.create(:admin_medium, id: Constants.get_leader_dummy_cutout_id, data: photo)
  #       end
  #       user_dummy_photo = AdminMedium.find_by(id: Constants.user_dummy_cutout_id)
  #       if user_dummy_photo.blank?
  #         user_dummy_photo = FactoryBot.create(:admin_medium, id: Constants.user_dummy_cutout_id, data: photo)
  #       end
  #
  #       @user.affiliated_party_circle_id = @circle.id
  #       @user.save!
  #       allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
  #       AppVersionSupport.new('2403.10.01')
  #
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       expect(user_layouts).not_to be_empty
  #       expect(user_layouts[0][:premium_pitch]).to be_present
  #
  #       expect(user_layouts[0][:header_1_photos]).to be_present
  #       expect(user_layouts[0][:header_1_photos].count).to eq(2)
  #       circle_photo_url = circle_photo.photo.replace_url('https://az-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/')
  #       expect(user_layouts[0][:header_1_photos][0][:photo_url]).to eq(circle_photo_url)
  #
  #       leader_photo_url = leader_dummy_photo.replace_url('https://az-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/')
  #       expect(user_layouts[0][:header_2_photos]).to be_present
  #       expect(user_layouts[0][:header_2_photos].count).to eq(4)
  #       expect(user_layouts[0][:header_2_photos][0][:photo_url]).to eq(leader_photo_url)
  #
  #       user_photo_url = user_dummy_photo.replace_url('https://az-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/')
  #       expect(user_layouts[0][:identity][:user][:photo_url]).to be_present
  #       expect(user_layouts[0][:identity][:user][:photo_url]).to eq(user_photo_url)
  #     end
  #
  #     it "it should customised layout information" do
  #       photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
  #       @circle = FactoryBot.create(:circle)
  #       leader_dummy_photo = AdminMedium.find_by(id: Constants.get_leader_dummy_cutout_id)
  #       if leader_dummy_photo.blank?
  #         FactoryBot.create(:admin_medium, id: Constants.get_leader_dummy_cutout_id, data: photo)
  #       end
  #       user_dummy_photo = AdminMedium.find_by(id: Constants.user_dummy_cutout_id)
  #       if user_dummy_photo.blank?
  #         FactoryBot.create(:admin_medium, id: Constants.user_dummy_cutout_id, data: photo)
  #       end
  #
  #       @user.affiliated_party_circle_id = @circle.id
  #       @user.save!
  #       allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
  #       AppVersionSupport.new('2403.10.01')
  #
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       expect(user_layouts).not_to be_empty
  #       expect(user_layouts[0][:premium_pitch]).to be_present
  #       expect(user_layouts[0][:layout_type]).to eq("premium")
  #       expect(user_layouts[0][:golden_frame]).to be_falsey
  #       expect(user_layouts[0][:show_praja_logo]).to be_falsey
  #       expect(user_layouts[0][:is_locked]).to be_truthy
  #       expect(user_layouts[0][:is_bordered_layout]).to be_falsey
  #       expect(user_layouts[0][:neutral_frame]).to be_falsey
  #       expect(user_layouts[0][:enable_outer_frame]).to be_falsey
  #       expect(user_layouts[0][:identity][:type]).to eq("trapezoidal_identity")
  #       expect(user_layouts[0][:analytics_params][:is_premium_pitch]).to be_truthy
  #       expect(user_layouts[0][:analytics_params][:premium_pitch_view_count]).to eq(1)
  #
  #       @user.update_pitch_premium_layout_view_count
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       expect(user_layouts[0][:analytics_params][:premium_pitch_view_count]).to eq(2)
  #
  #       @user.update_pitch_premium_layout_view_count
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       # as only one layout is been coming no shifting happening so remains at same place otherwise will shift to 1
  #       expect(user_layouts[0][:premium_pitch]).to be_present
  #       expect(user_layouts[0][:analytics_params][:premium_pitch_view_count]).to eq(3)
  #
  #     end
  #
  #     it "not to show premium pitch if user is not test user" do
  #       allow_any_instance_of(User).to receive(:is_test_user?).and_return(false)
  #       AppVersionSupport.new('2403.10.01')
  #
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       has_premium_pitch_layout = user_layouts.present? ? user_layouts[0][:premium_pitch].present? : false
  #       expect(has_premium_pitch_layout).to be_falsey
  #     end
  #
  #     it "not to show premium pitch if user is not in supported app version" do
  #       allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
  #       AppVersionSupport.new('2403.09.01')
  #
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       has_premium_pitch_layout = user_layouts.present? ? user_layouts[0][:premium_pitch].present? : false
  #       expect(has_premium_pitch_layout).to be_falsey
  #     end
  #
  #     it "not to show premium pitch if user has poster layout" do
  #       allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
  #       AppVersionSupport.new('2403.10.01')
  #       FactoryBot.create(:user_poster_layout, entity: @user, h1_count: 0, h2_count: 0)
  #
  #       user_layouts = @user.get_user_layouts(@poster_creative.id)
  #       has_premium_pitch_layout = user_layouts.present? ? user_layouts[0][:premium_pitch].present? : false
  #       expect(has_premium_pitch_layout).to be_falsey
  #     end
  #   end
  # end

  describe "#user_active_poster" do
    context "when user don't have user role then get the poster based on affiliated circle column in user" do
      it "when user is affiliated to any circle" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @circle = FactoryBot.create(:circle)
        @circle_photo = FactoryBot.create(:circle_photo, circle: @circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @poster = FactoryBot.create(:poster, circle: @circle,
                                    poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo,
                                                                     photo: FactoryBot.build(:admin_medium,
                                                                                             blob_data: normal_poster_photo))])

        @circle2 = FactoryBot.create(:circle)
        @poster2 = FactoryBot.create(:poster, circle: @circle2,
                                     poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo,
                                                                      photo: FactoryBot.build(:admin_medium,
                                                                                              blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("2310.24.0")
        AppVersionSupport.new('2310.24.0')

        # mock user affiliated_party_circle_id column value with 31403
        # allow(@user).to receive(:affiliated_party_circle_id).and_return(31403)

        get_poster = @user.user_active_poster(app_version)
        expect(get_poster).to be_present
        expect(get_poster.id).to eq(@poster.id)
      end
    end

    context "when user have user role then don't get the poster based on affiliated circle column in user" do
      it "when user is affiliated to any circle" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @circle = FactoryBot.create(:circle)
        @circle_photo = FactoryBot.create(:circle_photo, circle: @circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :private,
                                  has_purview: false,
                                  active: true)
        @private_circle = FactoryBot.create(:circle, level: :private, circle_type: :my_circle)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @private_circle.id)
        @poster = FactoryBot.create(:poster, circle: @circle,
                                    poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo,
                                                                     photo: FactoryBot.build(:admin_medium,
                                                                                             blob_data: normal_poster_photo))])

        @circle2 = FactoryBot.create(:circle)
        @poster2 = FactoryBot.create(:poster, circle: @circle2,
                                     poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo,
                                                                      photo: FactoryBot.build(:admin_medium,
                                                                                              blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("2310.24.0")
        AppVersionSupport.new('2310.24.0')

        # mock user affiliated_party_circle_id column value with 31403
        # allow(@user).to receive(:affiliated_party_circle_id).and_return(31403)

        get_poster = @user.user_active_poster(app_version)
        expect(get_poster).to be_nil
      end

      it "when user's user role has primary or secondary circle is political circle" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @circle = FactoryBot.create(:circle)
        @circle_photo = FactoryBot.create(:circle_photo, circle: @circle)
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  active: true)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle.id)
        @poster = FactoryBot.create(:poster, circle: @circle,
                                    poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo,
                                                                     photo: FactoryBot.build(:admin_medium,
                                                                                             blob_data: normal_poster_photo))])

        @circle2 = FactoryBot.create(:circle)
        @poster2 = FactoryBot.create(:poster, circle: @circle2,
                                     poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo,
                                                                      photo: FactoryBot.build(:admin_medium,
                                                                                              blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("2310.24.0")
        AppVersionSupport.new('2310.24.0')

        # mock user affiliated_party_circle_id column value with 31403
        # allow(@user).to receive(:affiliated_party_circle_id).and_return(31403)

        get_poster = @user.user_active_poster(app_version)
        expect(get_poster).to be_present
        expect(get_poster.id).to eq(@poster.id)
      end
    end
  end

  describe "#mla_contestants_of_user_mla_constituency" do
    context "get mla contestants of user mla constituency" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_constituency)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id, mla_constituency_id: @mla_constituency.id,
                                  mp_constituency_id: @mp_constituency.id)

        @mla_contestant = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                            circle_photos:
                                              [FactoryBot.build(:circle_photo,
                                                                photo: FactoryBot.create(:photo),
                                                                photo_type: :poster,
                                                                photo_order: 1)])
        @mla_contestant1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                             circle_photos:
                                               [FactoryBot.build(:circle_photo,
                                                                 photo: FactoryBot.create(:photo),
                                                                 photo_type: :poster,
                                                                 photo_order: 1)])
        @mla_contestant2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                             circle_photos:
                                               [FactoryBot.build(:circle_photo,
                                                                 photo: FactoryBot.create(:photo),
                                                                 photo_type: :poster,
                                                                 photo_order: 1)])
        @mla_contestant3 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader, members_count: 1,
                                             circle_photos:
                                               [FactoryBot.build(:circle_photo,
                                                                 photo: FactoryBot.create(:photo),
                                                                 photo_type: :poster,
                                                                 photo_order: 1)])
        @mla_contestant4 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader, members_count: 2,
                                             circle_photos:
                                               [FactoryBot.build(:circle_photo,
                                                                 photo: FactoryBot.create(:photo),
                                                                 photo_type: :poster,
                                                                 photo_order: 1)])

        # Order of contestants - YSRCP, TDP, JSP, BJP, INC & other parties (descending order of members_count)
        tg_party_ids_order = [31403, 31402, 31406, 37788, 37967]
        # Party circles
        @ycp_circle = FactoryBot.create(:circle, id: 31403, circle_type: :interest, level: :political_party)
        @tdp_circle = FactoryBot.create(:circle, id: 31402, circle_type: :interest, level: :political_party)
        @jsp_circle = FactoryBot.create(:circle, id: 31406, circle_type: :interest, level: :political_party)
        @bjp_circle = FactoryBot.create(:circle, id: 37788, circle_type: :interest, level: :political_party)
        @inc_circle = FactoryBot.create(:circle, id: 37967, circle_type: :interest, level: :political_party)
        @party_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @party_circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        # Leader2Party relation
        FactoryBot.create(:circles_relation, first_circle: @mla_contestant, second_circle: @ycp_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_contestant1, second_circle: @jsp_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_contestant2, second_circle: @inc_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_contestant3, second_circle: @party_circle, active: true,
                          relation: :Leader2Party)
        FactoryBot.create(:circles_relation, first_circle: @mla_contestant4, second_circle: @party_circle1, active: true,
                          relation: :Leader2Party)

        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @mla_contestant, active: true,
                          relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @mla_contestant1, active: true,
                          relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @mla_contestant2, active: true,
                          relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @mla_contestant3, active: true,
                          relation: :MLA_Contestant)
        FactoryBot.create(:circles_relation, first_circle: @mla_constituency, second_circle: @mla_contestant4, active: true,
                          relation: :MLA_Contestant)
      end
      it "when user mla constituency has mla contestants" do
        mla_contestant_circles = @user.mla_contestants_of_user_mla_constituency
        expect(mla_contestant_circles.length).to eq(5)
        expect(mla_contestant_circles.map(&:id)).to eq([@mla_contestant.id, @mla_contestant1.id,
                                                        @mla_contestant2.id, @mla_contestant4.id,
                                                        @mla_contestant3.id])

      end
    end
  end

  describe "#get_all_circle_permissions" do
    context "if user does not has a permission group on circle, returns default permissions when user joined" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @permission_group = FactoryBot.build(:permission_group, name: "test_permission_group")
        @permission_group.save(validate: false)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :add_tag)
        FactoryBot.create(:circle_permission_group, circle: @circle, permission_group: @permission_group)
      end

      it "returns default permission on user circle list" do
        permissions = @user.get_all_circle_permissions(@circle.id)
        expect(permissions).to be_present
        expect(permissions.length).to eq(1)
        expect(permissions).to include("add_tag")
      end

      it "returns default permissions on user circle list without permissions excluded to user" do
        FactoryBot.create(:excluded_user_circle_permission, user: @user, circle: @circle, permission_identifier: :add_tag)
        permissions = @user.get_all_circle_permissions(@circle.id)
        expect(permissions).not_to be_present
        expect(permissions.length).to eq(0)
        expect(permissions).not_to include("add_tag")
      end

    end

    context "if user has no permissions returns empty list" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
      end

      it "returns empty list" do
        permissions = @user.get_all_circle_permissions(@circle.id)
        expect(permissions).to be_empty
        expect(permissions.length).to eq(0)
      end
    end

    context "if user has a permission group on circle" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @permission_group = FactoryBot.build(:permission_group, name: "test_permission_group")
        @permission_group.save(validate: false)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :add_tag)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :remove_tag)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)
      end

      it "returns the list of permissions associated with the user on the circle" do
        permissions = @user.get_all_circle_permissions(@circle.id)
        expect(permissions).to be_present
        expect(permissions.length).to eq(2)
        expect(permissions).to include("add_tag")
        expect(permissions).to include("remove_tag")
      end

      it "removes the 'circle_share_poster' permission if the user is badge user affiliated to other circle" do
        @circle2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle2.id)

        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :circle_share_poster)

        permissions = @user.get_all_circle_permissions(@circle.id)
        expect(permissions).to be_present
        expect(permissions.length).to eq(2)
        expect(permissions).to include("add_tag")
        expect(permissions).to include("remove_tag")
        expect(permissions).not_to include("circle_share_poster")
      end

      it "should not return excluded user circle permissions" do
        FactoryBot.create(:excluded_user_circle_permission, user: @user, circle: @circle, permission_identifier: :add_tag)
        permissions = @user.get_all_circle_permissions(@circle.id)
        expect(permissions).to be_present
        expect(permissions.length).to eq(1)
        expect(permissions).to include("remove_tag")
        expect(permissions).not_to include("add_tag")
      end

    end

    context 'if user is a party affiliated badge user' do
      before :each do
        @user = FactoryBot.create(:user)
        @party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @party.id)
        @coalition_party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @opposition_party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @permission_group = FactoryBot.build(:permission_group, name: "test_permission_group")
        @permission_group.save(validate: false)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :circle_share_poster)

        FactoryBot.create(:circle_permission_group, circle_type: :interest, circle_level: :political_party, permission_group: @permission_group, is_user_joined: true, circle: nil)
        FactoryBot.create(:circle_permission_group, circle_type: :interest, circle_level: :political_party, permission_group: @permission_group, is_user_joined: false, circle: nil)

        allow(@user).to receive(:all_coalitions).and_return([
                                                              Elections2024::PartyCoalition.new(id: 'coalition', circle_ids: [@party.id, @coalition_party.id]),
                                                              Elections2024::PartyCoalition.new(id: 'opposition', circle_ids: [@opposition_party.id]),
                                                            ])
      end

      it 'should return circle_share_poster permission for same party circle' do
        expect(@user.get_all_circle_permissions(@party.id)).to include(:circle_share_poster.to_s)
      end

      it 'should return circle_share_poster permission for coalition party circle' do
        expect(@user.get_all_circle_permissions(@coalition_party.id)).to include(:circle_share_poster.to_s)
      end

      it 'should not return circle_share_poster permission for opposition party circle' do
        expect(@user.get_all_circle_permissions(@opposition_party.id)).not_to include(:circle_share_poster.to_s)
      end
    end
  end

  describe "#create_floww_contact" do
    context "when right response is not received from floww" do
      before :each do
        @user = FactoryBot.create(:user)
      end

      it "should not create a floww contact for the user" do
        allow(FlowwApi).to receive(:create_or_update_contact).and_return({})
        @user.create_floww_contact
        metadata = Metadatum.find_by(entity: @user, key: Constants.floww_contact_id_key)
        expect(metadata).to be_nil
      end
    end

    context "when right response is received from floww" do
      before :each do
        @user = FactoryBot.create(:user)
      end

      it "should create a floww contact for the user" do
        allow(FlowwApi).to receive(:create_or_update_contact).and_return({ "contact_id" => "floww_contact_id" })
        @user.create_floww_contact
        metadata = Metadatum.find_by(entity: @user, key: Constants.floww_contact_id_key)
        expect(metadata).to be_present
        expect(metadata.value).to eq("floww_contact_id")
      end
    end
  end

  describe "#get_premium_poster_share_text" do
    before :each do
      @user = FactoryBot.create(:user)
      allow(Singular).to receive(:shorten_link).and_return('https://short.link/mock')
    end

    context "when premium poster share text is matching" do
      it "should return the correct share text with shortend link" do
        expected_share_text = "\nనా అఫీషియల్ అప్డేట్స్ కోసం నన్ను *Praja App* లో ఫాలో అవ్వండి.👇👇\nhttps://short.link/mock"
        expect(@user.get_premium_poster_share_text).to eq(expected_share_text)
      end

      it "should call the shorten link with the correct link" do
        deeplink_uri = URI.parse("praja://buzz.praja.app/users/#{@user.id}")
        link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/p411')
        link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s, paffid: @user.id })

        expected_link = link_uri.to_s
        @user.get_premium_poster_share_text
        expect(Singular).to have_received(:shorten_link).with(expected_link)
      end
    end
  end

  # FIX the logic for get_subscription_status with new auto pay implementation
  # describe "#get_subscription_status" do
  #   context "when user has a subscription" do
  #     it "should return the subscription status of paid" do
  #       @user = FactoryBot.create(:user)
  #       FactoryBot.create(:user_product_subscription, user_id: @user.id, item_type: "Product",
  #                         item_id: Constants.get_poster_product_id, start_date: Time.zone.now - 1.day,
  #                         end_date: Time.zone.now + 1.day, source: :orders)
  #       expect(@user.get_subscription_status).to eq('paid')
  #     end
  #     it "should return the subscription status of premium_extended" do
  #       @user = FactoryBot.create(:user)
  #       FactoryBot.create(:user_product_subscription, user_id: @user.id, item_type: "Product",
  #                         item_id: Constants.get_poster_product_id, start_date: Time.zone.now - 10.day,
  #                         end_date: Time.zone.now - 5.day, source: :orders)
  #       FactoryBot.create(:user_product_subscription, user_id: @user.id, item_type: "Product",
  #                         item_id: Constants.get_poster_product_id, start_date: Time.zone.now - 1.day,
  #                         end_date: Time.zone.now + 1.day, source: :premium_extension)
  #       expect(@user.get_subscription_status).to eq('premium_extended')
  #     end
  #     it "should return the subscription status of in_trial" do
  #       @user = FactoryBot.create(:user, :trial_user)
  #       photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
  #       allow(Photo).to receive(:upload).with(instance_of(ActionDispatch::Http::UploadedFile),
  #                                             Constants.praja_account_user_id).and_return(photo)
  #       @user.poster_photo = FactoryBot.create(:admin_medium, data: photo)
  #       @user.poster_photo_with_background = FactoryBot.create(:admin_medium, data: photo)
  #       @user.save
  #       FactoryBot.create(:user_poster_layout, entity: @user,
  #                         user_leader_photos: [
  #                           FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
  #                                             header_type: :header_1, priority: 1),
  #                           FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
  #                                             header_type: :header_2, priority: 1)])
  #       expect(@user.get_subscription_status).to eq('in_trial')
  #     end
  #     it "should return the subscription status of trial_extended" do
  #       @user = FactoryBot.create(:user, :trial_extension_user)
  #       photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
  #       allow(Photo).to receive(:upload).with(instance_of(ActionDispatch::Http::UploadedFile),
  #                                             Constants.praja_account_user_id).and_return(photo)
  #       @user.poster_photo = FactoryBot.create(:admin_medium, data: photo)
  #       @user.poster_photo_with_background = FactoryBot.create(:admin_medium, data: photo)
  #       @user.save
  #       FactoryBot.create(:user_poster_layout, entity: @user,
  #                         user_leader_photos: [
  #                           FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
  #                                             header_type: :header_1, priority: 1),
  #                           FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
  #                                             header_type: :header_2, priority: 1)])
  #       expect(@user.get_subscription_status).to eq('trial_extended')
  #     end
  #     it "should return the subscription status of premium_expired" do
  #       @user = FactoryBot.create(:user)
  #       FactoryBot.create(:user_product_subscription, user_id: @user.id, item_type: "Product",
  #                         item_id: Constants.get_poster_product_id, start_date: Time.zone.now - 10.day,
  #                         end_date: Time.zone.now - 5.day, source: :orders)
  #       expect(@user.get_subscription_status).to eq('premium_expired')
  #     end
  #     it "should return the subscription status of trial_expired" do
  #       @user = FactoryBot.create(:user, :trial_expired_user)
  #       expect(@user.get_subscription_status).to eq('trial_expired')
  #     end
  #
  #     it "should return the subscription status of free" do
  #       @user = FactoryBot.create(:user)
  #       expect(@user.get_subscription_status).to eq('free')
  #     end
  #
  #     it "should return the subscription status of not_yet_paid" do
  #       @user = FactoryBot.create(:user)
  #       FactoryBot.create(:order, :with_order_items, user: @user)
  #       expect(@user.get_subscription_status).to eq('not_yet_paid')
  #     end
  #
  #   end
  # end

  describe "#send_as_fresh_lead_to_crm" do
    before :each do
      @user = FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}")
    end

    context "when user is not a fresh lead" do
      it "should not send user as a fresh lead to crm" do
        allow(@user).to receive(:get_floww_contact_id).and_return("floww_contact_id")
        expect { @user.send_as_fresh_lead_to_crm(:some_suffix) }
          .to raise_error(StandardError).with_message("Already a lead in Floww CRM")
      end
    end
  end

  describe "#get_badge_role_including_unverified" do
    context "when user has a badge role" do
      before :each do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role, has_badge: true)
        @user_role = FactoryBot.create(:user_role, verification_status: :verified, user: @user, role: @role)
      end

      it "should return the user role" do
        expect(@user.get_badge_role_including_unverified).to eq(@user_role)
      end
    end

    context "when user has a badge role but it is unverified" do
      before :each do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role, has_badge: true)
        @user_role = FactoryBot.create(:user_role, verification_status: :unverified, user: @user, role: @role)
      end

      it "should return the badge role" do
        expect(@user.get_badge_role_including_unverified).to eq(@user_role)
      end
    end

    context "when user does not have a badge role" do
      before :each do
        @user = FactoryBot.create(:user)
      end

      it "should return nil" do
        expect(@user.get_badge_role_including_unverified).to be_nil
      end
    end
  end

  describe '#leader_profession?' do
    let(:user) { FactoryBot.create(:user) }

    context 'when user has a profession' do
      it 'returns true for users with a profession' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession = create(:profession, admin_medium: admin_medium)
        allow(Constants).to receive(:leader_profession_ids).and_return([profession.id])

        FactoryBot.create(:user_profession, user: user, profession: profession)
        expect(user.leader_profession?).to be true
      end
    end

    context 'when user does not have a profession' do
      it 'returns false for users without a profession' do
        expect(user.leader_profession?).to be false
      end
    end
  end

  describe "#get_subscription_status" do
    it "returns 'paid' when user is subscribed" do
      user = FactoryBot.create(:user)
      allow(SubscriptionUtils).to receive(:get_subscription_status_for_mixpanel).with(user.id).and_return(:subscribed)
      expect(user.get_subscription_status).to eq('paid')
    end

    it "returns 'premium_expired' when user subscription has expired" do
      user = FactoryBot.create(:user)
      allow(SubscriptionUtils).to receive(:get_subscription_status_for_mixpanel).with(user.id).and_return(:premium_expired)
      expect(user.get_subscription_status).to eq('premium_expired')
    end

    it "returns 'in_trial' when user is in trial period" do
      user = FactoryBot.create(:user)
      allow(SubscriptionUtils).to receive(:get_subscription_status_for_mixpanel).with(user.id).and_return(:in_trial)
      expect(user.get_subscription_status).to eq('in_trial')
    end

    it "returns 'trial_expired' when user trial period has expired" do
      user = FactoryBot.create(:user)
      allow(SubscriptionUtils).to receive(:get_subscription_status_for_mixpanel).with(user.id).and_return(:trial_expired)
      expect(user.get_subscription_status).to eq('trial_expired')
    end

    it "returns 'auto_pay_not_set_up' when auto pay is not set up" do
      user = FactoryBot.create(:user)
      allow(SubscriptionUtils).to receive(:get_subscription_status_for_mixpanel).with(user.id).and_return(:auto_pay_not_set_up)
      expect(user.get_subscription_status).to eq('auto_pay_not_set_up')
    end

    it "returns 'free' when no order is created" do
      user = FactoryBot.create(:user)
      allow(SubscriptionUtils).to receive(:get_subscription_status_for_mixpanel).with(user.id).and_return(:no_order_created)
      expect(user.get_subscription_status).to eq('free')
    end
  end

  describe '#affiliated_circle_id_changed_between_nil_and_integer?' do
    let(:user) { FactoryBot.create(:user) }

    context 'when affiliated_party_circle_id changes from nil to an integer' do
      it 'returns true' do
        user.update(affiliated_party_circle_id: 1)
        expect(user.affiliated_circle_id_changed_between_nil_and_integer?).to eq(true)
      end
    end

    context 'when affiliated_party_circle_id changes from an integer to nil' do
      it 'returns true' do
        user.update(affiliated_party_circle_id: 1)
        user.update(affiliated_party_circle_id: nil)
        expect(user.affiliated_circle_id_changed_between_nil_and_integer?).to eq(true)
      end
    end

    context 'when affiliated_party_circle_id changes from one integer to another' do
      it 'returns false' do
        user.update(affiliated_party_circle_id: 1)
        user.update(affiliated_party_circle_id: 2)
        expect(user.affiliated_circle_id_changed_between_nil_and_integer?).to eq(false)
      end
    end

    context 'when affiliated_party_circle_id does not change' do
      it 'returns false' do
        user.update(affiliated_party_circle_id: 1)
        user.update(name: 'New Name')
        expect(user.affiliated_circle_id_changed_between_nil_and_integer?).to eq(false)
      end
    end
  end

  describe '#is_internal_user' do
    before do
      @user1 = FactoryBot.create(:user, status: :active, phone: "01#{Faker::Number.unique.number(digits: 8)}")
      @user2 = FactoryBot.create(:user, status: :active, phone: "21#{Faker::Number.unique.number(digits: 8)}")
      @user3 = FactoryBot.create(:user, status: :active, phone: "11#{Faker::Number.unique.number(digits: 8)}")
      @user4 = FactoryBot.create(:user, status: :active, phone: Constants.app_store_account_phone)
      @user5 = FactoryBot.create(:user, status: :active, phone: Constants.pg_account_phone)
    end
    context 'calls the method with different phone numbrs' do

      it 'returns false' do
        expect(described_class.is_internal(@user1.phone)).to eq(false)
      end

      it 'returns true' do
        expect(described_class.is_internal(@user2.phone)).to eq(true)
      end

      it 'returns true' do
        expect(described_class.is_internal(@user3.phone)).to eq(true)
      end

      it 'returns true' do
        phone = 9_999_999_999
        expect(described_class.is_internal(phone)).to eq(true)
      end

      it 'returns true' do
        expect(described_class.is_internal(@user4.phone)).to eq(true)
      end

      it 'returns true' do
        expect(described_class.is_internal(@user5.phone)).to eq(true)
      end

    end
  end

  describe '#verify_eligibility_rules_for_premium_pitch?' do
    let(:user) { build(:user) }

    context 'when app version does not support autopay' do
      before do
        allow(AppVersionSupport).to receive(:supports_autopay?).and_return(false)
      end

      it 'returns false' do
        expect(user.verify_eligibility_rules_for_premium_pitch?).to be_falsey
      end
    end

    context 'when app version supports autopay' do
      before do
        allow(AppVersionSupport).to receive(:supports_autopay?).and_return(true)
      end

      context 'when user has premium layout' do
        before do
          allow(user).to receive(:has_premium_layout?).and_return(true)
        end

        it 'returns false' do
          expect(user.verify_eligibility_rules_for_premium_pitch?).to be_falsey
        end
      end

      context 'when user does not have premium layout' do
        before do
          allow(user).to receive(:has_premium_layout?).and_return(false)
        end

        it 'returns true' do
          expect(user.verify_eligibility_rules_for_premium_pitch?).to be_truthy
        end

        context 'with commented-out daily leads limit check' do
          # Uncomment if logic is reintroduced
          # before do
          #   allow(PremiumPitch).to receive(:todays_leads_count).and_return(Constants.daily_premium_leads_limit + 1)
          # end
          #
          # it 'returns false if daily leads limit is reached' do
          #   expect(user.verify_eligibility_rules_for_premium_pitch?).to be_falsey
          # end
        end
      end
    end

    context 'when check_self_trial_eligibility is true' do
      before do
        allow(AppVersionSupport).to receive(:supports_autopay?).and_return(true)
        allow(user).to receive(:has_premium_layout?).and_return(false)
      end

      context 'when app version does not support self trial' do
        before do
          allow(AppVersionSupport).to receive(:self_trial_supported?).and_return(false)
        end

        it 'returns false' do
          expect(user.verify_eligibility_rules_for_premium_pitch?(check_self_trial_eligibility: true)).to be_falsey
        end
      end

      context 'when app version supports self trial' do
        before do
          allow(AppVersionSupport).to receive(:self_trial_supported?).and_return(true)
        end

        context 'when user has previously subscribed' do
          before do
            allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).with(user.id).and_return(true)
          end

          it 'returns false' do
            expect(user.verify_eligibility_rules_for_premium_pitch?(check_self_trial_eligibility: true)).to be_falsey
          end
        end

        context 'when user has never subscribed' do
          before do
            allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).with(user.id).and_return(false)
          end

          it 'returns true' do
            expect(user.verify_eligibility_rules_for_premium_pitch?(check_self_trial_eligibility: true)).to be_truthy
          end
        end
      end
    end
  end

  describe "#get_user_poster_layout_including_inactive" do
    it "returns the user poster layout including inactive" do
      user = create(:user)
      layout = create(:user_poster_layout, entity: user, active: false)
      result = user.get_user_poster_layout_including_inactive
      expect(result).to eq(layout)
    end

    it 'returns the user poster layout if active' do
      user = create(:user)
      layout = create(:user_poster_layout, entity: user, active: true)
      result = user.get_user_poster_layout_including_inactive
      expect(result).to eq(layout)
    end

    it "returns nil if no user poster layout is found" do
      user = create(:user)
      result = user.get_user_poster_layout_including_inactive
      expect(result).to be_nil
    end
  end

end
