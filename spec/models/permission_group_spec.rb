require 'rails_helper'

RSpec.describe PermissionGroup, type: :model do
  # pending "add some examples to (or delete) #{__FILE__}"

  context 'validate name' do
    it 'should not be valid without name' do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @permission_group.name = nil
      expect(@permission_group).to_not be_valid
    end

    it 'should not be valid with name is empty' do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @permission_group.name = ''
      expect(@permission_group).to_not be_valid
    end

    it 'should not be valid with name contains spaces' do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @permission_group.name = ' '
      expect(@permission_group).to_not be_valid
    end

    it 'should be valid with name contains letters' do
      @permission_group = FactoryBot.build(:permission_group, name: Faker::Name.unique.name.gsub(/\W/, ''))
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      expect(@permission_group).to be_valid
    end

    it 'should be valid with name contains letters and underscores' do
      @permission_group = FactoryBot.build(:permission_group, name: Faker::Name.unique.name.gsub(/\W/, '') + '_')
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      expect(@permission_group).to be_valid
    end

    it 'should not be valid with name contains digits' do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @permission_group.name = Faker::Name.unique.name + '1'
      expect(@permission_group).to_not be_valid
    end

    it 'should not be valid with name contains special characters' do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @permission_group.name = Faker::Name.unique.name + '@'
      expect(@permission_group).to_not be_valid
    end

    it 'should be valid with name' do
      @permission_group = FactoryBot.build(:permission_group, name: Faker::Name.unique.name.gsub(/\W/, ''))
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      expect(@permission_group).to be_valid
    end
  end

  context 'get_permission_data_for_post' do
    let(:permission_group) { PermissionGroup }
    before(:each) do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @circle = FactoryBot.create(:circle)
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user, circle: @circle)
    end

    it 'should return true if permission data returned' do
      # permission should be remove tag
      get_permission_data = PermissionGroup.get_permission_data_for_post("remove_tag", @circle.id, @user, @post.id)
      expect(get_permission_data.present?).to be_truthy
    end

    it 'should return false if no permission data returned' do
      # permission should not be remove tag
      get_permission_data = PermissionGroup.get_permission_data_for_post("add_tag", @circle.id, @user, @post.id)
      expect(get_permission_data.present?).to be_falsey
    end
  end
end
