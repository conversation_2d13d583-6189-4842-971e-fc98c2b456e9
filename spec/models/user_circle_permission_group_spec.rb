require 'rails_helper'

RSpec.describe UserCirclePermissionGroup, type: :model do
  describe 'validate user_id' do
    context "it is valid with digits only" do
      before :each do
        @circle = FactoryBot.create(:circle)
        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      end
      it "valid with digits" do
        @user = FactoryBot.create(:user)
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).to be_valid
      end

      it "invalid with not existed user id" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: 0, circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with english letters" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: Faker::Name.unique.name, circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with blank" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: "", circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with empty space" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: " ", circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with nil value" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: nil, circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end
    end
  end

  describe 'validate circle_id' do
    context "it is valid with digits only" do
      before :each do
        @user = FactoryBot.create(:user)
        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      end
      it "valid with digits" do
        @circle = FactoryBot.create(:circle)
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: @circle.id, permission_group: @permission_group)
        expect(@user_circle_permission_group).to be_valid
      end

      it "invalid with not existed circle id" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: -1, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "circle_id not valid with english letters" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: Faker::Name.unique.name, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with blank" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: "", permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with empty space" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: " ", permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with nil value" do
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: nil, permission_group: @permission_group)
        expect(@user_circle_permission_group).not_to be_valid
      end
    end
  end

  describe 'validate permission_group_id' do
    context "it is valid with digits only" do
      before :each do
        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, permission_group: @permission_group)
      end
      it "valid with digits" do
        expect(@user_circle_permission_group).to be_valid
      end

      it "invalid with not existed permission group id" do
        @user_circle_permission_group.permission_group_id = 0
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with english letters" do
        @user_circle_permission_group.permission_group_id = Faker::Name.unique.name
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with blank" do
        @user_circle_permission_group.permission_group_id = ""
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with empty space" do
        @user_circle_permission_group.permission_group_id = " "
        expect(@user_circle_permission_group).not_to be_valid
      end

      it "not valid with nil value" do
        @user_circle_permission_group.permission_group_id = nil
        expect(@user_circle_permission_group).not_to be_valid
      end
    end
  end

  describe 'validate columns changing before update' do
    before :each do
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, permission_group: @permission_group)
    end

    it 'should allow create new record' do
      user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, permission_group: @permission_group)
      expect(user_circle_permission_group).to be_valid
    end

    it 'should not allow to change user_id' do
      @user = FactoryBot.create(:user)
      @user_circle_permission_group.user_id = @user.id
      expect(@user_circle_permission_group).not_to be_valid
    end

    it 'should not allow to change circle_id' do
      @circle = FactoryBot.create(:circle)
      @user_circle_permission_group.circle_id = @circle.id
      expect(@user_circle_permission_group).not_to be_valid
    end

    it 'should allow to change permission_group_id' do
      @permission_group2 = FactoryBot.build(:permission_group)
      @permission_group2.save(validate: false)
      @permission_group_permission2 = FactoryBot.create(:permission_group_permission, permission_group: @permission_group2)
      @user_circle_permission_group.permission_group_id = @permission_group2.id
      expect(@user_circle_permission_group).to be_valid
    end
  end

  describe 'callback to dm on create,update and destroy' do
    before :each do
      @user = FactoryBot.create(:user)
      @circle = FactoryBot.create(:circle)
      @circle.update_columns(conversation_type: :channel)
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: 'add_tag')
    end
    context 'on create' do
      it 'should call send_user_circle_permission_update_to_dm_service' do
        allow(DmUtil).to receive(:send_user_circle_permission_update_to_dm).with(@user.id, @circle.id)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)
        expect(DmUtil).to have_received(:send_user_circle_permission_update_to_dm).with(@user.id, @circle.id)
      end
    end

    context 'on update' do
      it 'should call send_user_circle_permission_update_to_dm_service' do
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)
        @permission_group2 = FactoryBot.build(:permission_group)
        @permission_group2.save(validate: false)
        allow(DmUtil).to receive(:send_user_circle_permission_update_to_dm)
        @user_circle_permission_group.update(permission_group_id: @permission_group2.id)
        expect(DmUtil).to have_received(:send_user_circle_permission_update_to_dm).with(@user.id, @circle.id)
      end
    end

    context 'on destroy' do
      it 'should call send_user_circle_permission_update_to_dm_service' do
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)
        allow(DmUtil).to receive(:send_user_circle_permission_update_to_dm).with(@user.id, @circle.id)
        @user_circle_permission_group.destroy
        expect(DmUtil).to have_received(:send_user_circle_permission_update_to_dm).with(@user.id, @circle.id)
      end
    end
  end

  describe 'create leader to location relation' do
    before :each do
      @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
      @district = FactoryBot.create(:circle, level: :district, circle_type: :location, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, level: :mandal, circle_type: :location, parent_circle: @district)
      @mandal2 = FactoryBot.create(:circle, level: :mandal, circle_type: :location, parent_circle: @district)
      @village = FactoryBot.create(:circle, level: :village, circle_type: :location, parent_circle: @mandal)
      @village2 = FactoryBot.create(:circle, level: :village, circle_type: :location, parent_circle: @mandal)
      @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)
      @circle = FactoryBot.create(:circle, level: :political_leader)
    end

    it 'should create leader to location relation' do
      @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, circle: @circle, user: @user,
                                                        permission_group_id: Constants.owner_permission_group_id)
      expect(CirclesRelation.where(first_circle_id: @circle.id, second_circle_id: @mandal.id, relation: :Leader2Location).exists?).to be_truthy
    end

    it 'if relation already exists updates the location circle id' do
      @user2 = FactoryBot.create(:user, village_id: @village2.id, mandal_id: @mandal2.id, district_id: @district.id, state_id: @state.id)
      @permission_group = FactoryBot.build(:permission_group)
      @permission_group.save(validate: false)
      @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
      @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user2, circle: @circle, permission_group_id: @permission_group.id)

      @user_circle_permission_group.update(permission_group_id: Constants.owner_permission_group_id)
      expect(CirclesRelation.where(first_circle_id: @circle.id, second_circle_id: @mandal2.id, relation: :Leader2Location).exists?).to be_truthy
    end
  end
end
