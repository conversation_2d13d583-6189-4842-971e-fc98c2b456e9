require 'rails_helper'
require 'spec_helper'

RSpec.describe User::UserLayoutConcern, type: :model do
  describe "user_json_for_rm_layout_creation_tool" do
    let(:user) { create(:user) }
    let(:poster_layout) { create(:poster_layout, user: user) }
    let(:circle) { create(:circle) }
    let(:photo) { create(:photo) }
    it "returns correct JSON structure with all fields populated" do
      allow(user).to receive(:village_id).and_return(circle.id)
      allow(user).to receive(:mandal_id).and_return(circle.id)
      allow(user).to receive(:district_id).and_return(circle.id)
      allow(user).to receive(:state_id).and_return(circle.id)
      allow(user).to receive(:photo).and_return(photo)
      allow(user).to receive(:name).and_return("User Name")
      allow(user).to receive(:dob).and_return("2000-01-01")
      allow(user).to receive(:user_badge_json_including_unverified).and_return("badge_json")
      allow(user).to receive(:get_affiliated_circle_json_for_rm_layout_creation).and_return("affiliated_party_json")
      allow(user).to receive(:get_profession_for_rm_layout_creation).and_return("profession_json")

      result = user.user_json_for_rm_layout_creation_tool

      expect(result).to include(
                          photo: photo,
                          name: "User Name",
                          village: circle,
                          mandal: circle,
                          district: circle,
                          state: circle,
                          badge: "badge_json",
                          dob: "2000-01-01",
                          affiliated_party: "affiliated_party_json",
                          profession: "profession_json"
                        )
    end
  end

  describe "get_profession_for_rm_layout_creation" do
    let(:user) { create(:user) }
    let(:profession) { create(:profession, name_en: "Engineer") }
    let(:sub_profession) { create(:sub_profession, name_en: "Software", profession: profession) }
    let(:user_profession) { create(:user_profession, user: user, profession: profession, sub_profession: sub_profession) }
    it "returns profession and sub profession names if both are present" do
      allow(UserProfession).to receive(:where).with(user: user).and_return([user_profession])

      result = user.get_profession_for_rm_layout_creation

      expect(result).to eq("Engineer (Software)")
    end

    it "returns only profession name if sub profession is absent" do
      user_profession.update(sub_profession: nil)
      allow(UserProfession).to receive(:where).with(user: user).and_return([user_profession])
      result = user.get_profession_for_rm_layout_creation

      expect(result).to eq("Engineer")
    end

    it "returns nil if no profession is found" do
      allow(UserProfession).to receive(:where).with(user: user).and_return([])
      result = user.get_profession_for_rm_layout_creation

      expect(result).to be_nil
    end
  end

  describe "get_affiliated_circle_id_for_rm_layout_creation" do
    let(:user) { create(:user) }
    let(:affiliated_party_circle) { create(:circle) }
    let(:poster_layout) { create(:poster_layout, user: user) }
    it "returns poster affiliated party id if user has premium layout" do
      allow(user).to receive(:get_user_poster_layout_including_inactive).and_return(true)
      allow(user).to receive(:get_poster_affiliated_party_id).and_return(affiliated_party_circle.id)

      result = user.get_affiliated_circle_id_for_rm_layout_creation

      expect(result).to eq(affiliated_party_circle.id)
    end

    it "returns affiliated party circle id if user does not have premium layout" do
      allow(user).to receive(:has_premium_layout?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(affiliated_party_circle.id)

      result = user.get_affiliated_circle_id_for_rm_layout_creation

      expect(result).to eq(affiliated_party_circle.id)
    end

    it "returns nil if no affiliated party circle id is found" do
      allow(user).to receive(:has_premium_layout?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(nil)

      result = user.get_affiliated_circle_id_for_rm_layout_creation

      expect(result).to be_nil
    end
  end

  describe "get_affiliated_circle_json_for_rm_layout_creation" do
    let(:user) { create(:user) }
    let(:circle) { create(:circle) }
    it "returns JSON for the affiliated circle if it exists" do
      allow(user).to receive(:get_affiliated_circle_id_for_rm_layout_creation).and_return(circle.id)
      allow(Circle).to receive(:find_by).with(id: circle.id).and_return(circle)
      allow(circle).to receive(:get_json_for_rm_layout_creation).and_return({ name: "Circle Name" })

      result = user.get_affiliated_circle_json_for_rm_layout_creation
      expect(result).to eq({ name: "Circle Name" })
    end

    it "returns nil if no affiliated circle is found" do
      allow(user).to receive(:get_affiliated_circle_id_for_rm_layout_creation).and_return(nil)
      allow(Circle).to receive(:find_by).with(id: nil).and_return(nil)

      result = user.get_affiliated_circle_json_for_rm_layout_creation
      expect(result).to be_nil
    end

    it "returns nil if the affiliated circle does not exist" do
      allow(user).to receive(:get_affiliated_circle_id_for_rm_layout_creation).and_return(circle.id)
      allow(Circle).to receive(:find_by).with(id: circle.id).and_return(nil)

      result = user.get_affiliated_circle_json_for_rm_layout_creation
      expect(result).to be_nil
    end
  end

  describe "#get_badge_free_text_for_rm_layout_creation" do
    it "returns the latest badge free text if present" do
      user = FactoryBot.create(:user)
      FactoryBot.create(:user_metadatum, user: user, key: Constants.badge_free_text_key, value: "Free Text")
      result = user.get_badge_free_text_for_rm_layout_creation
      expect(result).to eq("Free Text")
    end

    it "returns nil if no badge free text is found" do
      user = FactoryBot.create(:user)
      result = user.get_badge_free_text_for_rm_layout_creation
      expect(result).to be_nil
    end
  end

  describe "get_affiliated_circles_for_rm_layout_creation" do
    let(:user) { create(:user) }
    let(:poster_layout) { create(:user_poster_layout, entity: user) }
    let(:circle) { create(:circle) }
    let(:photo) { create(:photo) }

    it "returns existing circles with their details" do
      leader_photo = FactoryBot.create(:user_leader_photo, circle: circle, photo: photo,
                                       user_poster_layout: poster_layout)
      poster_photo = FactoryBot.create(:circle_photo, circle: circle, photo: photo, photo_type: :poster)

      result = user.get_affiliated_circles_for_rm_layout_creation(poster_layout)
      circle_json = leader_photo.circle.get_json_for_rm_layout_creation(poster_photos_required: true)
      circle_json[:priority] = leader_photo.priority
      circle_json[:type] = leader_photo.header_type
      if photo = circle_json[:poster_photos].find { |photo| photo[:id] == leader_photo.photo_id }
        photo[:selected] = true
      end
      expect(result).to eq([circle_json])
    end

    it "returns requested circles with draft data if photo_id is blank" do
      leader_photo = FactoryBot.create(:user_leader_photo, circle: circle, photo: photo,
                                       user_poster_layout: poster_layout)
      leader_photo.update(circle_id: nil, draft_data: { "photo_id" => photo.id, "circle_name" => "Draft Circle Name" })

      result = user.get_affiliated_circles_for_rm_layout_creation(poster_layout)
      expect(result).to eq([{ priority: leader_photo.priority, type: leader_photo.header_type,
                              name: "Draft Circle Name",
                              name_en: "Draft Circle Name",
                              poster_photos: [
                                {
                                  id: leader_photo.draft_data.dig("photo_id"),
                                  url: photo.placeholder_url,
                                  selected: true
                                }
                              ]
                            }])
    end

    it "returns an empty array if no leader photos are present" do
      user = FactoryBot.create(:user)
      poster_layout = FactoryBot.create(:user_poster_layout, entity: user)
      result = user.get_affiliated_circles_for_rm_layout_creation(poster_layout)

      expect(result).to eq([])
    end

    it "returns an empty array if poster_layout is nil" do
      user = FactoryBot.create(:user)
      result = user.get_affiliated_circles_for_rm_layout_creation(nil)

      expect(result).to eq([])
    end
  end

  describe "#get_layout_original_identity_photos" do
    let(:user) { create(:user) }
    let(:photo1) { create(:photo, placeholder_url: "http://example.com/photo1.jpg") }
    let(:photo2) { create(:photo, placeholder_url: "http://example.com/photo2.jpg") }

    it "returns photos with correct types and URLs if photos are present" do
      create(:user_metadatum, user: user, key: Constants.poster_photo_without_background_original_key, value: photo1.id)
      create(:user_metadatum, user: user, key: Constants.hero_frame_photo_original_key, value: photo2.id)

      result = user.get_layout_original_identity_photos

      expect(result).to eq([
                             { type: Constants.poster_photo_without_background_original_key,
                               photo: {
                                 id: photo1.id,
                                 url: photo1.placeholder_url
                               } },
                             { type: Constants.hero_frame_photo_original_key,
                               photo: {
                                 id: photo2.id,
                                 url: photo2.placeholder_url
                               } }
                           ])
    end

    it "returns an empty array if no photos are found" do
      result = user.get_layout_original_identity_photos

      expect(result).to eq([])
    end

    it "returns only photos with valid URLs" do
      create(:user_metadatum, user: user, key: Constants.poster_photo_without_background_original_key, value: photo1.id)
      create(:user_metadatum, user: user, key: Constants.hero_frame_photo_original_key, value: nil)

      result = user.get_layout_original_identity_photos

      expect(result).to eq([
                             { type: Constants.poster_photo_without_background_original_key,
                               photo: {
                                 id: photo1.id,
                                 url: photo1.placeholder_url
                               } }
                           ])
    end

    it "returns an empty array if all photo URLs are invalid" do
      create(:user_metadatum, user: user, key: Constants.poster_photo_without_background_original_key, value: nil)
      create(:user_metadatum, user: user, key: Constants.hero_frame_photo_original_key, value: nil)

      result = user.get_layout_original_identity_photos

      expect(result).to eq([])
    end
  end

  describe "#user_badge_json_including_unverified" do
    it "returns the JSON of the user role if present" do
      user = FactoryBot.create(:user)
      FactoryBot.create(:user_role, user: user)
      result = user.user_badge_json_including_unverified

      expect(result).to_not be_nil
    end

    it "returns nil if no user role is found" do
      user = FactoryBot.create(:user)
      result = user.user_badge_json_including_unverified
      expect(result).to be_nil
    end
  end

  describe "#get_layout_remarks" do
    let(:user) { create(:user) }
    let(:poster_layout) { create(:user_poster_layout, entity: user) }

    it "returns the latest remark of the layout if present" do
      allow(poster_layout).to receive(:get_latest_remark_of_layout).and_return("Latest Remark")
      result = user.get_layout_remarks(poster_layout)
      expect(result).to eq("Latest Remark")
    end

    it "returns nil if poster_layout is nil" do
      result = user.get_layout_remarks(nil)
      expect(result).to be_nil
    end

    it "returns nil if no remarks are found" do
      allow(poster_layout).to receive(:get_latest_remark_of_layout).and_return(nil)
      result = user.get_layout_remarks(poster_layout)
      expect(result).to be_nil
    end
  end

  describe "#get_layout_bg_removed_identity_photos" do
    let(:user) { create(:user) }
    let(:photo1) { create(:photo, placeholder_url: "http://example.com/photo1.jpg") }
    let(:photo2) { create(:photo, placeholder_url: "http://example.com/photo2.jpg") }

    before do
      # Stub the constants to avoid dependency on actual implementation
      stub_const("User::BG_REMOVAL_POSTER_PHOTO_KEYS", [
        "poster_photo_without_background",
        "hero_frame_photo",
        "family_frame_photo"
      ])
    end

    it "returns photos with correct types and URLs if photos are present" do
      create(:user_metadatum, user: user, key: "poster_photo_without_background", value: photo1.id)
      create(:user_metadatum, user: user, key: "hero_frame_photo", value: photo2.id)

      result = user.get_layout_bg_removed_identity_photos

      expect(result).to eq([
                             { type: "poster_photo_without_background",
                               photo: {
                                 id: photo1.id,
                                 url: photo1.placeholder_url
                               } },
                             { type: "hero_frame_photo",
                               photo: {
                                 id: photo2.id,
                                 url: photo2.placeholder_url
                               } }
                           ])
    end

    it "returns an empty array if no photos are found" do
      result = user.get_layout_bg_removed_identity_photos

      expect(result).to eq([])
    end

    it "returns only photos with valid URLs" do
      create(:user_metadatum, user: user, key: "poster_photo_without_background", value: photo1.id)
      create(:user_metadatum, user: user, key: "hero_frame_photo", value: nil)

      result = user.get_layout_bg_removed_identity_photos

      expect(result).to eq([
                             { type: "poster_photo_without_background",
                               photo: {
                                 id: photo1.id,
                                 url: photo1.placeholder_url
                               } }
                           ])
    end

    it "returns an empty array if all photo URLs are invalid" do
      create(:user_metadatum, user: user, key: "poster_photo_without_background", value: nil)
      create(:user_metadatum, user: user, key: "hero_frame_photo", value: nil)

      result = user.get_layout_bg_removed_identity_photos

      expect(result).to eq([])
    end

    it "handles non-existent photo IDs gracefully" do
      # Create a user metadatum with a non-existent photo ID
      non_existent_id = 999999
      create(:user_metadatum, user: user, key: "poster_photo_without_background", value: non_existent_id)
      create(:user_metadatum, user: user, key: "hero_frame_photo", value: photo2.id)

      result = user.get_layout_bg_removed_identity_photos

      # Should only include the valid photo
      expect(result).to eq([
                             { type: "hero_frame_photo",
                               photo: {
                                 id: photo2.id,
                                 url: photo2.placeholder_url
                               } }
                           ])
    end
  end
end
