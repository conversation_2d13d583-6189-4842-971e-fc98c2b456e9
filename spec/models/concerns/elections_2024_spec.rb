require 'rails_helper'
require 'spec_helper'

RSpec.describe Elections2024 do
  include Elections2024

  describe '#get_css_background' do
    context 'when there is single color in bg colors' do
      it 'returns the color' do
        hash = {
            "background_colors" => [0xff008E46],
            "background_color_stops" => nil,
            "bg_gradient_begin_x" => 1.0,
            "bg_gradient_begin_y" => -1.0,
            "bg_gradient_end_x" => 1.0,
            "bg_gradient_end_y" => 1.0,
        }

        expect(get_css_background(hash)).to eq("#008e46")
      end
    end

    context 'when there are multiple colors in bg colors, without stops' do
      it 'returns the gradient with just colors' do
        hash = {
            "background_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
            "background_color_stops" => nil,
            "bg_gradient_begin_x" => -1.0,
            "bg_gradient_begin_y" => -1.0,
            "bg_gradient_end_x" => 1.0,
            "bg_gradient_end_y" => 1.0,
        }

        expect(get_css_background(hash)).to eq("linear-gradient(45.0deg, #008e46, #22bbb8, #0266b4)")
      end
    end

    context 'when there are multiple colors in bg colors, with stops' do
      it 'returns the gradient with colors and stops' do
        hash = {
            "background_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
            "background_color_stops" => [0.0, 0.5539, 1.0],
            "bg_gradient_begin_x" => -1.0,
            "bg_gradient_begin_y" => -1.0,
            "bg_gradient_end_x" => 1.0,
            "bg_gradient_end_y" => 1.0,
        }

        expect(get_css_background(hash)).to eq("linear-gradient(45.0deg, #008e46 0.0%, #22bbb8 55.39%, #0266b4 100.0%)")
      end
    end
  end

end
