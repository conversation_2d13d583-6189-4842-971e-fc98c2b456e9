require 'rails_helper'
require 'spec_helper'

RSpec.describe UserPostersTrial do
  include UserPostersTrial

  describe "#get_poster_trial_day_and_duration" do
    context "when user is not a trial user" do
      it "returns nil" do
        user = FactoryBot.create(:user)
        expect(get_poster_trial_day_and_duration(user)).to eq(nil)
      end
    end

    context "when user is a trial user" do
      before :each do
        @user = FactoryBot.create(:user, :trial_user)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        allow(Photo).to receive(:upload).with(instance_of(ActionDispatch::Http::UploadedFile),
                                              Constants.praja_account_user_id).and_return(photo)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.save
        FactoryBot.create(:user_poster_layout, entity: @user,
                          user_leader_photos: [
                            FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                              header_type: :header_1, priority: 1),
                            FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                              header_type: :header_2, priority: 1)])
      end

      it "returns trial day and duration" do
        expect(@user.is_trial_user?).to eq(true)
        expect(get_poster_trial_day_and_duration(@user)).to eq([1, 15])
      end
    end
  end

  describe "#validate_user_status" do
    context "when entity is a User" do
      let(:active_user) { FactoryBot.create(:user, status: 'active') }
      let(:pre_signup_user) { FactoryBot.create(:user, status: 'pre_signup') }
      let(:user_poster_layout) { FactoryBot.build(:user_poster_layout, entity: active_user) }

      it "is valid when user status is active" do
        user_poster_layout.validate_user_status
        expect(user_poster_layout.errors).to be_empty
      end

      it "is invalid when user status is pre_signup" do
        user_poster_layout.entity = pre_signup_user
        user_poster_layout.validate_user_status
        expect(user_poster_layout.errors[:entity_id]).to include('User must be in active status')
      end
    end

    context "when entity is not a User" do
      let(:not_user_entity) { FactoryBot.create(:circle) }

      it "is valid and does not add an error" do
        user_poster_layout = FactoryBot.build(:user_poster_layout, entity: not_user_entity)
        user_poster_layout.validate_user_status
        expect(user_poster_layout.errors).to be_empty
      end
    end
  end
end

