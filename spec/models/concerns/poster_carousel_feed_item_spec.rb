require 'rails_helper'
require 'spec_helper'

RSpec.describe PosterCarouselFeedItem do
  include PosterCarouselFeedItem
  describe 'fetch_weighted_poster_creatives' do
    context '#fetch_weighted_poster_creatives' do
      before :each do
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id)
        @political_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                              circle_photos: [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        FactoryBot.create(:user_circle, user: @user, circle: @political_leader)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end
      it 'should return weighted poster creatives' do
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, photo_v2: @admin_medium_2,
                                             primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @political_leader)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, photo_v2: @admin_medium_2,
                                               primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @state)
        expect(fetch_weighted_poster_creatives(circle_ids: [@state.id], sub_query: '')).to eq([@poster_creative_1])
        expect(fetch_weighted_poster_creatives(circle_ids: [@political_leader.id], sub_query: ''))
          .to eq([@poster_creative])
        expect(fetch_weighted_poster_creatives(circle_ids: [@state.id, @political_leader.id], sub_query: ''))
          .to eq([@poster_creative])
      end
      it 'should not return the creative when event is inactive' do
        event = FactoryBot.create(:event)
        FactoryBot.create(:event_circle, event: event, circle: @political_leader)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, photo_v2: @admin_medium_2,
                                             primary: true, active: true, event: event)

        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @political_leader)

        # inactivate event (creatives should be automatically inactivated)
        event.active = false
        event.save

        expect(fetch_weighted_poster_creatives(circle_ids: [@political_leader.id], sub_query: '')).to eq([])
      end
    end
  end

  describe '#poster_carousel_in_feed' do
    context 'when user eligible_for_premium_pitch' do
      before :each do
        AppVersionSupport.new('2404.16.00')
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id)
        @political_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                              circle_photos: [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        FactoryBot.create(:user_circle, user: @user, circle: @political_leader)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end
      it 'should return carousel feed' do
        allow(@user).to receive(:premium_pitch_hot_lead?).and_return(true)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, photo_v2: @admin_medium_2,
                                             primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @political_leader)

        feed = poster_carousel_in_feed(user: @user)
        expect(feed[:feed_type]).to eq('poster_carousel')
        expect(feed[:items].first[:creative][:id]).to eq(@poster_creative.id)
        expect(feed[:show_help]).to eq(false)
        expect(feed[:more_cta]).to eq(nil)
        expect(feed[:more_deeplink]).to eq(nil)
        expect(feed[:feed_item_id]).to include(Constants.poster_carousel_feed_item_id)
        expect(feed[:items].size).to eq(3)
      end
    end

    context 'when user is trial user' do
      before :each do
        AppVersionSupport.new('2404.16.00')
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id)
        @political_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                              circle_photos: [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        FactoryBot.create(:user_circle, user: @user, circle: @political_leader)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end
      it 'should return carousel feed' do
        allow(@user).to receive(:premium_pitch_hot_lead?).and_return(false)
        allow(@user).to receive(:is_trial_user?).and_return(true)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, photo_v2: @admin_medium_2,
                                             primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @political_leader)

        feed = poster_carousel_in_feed(user: @user)
        expect(feed[:feed_type]).to eq('poster_carousel')
        expect(feed[:items].first[:creative][:id]).to eq(@poster_creative.id)
        expect(feed[:show_help]).to eq(false)
        expect(feed[:more_cta]).to eq(nil)
        expect(feed[:more_deeplink]).to eq(nil)
        expect(feed[:feed_item_id]).to include(Constants.poster_carousel_feed_item_id)
        expect(feed[:items].size).to eq(3)
      end
    end

    context 'when user has premium subscription' do
      before :each do
        AppVersionSupport.new('2408.05.00')
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id)
        @political_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                              circle_photos: [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        FactoryBot.create(:user_circle, user: @user, circle: @political_leader)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user_badge_circle)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)
        FactoryBot.create(:user_plan, user: @user, end_date: Time.zone.now + 1.month)
        FactoryBot.create(:user_frame, user: @user, frame: @frame1)
        FactoryBot.create(:user_frame, user: @user, frame: @frame2)
        FactoryBot.create(:user_frame, user: @user, frame: @frame3)
        FactoryBot.create(:user_frame, user: @user, frame: @frame4)
      end

      it 'should return carousel feed' do
        allow(@user).to receive(:premium_pitch_hot_lead?).and_return(false)
        allow(@user).to receive(:is_trial_user?).and_return(false)
        allow(@user).to receive(:is_poster_subscribed).and_return(true)
        @event = FactoryBot.create(:event)
        FactoryBot.create(:event_circle, event: @event, circle: @political_leader)
        @sub_query_for_free_users = @user.get_sub_query_for_free_users(@user.has_premium_layout?)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        feed = poster_carousel_in_feed(user: @user)
        expect(feed[:feed_type]).to eq('poster_carousel')
        expect(feed[:items].first[:creative][:id]).to eq(@poster_creative.id)
        expect(feed[:show_help]).to eq(true)
        expect(feed[:more_cta]).to eq('మరిన్ని డిజైన్లు పొందండి')
        expect(feed[:more_deeplink]).to start_with('/posters/layout')
        expect(feed[:feed_item_id]).to include(Constants.poster_carousel_feed_item_id)
        expect(feed[:items].size).to eq(3)

      end
      it 'should not return carousel feed if event is absent' do
        allow(@user).to receive(:premium_pitch_hot_lead?).and_return(false)
        allow(@user).to receive(:is_trial_user?).and_return(false)
        allow(@user).to receive(:is_poster_subscribed).and_return(true)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, photo_v2: @admin_medium_2,
                                             primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @political_leader)

        feed = poster_carousel_in_feed(user: @user)
        expect(feed).to eq(nil)
      end
    end
  end

  describe 'get most_relevant_creative' do
    context '#most_relevant_creative' do
      before :each do
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id)
        @political_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                              circle_photos: [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        FactoryBot.create(:user_circle, user: @user, circle: @political_leader)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end
      it 'should return most relevant creative among events' do
        @event = FactoryBot.create(:event)
        FactoryBot.create(:event_circle, event: @event, circle: @political_leader)
        @sub_query_for_free_users = @user.get_sub_query_for_free_users(@user.has_premium_layout?)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(most_relevant_creative(user: @user).first).to eq(@poster_creative)
        @event_1 = FactoryBot.create(:event)
        FactoryBot.create(:event_circle, event: @event_1, circle: @state)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        expect(most_relevant_creative(user: @user).first).to eq(@poster_creative)
      end
      it 'should return most relevant creative among creatives when events are absent' do
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @state)
        expect(most_relevant_creative(user: @user).first).to eq(@poster_creative)
      end
    end
  end

  describe '#poster_carousel_in_premium_benefits_loss_screen' do
    context 'when user has premium subscription and layout' do
      before :each do
        AppVersionSupport.new('2408.05.00')
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id,
                                  state_id: @state.id)
        @political_leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                              circle_photos: [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        FactoryBot.create(:user_circle, user: @user, circle: @political_leader)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user_badge_circle)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)
        FactoryBot.create(:user_plan, user: @user, end_date: Time.zone.now + 1.month)
        FactoryBot.create(:user_frame, user: @user, frame: @frame1)
        FactoryBot.create(:user_frame, user: @user, frame: @frame2)
        FactoryBot.create(:user_frame, user: @user, frame: @frame3)
        FactoryBot.create(:user_frame, user: @user, frame: @frame4)
      end

      it 'should return carousel feed with upcoming events with events size count' do
        allow(@user).to receive(:is_poster_subscribed).and_return(true)
        allow(@user).to receive(:has_premium_layout?).and_return(true)

        # Create upcoming events
        @event1 = FactoryBot.create(:event, start_time: Time.zone.now + 1.day, end_time: Time.zone.now + 2.days)
        @event2 = FactoryBot.create(:event, start_time: Time.zone.now + 3.days, end_time: Time.zone.now + 4.days)
        FactoryBot.create(:event_circle, event: @event1, circle: @political_leader)
        FactoryBot.create(:event_circle, event: @event2, circle: @political_leader)

        # Create poster creatives for these events
        @poster_creative1 = FactoryBot.create(:poster_creative, event: @event1, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 1.day, end_time: Time.zone.now + 2.days)
        @poster_creative2 = FactoryBot.create(:poster_creative, event: @event2, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 3.days, end_time: Time.zone.now + 4.days)
        feed = poster_carousel_in_premium_benefits_loss_screen(user: @user)

        expect(feed).not_to be_nil
        expect(feed[:feed_type]).to eq('poster_carousel')
        expect(feed[:items].size).to be > 0
        expect(feed[:items].size).to eq(2)
        expect(feed[:items].first).to have_key(:event_date)
        expect(feed[:items].first).to have_key(:layout)
        expect(feed[:items].first).to have_key(:creative)
        expect(feed[:items].first).to have_key(:params)
        expect(feed[:show_help]).to eq(false)
      end

      it 'should return carousel feed with upcoming events with layouts size count' do
        allow(@user).to receive(:is_poster_subscribed).and_return(true)
        allow(@user).to receive(:has_premium_layout?).and_return(true)
        # Create upcoming events
        @event1 = FactoryBot.create(:event, start_time: Time.zone.now + 1.day, end_time: Time.zone.now + 2.days)
        @event2 = FactoryBot.create(:event, start_time: Time.zone.now + 3.days, end_time: Time.zone.now + 4.days)
        @event3 = FactoryBot.create(:event, start_time: Time.zone.now + 5.days, end_time: Time.zone.now + 6.days)
        @event4 = FactoryBot.create(:event, start_time: Time.zone.now + 7.days, end_time: Time.zone.now + 8.days)
        @event5 = FactoryBot.create(:event, start_time: Time.zone.now + 9.days, end_time: Time.zone.now + 10.days)
        FactoryBot.create(:event_circle, event: @event1, circle: @political_leader)
        FactoryBot.create(:event_circle, event: @event2, circle: @political_leader)
        FactoryBot.create(:event_circle, event: @event3, circle: @political_leader)
        FactoryBot.create(:event_circle, event: @event4, circle: @political_leader)
        FactoryBot.create(:event_circle, event: @event5, circle: @political_leader)

        # Create poster creatives for these events
        @poster_creative1 = FactoryBot.create(:poster_creative, event: @event1, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 1.day, end_time: Time.zone.now + 2.days)
        @poster_creative2 = FactoryBot.create(:poster_creative, event: @event2, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 3.days, end_time: Time.zone.now + 4.days)
        @poster_creative3 = FactoryBot.create(:poster_creative, event: @event3, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 5.days, end_time: Time.zone.now + 6.days)
        @poster_creative4 = FactoryBot.create(:poster_creative, event: @event4, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 7.days, end_time: Time.zone.now + 8.days)
        @poster_creative5 = FactoryBot.create(:poster_creative, event: @event5, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now + 9.days, end_time: Time.zone.now + 10.days)

        feed = poster_carousel_in_premium_benefits_loss_screen(user: @user)

        expect(feed).not_to be_nil
        expect(feed[:feed_type]).to eq('poster_carousel')
        expect(feed[:items].size).to be > 0
        expect(feed[:items].size).to eq(4)
        expect(feed[:items].first).to have_key(:event_date)
        expect(feed[:items].first).to have_key(:layout)
        expect(feed[:items].first).to have_key(:creative)
        expect(feed[:items].first).to have_key(:params)
        expect(feed[:show_help]).to eq(false)
      end

      it 'should return nil when user is not poster subscribed' do
        allow(@user).to receive(:is_poster_subscribed).and_return(false)
        allow(@user).to receive(:has_premium_layout?).and_return(true)

        feed = poster_carousel_in_premium_benefits_loss_screen(user: @user)
        expect(feed).to be_nil
      end

      it 'should return nil when user does not have premium layout' do
        allow(@user).to receive(:is_poster_subscribed).and_return(true)
        allow(@user).to receive(:has_premium_layout?).and_return(false)

        feed = poster_carousel_in_premium_benefits_loss_screen(user: @user)
        expect(feed).to be_nil
      end

      it 'should return nil when there are no upcoming event creatives' do
        allow(@user).to receive(:is_poster_subscribed).and_return(true)
        allow(@user).to receive(:has_premium_layout?).and_return(true)
        allow(PosterCreative).to receive(:upcoming_event_creatives).and_return([])

        feed = poster_carousel_in_premium_benefits_loss_screen(user: @user)
        expect(feed).to be_nil
      end
    end
  end
end
