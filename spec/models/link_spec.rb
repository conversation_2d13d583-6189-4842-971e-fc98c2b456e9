require "rails_helper"

RSpec.describe <PERSON>, type: :model do
  context "extract_preview_info" do
    let(:url) { Faker::Internet.url}
    let(:link) { FactoryBot.create(:link) }
    let(:user) { FactoryBot.create(:user)}
    let(:response) { double("response") }

    it "should return nil if url is blank" do
      expect(Link.extract_preview_info("", nil)).to eq(nil)
    end

    it "should return link if link is found" do
      allow(Link).to receive(:where).and_return([link])
      expect(Link.extract_preview_info(link.url, user)).to eq(link)
    end

    it "should return nil if response code is not 200" do
      allow(Net::HTTP).to receive(:get_response).and_return(response)
      allow(response).to receive(:code).and_return(404)

      expect(Link.extract_preview_info(url, user)).to eq(nil)
    end

    it "should return nil if raises exception" do
      allow(Link).to receive(:where).and_return([])
      allow(Rails.application.credentials).to receive(:[]).with(:link_preview_key).and_return("test_link_preview_key")
      allow(Net::HTTP).to receive(:get_response).and_raise(StandardError)

      expect(Link.extract_preview_info(url, user)).to eq(nil)
    end

    it "should return nil if user is not found" do
      allow(Link).to receive(:where).and_return([])
      allow(Rails.application.credentials).to receive(:[]).with(:link_preview_key).and_return("test_link_preview_key")
      allow(Net::HTTP).to receive(:get_response).and_return(response)
      allow(response).to receive(:code).and_return(200)
      allow(response).to receive(:body).and_return({"title" => "test_title", "description" => "test_description", "image" => "test_image", "url" => url})
      allow(ActiveSupport::JSON).to receive(:decode).and_return({"title" => "test_title", "description" => "test_description", "image" => "test_image", "url" => url})
      allow(Link).to receive(:create)

      expect(Link.extract_preview_info(url, nil)).to eq(nil)
      expect(Link.where(url: url, active: true).first).to eq(nil)
    end

    it "should create new link if response is 200" do
      expect(Link.where(url: url, active: true).first).to eq(nil)
      new_link = FactoryBot.create(:link, title: "test_title", description: "test_description", image: "image", url: url, user: user)
      allow(Link).to receive(:where).and_return([])
      allow(Rails.application.credentials).to receive(:[]).with(:link_preview_key).and_return("test_link_preview_key")
      allow(Net::HTTP).to receive(:get_response).and_return(response)
      allow(response).to receive(:code).and_return(200)
      allow(response).to receive(:body).and_return({"title" => "test_title", "description" => "test_description", "image" => "test_image", "url" => url})
      allow(ActiveSupport::JSON).to receive(:decode).and_return({"title" => "test_title", "description" => "test_description", "image" => "test_image", "url" => url})
      allow(Link).to receive(:create).and_return(new_link)

      expect(Link.extract_preview_info(url, user)).to eq(new_link)
      expect(Link).to have_received(:create)
    end
  end
end
