require 'rails_helper'

RSpec.describe CirclePremiumInterest, type: :model do
  describe '#fan_poster_interested_users' do
    context 'when user has fan poster interest' do
      it 'returns the interested users for the political circle' do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        user1 = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        user2 = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)

        circle_premium_interest1 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user1.id)
        circle_premium_interest2 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user2.id)
        users = described_class.fan_poster_interested_users(@circle)
        expect(users.count).to eq(2)
      end

      it 'returns the interested users for the political leader circle who is MLA' do
        @political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader)
        @mp_const_circle = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_const_circle)
        FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency.id, second_circle_id: @circle.id,
                          relation: :MLA)
        FactoryBot.create(:circles_relation, first_circle_id: @circle.id, second_circle_id: @political_circle.id,
                          relation: :Leader2Party)

        user1 = FactoryBot.create(:user)
        user2 = FactoryBot.create(:user)

        circle_premium_interest1 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user1.id)
        circle_premium_interest2 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user2.id)
        users = described_class.fan_poster_interested_users(@circle)
        expect(users.count).to eq(2)
      end

      it 'returns the interested users for the political leader circle who is MP' do
        @political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader)
        @mp_const_circle = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency,
                                              parent_circle: @mp_const_circle)
        FactoryBot.create(:circles_relation, first_circle_id: @mp_const_circle.id, second_circle_id: @circle.id,
                          relation: :MP)
        FactoryBot.create(:circles_relation, first_circle_id: @circle.id, second_circle_id: @political_circle.id,
                          relation: :Leader2Party)

        user1 = FactoryBot.create(:user)
        user2 = FactoryBot.create(:user)

        circle_premium_interest1 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user1.id)
        circle_premium_interest2 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user2.id)
        users = described_class.fan_poster_interested_users(@circle)
        expect(users.count).to eq(2)
      end
    end
  end

  describe '#fan_poster_interested_users_count' do
    context 'when user has fan poster interest' do
      it 'returns the interested users count for the political circle' do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        user1 = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        user2 = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)

        circle_premium_interest1 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user1.id)
        circle_premium_interest2 = FactoryBot.create(:circle_premium_interest, circle_id: @circle.id,
                                                     key: Constants.user_fan_poster_interests_key, user_id: user2.id)
        count = described_class.fan_poster_interested_users_count(@circle.id)
        expect(count).to eq(2)
      end
    end
  end

  describe '#has_fan_poster_interest?' do
    context 'when user has fan poster interest' do
      it 'returns true' do
        circle = FactoryBot.create(:circle)
        user = FactoryBot.create(:user)
        circle_premium_interest = FactoryBot.create(:circle_premium_interest, circle_id: circle.id,
                                                    key: Constants.user_fan_poster_interests_key, user_id: user.id)
        expect(described_class.has_fan_poster_interest?(circle_id: circle.id, user_id: user.id)).to be_truthy
      end
    end
  end
end
