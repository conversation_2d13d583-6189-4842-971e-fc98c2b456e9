require 'rails_helper'

RSpec.describe Metadatum, type: :model do
  # describe "#save_user_trial_data" do
  #   context "when user trial data saved" do
  #     it "should save user trial data" do
  #       user = create(:user)
  #       user_device_token = FactoryBot.create(:user_device_token, user_id: user.id)
  #       allow(UserDeviceToken).to receive(:where).and_return([user_device_token])
  #
  #       allow(EventTracker).to receive(:perform_async).with(user.id, "poster_trial_notification_sent", {
  #         "user_id" => user.id,
  #         "trial_duration" => 15
  #       })
  #
  #       allow(EventTracker).to receive(:perform_async).with(user.id, "poster_trial_created", {
  #         "trial_start_date" => Time.zone.today.strftime('%Y-%m-%d')
  #       })
  #
  #       allow(EventTracker).to receive(:perform_async).with(user.id, "poster_trial_started", {
  #         "trial_duration" => 15,
  #         "trial_start_date" => Time.zone.today,
  #         "trial_end_date" => Time.zone.today.advance(days: 15 - 1)
  #       })
  #
  #       allow(GarudaNotification).to receive(:send_user_notification)
  #
  #       Metadatum.save_user_trial_data(user, Time.zone.today, 15)
  #       expect(Metadatum.get_user_trial_start_date_and_duration(user)).to eq([Time.zone.today, 15])
  #
  #       expect(EventTracker).to have_received(:perform_async).with(user.id, "poster_trial_notification_sent", {
  #         "user_id" => user.id,
  #         "trial_duration" => 15
  #       })
  #
  #       expect(EventTracker).to have_received(:perform_async).with(user.id, "poster_trial_created", {
  #         "trial_start_date" => Time.zone.today.strftime('%Y-%m-%d')
  #       })
  #
  #       expect(EventTracker).to have_received(:perform_async).with(user.id, "poster_trial_started", {
  #         "trial_duration" => 15,
  #         "trial_start_date" => Time.zone.today,
  #         "trial_end_date" => Time.zone.today.advance(days: 15 - 1)
  #       })
  #
  #       expect(GarudaNotification).to have_received(:send_user_notification)
  #     end
  #
  #   end
  # end

  describe "#send_trial_ended_event_to_mixpanel" do
    context "when trial ended" do
      it "should send trial ended event to mixpanel" do
        user = create(:user)
        allow(EventTracker).to receive(:perform_async).with(user.id, "poster_trial_ended", {
          "trial_end_date" => Time.zone.today.strftime('%Y-%m-%d')
        })
        Metadatum.create(entity: user, key: Constants.user_poster_trial_end_date_key, value: Time.zone.today)
        expect(Metadatum.where(entity: user, key: Constants.user_poster_trial_end_date_key).count).to eq(1)
        expect(Metadatum.where(entity: user, key: Constants.user_poster_trial_end_date_key).last.value)
          .to eq(Time.zone.today.strftime('%Y-%m-%d'))
        expect(EventTracker).to have_received(:perform_async).with(user.id, "poster_trial_ended", {
          "trial_end_date" => Time.zone.today.strftime('%Y-%m-%d')
        })
      end
    end
  end

end
