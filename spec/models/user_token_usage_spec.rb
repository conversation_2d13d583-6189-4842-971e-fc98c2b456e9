require 'rails_helper'

RSpec.describe UserTokenUsage, type: :model do

  describe "#after_create_commit :record_last_online_time" do
    let(:user) { create(:user) }
    let(:user_token) { create(:user_token, user: user) }

    context "when user.get_floww_contact_id is present" do
      it "should call UpdateLeadScore.perform_async with user.id" do
        allow(user).to receive(:get_floww_contact_id).and_return("123")
        allow(UpdateLeadScore).to receive(:perform_async).with(user.id)
        allow($redis).to receive(:zadd).with(Constants.update_floww_lead_score_based_on_last_online_key,
                                             Time.zone.now.to_i + 1.hour.to_i, user.id)
        allow($redis).to receive(:zrangebyscore).with(Constants.update_floww_lead_score_based_on_last_online_key,
                                                      0, Time.zone.now.to_i + 1.hour.to_i).and_return([user.id.to_s])
        FactoryBot.create(:user_token_usage, user: user, user_token: user_token)
        expect(UpdateLeadScore).to have_received(:perform_async).with(user.id)
        expect($redis.zrangebyscore(Constants.update_floww_lead_score_based_on_last_online_key, 0,
                                    Time.zone.now.to_i + 1.hour.to_i)).to include(user.id.to_s)
      end

      it 'when user is already is subscribed should not call' do
        allow(user).to receive(:get_floww_contact_id).and_return("123")
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).with(user.id).and_return(true)
        allow(UpdateLeadScore).to receive(:perform_async).with(user.id)
        allow($redis).to receive(:zadd).with(Constants.update_floww_lead_score_based_on_last_online_key,
                                             Time.zone.now.to_i + 1.hour.to_i, user.id)
        FactoryBot.create(:user_token_usage, user: user, user_token: user_token)
        expect(UpdateLeadScore).not_to have_received(:perform_async).with(user.id)
        expect($redis.zrangebyscore(Constants.update_floww_lead_score_based_on_last_online_key, 0,
                                    Time.zone.now.to_i + 1.hour.to_i)).not_to include(user.id.to_s)
      end

    end
  end
end
