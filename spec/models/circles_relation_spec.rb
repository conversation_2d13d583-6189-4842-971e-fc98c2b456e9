require 'rails_helper'

RSpec.describe CirclesRelation, type: :model do
  describe CirclesRelation, :type => :model do
    context "validate circles relation model" do
      before :each do
        @first_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])
        @second_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @first_circle,
                                              second_circle: @second_circle,
                                              active: true,
                                              relation: :Leader2Party)
      end

      it "should create circles relation" do
        expect(@circles_relation).to be_valid
      end

      it "should raise error if first circle is not present" do
        @circles_relation.first_circle = nil
        expect(@circles_relation).to_not be_valid
        # expect { @circles_relation.save! }.to raise_error(ActiveRecord::RecordInvalid)
      end

      it "should raise error if second circle is not present" do
        @circles_relation.second_circle = nil
        expect(@circles_relation).to_not be_valid
      end

      it "should not create circles relation with same circles" do
        @circles_relation.first_circle = @party_circle
        expect(@circles_relation).to_not be_valid
      end
    end

    context "validate first_circle_id and second_circle_id" do
      before :each do
        @first_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])
        @second_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @first_circle,
                                              second_circle: @second_circle,
                                              active: true,
                                              relation: :Leader2Party)
      end

      it "should raise error if first_circle_id is not integer" do
        @circles_relation.first_circle_id = "abc"
        expect(@circles_relation).to_not be_valid
      end

      it "should raise error if second_circle_id is not integer" do
        @circles_relation.second_circle_id = "abc"
        expect(@circles_relation).to_not be_valid
      end
    end

    context "validate relation" do
      before :each do
        @first_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])
        @second_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @first_circle,
                                              second_circle: @second_circle,
                                              active: true,
                                              relation: :Leader2Party)
      end

      it "should raise error if relation is not present" do
        @circles_relation.relation = nil
        expect(@circles_relation).to_not be_valid
      end

      it "should raise error if relation is not in relations" do
        expect { @circles_relation.relation = "Leader2Interest" }.to raise_error(ArgumentError, "'Leader2Interest' is not a valid relation")
      end
    end
  end

  describe "validate MLA relation with circles types" do
    before :each do
      @mp_constituency_circle = FactoryBot.create(:circle, level: :mp_constituency, circle_type: :location)
      @first_circle = FactoryBot.create(:circle, level: :mla_constituency, circle_type: :location,
                                        parent_circle: @mp_constituency_circle)
      @second_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                         circle_photos:
                                           [FactoryBot.build(:circle_photo,
                                                             photo: FactoryBot.create(:photo),
                                                             photo_type: :poster,
                                                             photo_order: 1)])
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :MLA)
    end
    it "should be valid if relation is MLA when first circle level is mla_constituency and second circle level
        is political_leader" do
      expect(@circles_relation).to be_valid
    end

    it "should not be valid if relation is not MLA when first circle level is mla_constituency and second circle level
        is political_leader" do
      @circles_relation.relation = :MP
      expect(@circles_relation).to_not be_valid
    end
  end

  describe "validate MP relation with circles types" do
    before :each do
      @first_circle = FactoryBot.create(:circle, level: :mp_constituency, circle_type: :location)
      @second_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                         circle_photos:
                                           [FactoryBot.build(:circle_photo,
                                                             photo: FactoryBot.create(:photo),
                                                             photo_type: :poster,
                                                             photo_order: 1)])
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :MP)
    end
    it "should be valid if relation is MP when first circle level is mp_constituency and second circle level
        is political_leader" do
      expect(@circles_relation).to be_valid
    end

    it "should not be valid if relation is not MP when first circle level is mp_constituency and second circle level
        is political_leader" do
      @circles_relation.relation = :MLA
      expect(@circles_relation).to_not be_valid
    end
  end

  describe "validate MLC, MLC Contestant, MP Rajyasabha relation with circles type" do
    before :each do
      @state_circle = FactoryBot.create(:circle, level: :state, circle_type: :location)
      @first_circle = FactoryBot.create(:circle, level: :district, circle_type: :location, parent_circle: @state_circle)
      @second_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest)
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :MLC)
    end
    context "When relation is MLC" do
      it "should validate the MLC relation when first circle level is district and second circle level
        is political leader" do
        expect(@circles_relation).to be_valid
      end

      it "should not be valid if relation is not MLC when first circle level is district and second circle level
        is political leader" do
        @circles_relation.relation = :MLA
        expect(@circles_relation).to_not be_valid
      end
    end

    context "When relation is MLC Contestant" do
      it "should validate the MLC Contestant relation when first circle level is district and second circle level
        is political leader" do
        expect(@circles_relation).to be_valid
      end

      it "should not be valid if relation is not MLC Contestant when first circle level is district and second circle level
        is political leader" do
        @circles_relation.relation = :MLA
        expect(@circles_relation).to_not be_valid
      end
    end

    context "When relation is MP Rajyasabha" do
      it "should validate the MP Rajyasabha relation when first circle level is district and second circle level
        is political_leader" do
        expect(@circles_relation).to be_valid
      end

      it "should not be valid if relation is not MP Rajyasabha when first circle level is district and second circle level
        is political leader" do
        @circles_relation.relation = :MLA
        expect(@circles_relation).to_not be_valid
      end
    end
  end

  describe "validate Mandal2MLA relation with circles types" do
    before :each do
      @state_circle = FactoryBot.create(:circle, level: :state, circle_type: :location)
      @district_circle = FactoryBot.create(:circle, level: :district, circle_type: :location, parent_circle:
        @state_circle)
      @first_circle = FactoryBot.create(:circle, level: :mandal, circle_type: :location, parent_circle:
        @district_circle)
      @mp_constituency_circle = FactoryBot.create(:circle, level: :mp_constituency, circle_type: :location)
      @second_circle = FactoryBot.create(:circle, level: :mla_constituency, circle_type: :location,
                                         parent_circle: @mp_constituency_circle)
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :Mandal2MLA)
    end
    it "should be valid if relation is Mandal2MLA when first circle level is mandal and second circle level
        is mla_constituency" do
      expect(@circles_relation).to be_valid
    end

    it "should not be valid if relation is not Mandal2MLA when first circle level is mandal and second circle level
        is mla_constituency" do
      @circles_relation.relation = :MP
      expect(@circles_relation).to_not be_valid
    end
  end

  describe "validate Leader2Party relation with circles types" do
    before :each do
      @first_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                        circle_photos:
                                          [FactoryBot.build(:circle_photo,
                                                            photo: FactoryBot.create(:photo),
                                                            photo_type: :poster,
                                                            photo_order: 1)])
      @second_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :Leader2Party)
    end
    it "should be valid if relation is Leader2Party when first circle level is political_leader and second circle level
        is political_party" do
      expect(@circles_relation).to be_valid
    end

    it "should not be valid if relation is not Leader2Party when first circle level is political_leader and second circle level
        is political_party" do
      @circles_relation.relation = :MLA
      expect(@circles_relation).to_not be_valid
    end
  end

  describe "validate Leader2State relation with circles types" do
    before :each do
      @first_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                        circle_photos:
                                          [FactoryBot.build(:circle_photo,
                                                            photo: FactoryBot.create(:photo),
                                                            photo_type: :poster,
                                                            photo_order: 1)])
      @second_circle = FactoryBot.create(:circle, level: :state, circle_type: :location)
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :Leader2State)
    end
    it "should be valid if relation is Leader2State when first circle level is political_leader and second circle level
        is state" do
      expect(@circles_relation).to be_valid
    end

    it "should not be valid if relation is not Leader2State when first circle level is political_leader and second circle level
        is state" do
      @circles_relation.relation = :MLA
      expect(@circles_relation).to_not be_valid
    end
  end

  describe "validate Leader2Location relation validations" do
    before :each do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)

      @leader_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                         circle_photos:
                                           [FactoryBot.build(:circle_photo,
                                                             photo: FactoryBot.create(:photo),
                                                             photo_type: :poster,
                                                             photo_order: 1)])
      @leader_circle2 = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])

    end
    it "should validate Leader2Location relation with location circles types" do
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @leader_circle,
                                            second_circle: @district,
                                            active: true,
                                            relation: :Leader2Location)
      expect(@circles_relation).to be_valid
    end

    it "should not validate Leader2Location relation with non location circles types" do
      @circles_relation = FactoryBot.build(:circles_relation,
                                            first_circle: @leader_circle,
                                            second_circle: @leader_circle2,
                                            active: true,
                                            relation: :Leader2Location)
      expect(@circles_relation).to_not be_valid
    end

    it "should not validate more than one Leader2Location relation for a leader" do
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @leader_circle,
                                            second_circle: @mandal,
                                            active: true,
                                            relation: :Leader2Location)

      @circles_relation2 = FactoryBot.build(:circles_relation,
                                            first_circle: @leader_circle,
                                            second_circle: @district,
                                            active: true,
                                            relation: :Leader2Location)

      expect(@circles_relation2).to_not be_valid
    end


  end

  describe "validate Party2State relation with circles types" do
    before :each do
      @first_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
      @second_circle = FactoryBot.create(:circle, level: :state, circle_type: :location)
      @circles_relation = FactoryBot.create(:circles_relation,
                                            first_circle: @first_circle,
                                            second_circle: @second_circle,
                                            active: true,
                                            relation: :Party2State)
    end
    it "should be valid if relation is Party2State when first circle level is political_party and second circle level
        is state" do
      expect(@circles_relation).to be_valid
    end

    it "should not be valid if relation is not Party2State when first circle level is political_party and second circle level
        is state" do
      @circles_relation.relation = :MLA
      expect(@circles_relation).to_not be_valid
    end
  end

  describe "#generate_kyc_creative_image" do
    context "check whether generate_kyc_creative_image callback is calling or not" do
      before :each do
        allow(GenerateKycCreativeImage).to receive(:perform_async)
        @first_circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                          circle_photos:
                                            [FactoryBot.build(:circle_photo,
                                                              photo: FactoryBot.create(:photo),
                                                              photo_type: :poster,
                                                              photo_order: 1)])
        @second_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @first_circle,
                                              second_circle: @second_circle,
                                              active: true,
                                              relation: :Leader2Party)
        @mp_constituency_circle = FactoryBot.create(:circle, level: :mp_constituency, circle_type: :location)
        @mla_const_circle = FactoryBot.create(:circle, level: :mla_constituency, circle_type: :location,
                                              parent_circle: @mp_constituency_circle)
        FactoryBot.create(:circles_relation,
                          first_circle: @mla_const_circle,
                          second_circle: @first_circle,
                          active: true,
                          relation: :MLA_Contestant)

      end

      it "should call generate_kyc_creative_image after create" do
        # twice because it is calling while creating circle and also while creating mla contestant relation
        expect(GenerateKycCreativeImage).to have_received(:perform_async).twice
      end
    end
  end

end
