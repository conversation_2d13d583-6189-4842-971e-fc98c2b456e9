require 'rails_helper'
require 'webmock/rspec'

RSpec.describe GarudaNotification, type: :model do
  describe 'validations' do
    it 'fail if title is not present' do
      garuda_notification = FactoryBot.build(:garuda_notification, title: nil)
      expect(garuda_notification.valid?).to be false
    end
    it 'fail if body is not present' do
      garuda_notification = FactoryBot.build(:garuda_notification, body: nil)
      expect(garuda_notification.valid?).to be false
    end
    it 'fail if path is not present' do
      garuda_notification = FactoryBot.build(:garuda_notification, path: nil)
      expect(garuda_notification.valid?).to be false
    end
  end

  describe 'aasm' do
    let(:garuda_notification) { FactoryBot.create(:garuda_notification) }
    it 'should have created state' do
      expect(garuda_notification).to have_state(:created)
    end
    it 'validation sending state transitions' do
      expect(garuda_notification).to transition_from(:created).to(:sending).on_event(:send_notification)
      expect(garuda_notification).to_not transition_from(:stopped).to(:sending).on_event(:send_notification)
      expect(garuda_notification).to_not transition_from(:failed).to(:sending).on_event(:send_notification)
      expect(garuda_notification).to_not transition_from(:sent).to(:sending).on_event(:send_notification)
      expect(garuda_notification).to_not transition_from(:sending).to(:sending).on_event(:send_notification)
    end
    it 'validation sent state transitions' do
      expect(garuda_notification).to transition_from(:sending).to(:sent).on_event(:mark_as_sent)
      expect(garuda_notification).to transition_from(:sent).to(:sent).on_event(:mark_as_sent)
      expect(garuda_notification).to_not transition_from(:stopped).to(:sent).on_event(:mark_as_sent)
      expect(garuda_notification).to_not transition_from(:failed).to(:sent).on_event(:mark_as_sent)
      expect(garuda_notification).to_not transition_from(:created).to(:sent).on_event(:mark_as_sent)
    end
    it 'validation failed state transitions' do
      expect(garuda_notification).to transition_from(:sending).to(:failed).on_event(:mark_as_failed)
      expect(garuda_notification).to transition_from(:created).to(:failed).on_event(:mark_as_failed)
      expect(garuda_notification).to_not transition_from(:stopped).to(:failed).on_event(:mark_as_failed)
      expect(garuda_notification).to_not transition_from(:sent).to(:failed).on_event(:mark_as_failed)
      expect(garuda_notification).to_not transition_from(:failed).to(:failed).on_event(:mark_as_failed)
    end
    it 'validation stopped state transitions' do
      expect(garuda_notification).to transition_from(:created).to(:stopped).on_event(:stop)
      expect(garuda_notification).to_not transition_from(:sending).to(:stopped).on_event(:stop)
      expect(garuda_notification).to_not transition_from(:failed).to(:stopped).on_event(:stop)
      expect(garuda_notification).to_not transition_from(:sent).to(:stopped).on_event(:stop)
      expect(garuda_notification).to_not transition_from(:stopped).to(:stopped).on_event(:stop)
    end
  end

  describe 'send_at & sidekiq_job_id' do
    it 'should have default send_at & sidekiq_job_id' do
      garuda_notification = FactoryBot.create(:garuda_notification, send_at: nil)
      expect(garuda_notification.send_at).to be_present
      expect(garuda_notification.sidekiq_job_id).to be_present
    end
    it 'should have inputed send_at' do
      garuda_notification = FactoryBot.create(:garuda_notification, send_at: Time.zone.now + 1.hour)
      expect(garuda_notification.send_at).to be_present
      expect(garuda_notification.sidekiq_job_id).to be_present
    end
  end

  describe 'trigger_notification' do
    it 'should be success' do
      stub_request(:post, "#{Constants.get_garuda_base_url}/users/all/dispatch")
        .to_return(status: 200, body: { success: true, "notification_id": Nanoid.generate }.to_json, headers: {})

      garuda_notification = FactoryBot.create(:garuda_notification)
      garuda_notification.trigger_notification
      expect(garuda_notification.notification_id).to be_present
      expect(garuda_notification.sending?).to be true
    end
    it 'should be failed' do
      stub_request(:post, "#{Constants.get_garuda_base_url}/users/all/dispatch")
        .to_return(status: 400, body: {"errors": "Error"}.to_json, headers: {})

      garuda_notification = FactoryBot.create(:garuda_notification)
      garuda_notification.trigger_notification
      expect(garuda_notification.notification_id).to be_nil
      expect(garuda_notification.failed?).to be true
    end
  end

  describe "send_channel_notification" do
    it 'should be success' do
      circle_id = FactoryBot.create(:circle).id
      payload = {
        "data": {
          "sender_photo_url": "circle_photo_url",
          "sender_id": "conversation_id",
          "name": "circle_name",
          "text": "notification_body",
          "channel_id": "message",
          "channel_name": "Messages",
          "channel_description": "Messages channel for Praja Users",
          "message": {}.to_json,
          "path": "path",
        }
      }

      stub_request(:post, "#{Constants.get_garuda_base_url}/circle/#{circle_id}/dispatch")
        .to_return(status: 200, body: {"notification_id": SecureRandom.uuid}.to_json, headers: {})

      response = GarudaNotification.send_channel_notification(circle_id, payload)
      expect(response).to be true
    end
    it 'should be failed' do
      circle_id = FactoryBot.create(:circle).id
      payload = {
        "data": {
          "sender_photo_url": "circle_photo_url",
          "sender_id": "conversation_id",
          "name": "circle_name",
          "text": "notification_body",
          "channel_id": "message",
          "channel_name": "Messages",
          "channel_description": "Messages channel for Praja Users",
          "message": {}.to_json,
          "path": "path",
        }
      }

      allow(Honeybadger).to receive(:notify)
      stub_request(:post, "#{Constants.get_garuda_base_url}/circle/#{circle_id}/dispatch")
        .to_return(status: 400, body: {"errors": "Error"}.to_json, headers: {})

      response = GarudaNotification.send_channel_notification(circle_id, payload)
      expect(response).to be false
      expect(Honeybadger).to have_received(:notify)
    end
  end
end
