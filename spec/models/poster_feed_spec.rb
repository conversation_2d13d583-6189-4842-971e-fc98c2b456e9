require 'rails_helper'
require 'factory_bot_rails'

RSpec.describe PosterFeed, type: :model do
  describe 'initialization' do
    it 'initializes with attributes' do
      attributes = {
        id: 'event_123',
        created_at: Time.zone.now,
        start_date: Time.zone.now - 1.day,
        end_date: Time.zone.now + 1.day,
        views_count: 10,
        shares_count: 5,
        priority: 'high',
        primary_creative_id: 1,
        creative_ids: [1, 2, 3],
        circle_ids: [4, 5, 6],
        active: true
      }

      poster_feed = PosterFeed.new(attributes)

      expect(poster_feed.id).to eq(attributes[:id])
      expect(poster_feed.created_at).to eq(attributes[:created_at])
      expect(poster_feed.start_date).to eq(attributes[:start_date])
      expect(poster_feed.end_date).to eq(attributes[:end_date])
      expect(poster_feed.views_count).to eq(attributes[:views_count])
      expect(poster_feed.shares_count).to eq(attributes[:shares_count])
      expect(poster_feed.priority).to eq(attributes[:priority])
      expect(poster_feed.primary_creative_id).to eq(attributes[:primary_creative_id])
      expect(poster_feed.creative_ids).to eq(attributes[:creative_ids])
      expect(poster_feed.circle_ids).to eq(attributes[:circle_ids])
      expect(poster_feed.active).to eq(attributes[:active])
    end
  end

  describe 'search_data' do
    it 'returns a hash with all attributes' do
      attributes = {
        id: 'event_123',
        created_at: Time.zone.now,
        start_date: Time.zone.now - 1.day,
        end_date: Time.zone.now + 1.day,
        views_count: 10,
        shares_count: 5,
        priority: 'high',
        primary_creative_id: 1,
        creative_ids: [1, 2, 3],
        circle_ids: [4, 5, 6],
        active: true
      }

      poster_feed = PosterFeed.new(attributes)
      search_data = poster_feed.search_data

      expect(search_data).to be_a(Hash)
      expect(search_data[:id]).to eq(attributes[:id])
      expect(search_data[:created_at]).to eq(attributes[:created_at])
      expect(search_data[:start_date]).to eq(attributes[:start_date])
      expect(search_data[:end_date]).to eq(attributes[:end_date])
      expect(search_data[:views_count]).to eq(attributes[:views_count])
      expect(search_data[:shares_count]).to eq(attributes[:shares_count])
      expect(search_data[:priority]).to eq(attributes[:priority])
      expect(search_data[:primary_creative_id]).to eq(attributes[:primary_creative_id])
      expect(search_data[:creative_ids]).to eq(attributes[:creative_ids])
      expect(search_data[:circle_ids]).to eq(attributes[:circle_ids])
      expect(search_data[:active]).to eq(attributes[:active])
    end
  end

  describe '.index_name' do
    it 'returns the index name from EsUtil' do
      allow(EsUtil).to receive(:get_index_name).with('poster_feeds').and_return('test_poster_feeds')

      expect(PosterFeed.index_name).to eq('test_poster_feeds')
      expect(EsUtil).to have_received(:get_index_name).with('poster_feeds')
    end
  end

  describe '#reindex' do
    let(:poster_feed) do
      PosterFeed.new(
        id: 'event_123',
        created_at: Time.zone.now,
        start_date: Time.zone.now - 1.day,
        end_date: Time.zone.now + 1.day,
        views_count: 10,
        shares_count: 5,
        priority: 'high',
        primary_creative_id: 1,
        creative_ids: [1, 2, 3],
        circle_ids: [4, 5, 6],
        active: true
      )
    end

    it 'indexes the document in Elasticsearch' do
      allow(PosterFeed).to receive(:index_name).and_return('test_poster_feeds')
      allow(Searchkick).to receive_message_chain(:client, :index).and_return(true)

      expect(poster_feed.reindex).to be_truthy
      expect(Searchkick.client).to have_received(:index).with(
        index: 'test_poster_feeds',
        id: 'event_123',
        body: poster_feed.search_data
      )
    end

    it 'handles exceptions gracefully' do
      allow(PosterFeed).to receive(:index_name).and_return('test_poster_feeds')
      allow(Searchkick).to receive_message_chain(:client, :index).and_raise(StandardError.new('Indexing failed'))
      allow(poster_feed).to receive(:puts)

      expect { poster_feed.reindex }.not_to raise_error
      expect(poster_feed).to have_received(:puts).with('Failed to index document: Indexing failed')
    end
  end

  describe '.poster_feed_query' do
    let(:user) { instance_double(User) }
    let(:per_page) { 10 }
    let(:circle_id) { nil }
    let(:loaded_feed_item_ids) { [] }
    let(:specific_doc_id) { nil }
    let(:user_joined_circle_ids) { [101, 102, 103] }
    let(:search_results) do
      [
        double(id: 'event_1', creative_ids: [1, 2], primary_creative_id: 1),
        double(id: 'event_2', creative_ids: [3, 4], primary_creative_id: 3)
      ]
    end

    before do
      allow(user).to receive(:get_user_joined_circle_ids_for_posters_feed_for_you)
        .with(add_limit: true, add_public_and_state_circle_id: true)
        .and_return(user_joined_circle_ids)
      allow(user).to receive(:get_poster_affiliated_party_id).and_return(nil)
      allow(user).to receive(:get_badge_role_including_unverified).and_return(nil)
      allow(user).to receive(:get_user_joined_party_and_leader_ids).with(add_limit: true).and_return([104, 105])
      allow(user).to receive(:state_id).and_return(201)
      allow(Constants).to receive(:public_circle_id).and_return(301)
      allow(user).to receive(:viewed_pc_event_ids).and_return([5, 6])
      allow(user).to receive(:get_user_joined_party_circle_ids).and_return([104])
      allow(user).to receive(:get_user_joined_leader_ids).with(add_limit: true).and_return([105])
      allow(user).to receive(:village_id).and_return(401)
      allow(user).to receive(:has_premium_layout?).and_return(false)

      allow(PosterFeed).to receive(:index_name).and_return('test_poster_feeds')
      allow(Searchkick).to receive(:search).and_return(search_results)
    end

    it 'returns poster feed data' do
      result = PosterFeed.poster_feed_query(
        user: user,
        per_page: per_page,
        circle_id: circle_id,
        loaded_feed_item_ids: loaded_feed_item_ids,
        specific_doc_id: specific_doc_id
      )

      expect(result).to be_a(Hash)
      expect(result['event_1']).to eq({ creative_ids: [1, 2], primary_creative_id: 1 })
      expect(result['event_2']).to eq({ creative_ids: [3, 4], primary_creative_id: 3 })
      expect(Searchkick).to have_received(:search)
    end

    context 'when circle_id is provided' do
      let(:circle_id) { 501 }

      it 'adds circle_id filter to the query' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { circle_ids: circle_id } },
            { term: { active: true } }
          )
        end
      end
    end

    context 'when specific_doc_id is provided' do
      let(:specific_doc_id) { 'event_3' }

      it 'adds specific_doc_id to the should clause' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          should_clause = body[:query][:function_score][:query][:bool][:should]
          expect(should_clause).to include(
            { term: { _id: specific_doc_id } },
            { constant_score: { filter: { term: { _id: specific_doc_id } }, boost: 99999 } }
          )
        end
      end
    end

    context 'when user has poster_affiliated_party_id that is included in user_joined_circle_ids' do
      before do
        allow(user).to receive(:get_poster_affiliated_party_id).and_return('106')
        allow(user_joined_circle_ids).to receive(:include?).with(106).and_return(true)
        allow(CirclesRelation).to receive(:party_affiliated_circle_ids)
          .with(party_id: 106, circle_ids: user_joined_circle_ids)
          .and_return([107, 108])
      end

      it 'adjusts user_joined_circle_ids based on poster_affiliated_party_id' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { active: true } },
            { terms: { circle_ids: [106, 201, 301, 107, 108] } }
          )
        end
      end
    end

    context 'when user has poster_affiliated_party_id that is NOT included in user_joined_circle_ids' do
      before do
        allow(user).to receive(:get_poster_affiliated_party_id).and_return('106')
        allow(user_joined_circle_ids).to receive(:include?).with(106).and_return(false)
      end

      it 'sets user_joined_circle_ids to state_id and public_circle_id' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { active: true } },
            { terms: { circle_ids: [201, 301] } }
          )
        end
      end
    end

    context 'when user has premium layout and no badge user affiliated party circle id' do
      before do
        allow(user).to receive(:get_poster_affiliated_party_id).and_return(nil)
        allow(user).to receive(:has_premium_layout?).and_return(true)
        allow(user).to receive(:get_badge_role_including_unverified).and_return(double(get_badge_user_affiliated_party_circle_id: 0))
        allow(user).to receive(:get_user_joined_party_and_leader_ids).with(add_limit: true).and_return([104, 105])
      end

      it 'uses party and leader ids plus state and public circle ids' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { active: true } },
            { terms: { circle_ids: [104, 105, 201, 301] } }
          )
        end
      end
    end

    context 'when user has a role and affiliated party circle id that is included in user_joined_circle_ids' do
      let(:user_role) { double('UserRole') }

      before do
        allow(user).to receive(:get_poster_affiliated_party_id).and_return(nil)
        allow(user).to receive(:has_premium_layout?).and_return(false)
        allow(user).to receive(:get_badge_role_including_unverified).and_return(user_role)
        allow(user).to receive(:affiliated_party_circle_id).and_return(110)
        allow(user_joined_circle_ids).to receive(:include?).with(110).and_return(true)
        allow(CirclesRelation).to receive(:party_affiliated_circle_ids)
          .with(party_id: 110, circle_ids: user_joined_circle_ids)
          .and_return([111, 112])
      end

      it 'uses affiliated party circle id and relevant leader circle ids' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { active: true } },
            { terms: { circle_ids: [110, 111, 112, 201, 301] } }
          )
        end
      end
    end

    context 'when user has a role but affiliated party circle id is not included in user_joined_circle_ids' do
      let(:user_role) { double('UserRole') }

      before do
        allow(user).to receive(:get_poster_affiliated_party_id).and_return(nil)
        allow(user).to receive(:has_premium_layout?).and_return(false)
        allow(user).to receive(:get_badge_role_including_unverified).and_return(user_role)
        allow(user).to receive(:affiliated_party_circle_id).and_return(110)
        allow(user_joined_circle_ids).to receive(:include?).with(110).and_return(false)
      end

      it 'sets user_joined_circle_ids to empty array plus state and public circle ids' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { active: true } },
            { terms: { circle_ids: [201, 301] } }
          )
        end
      end
    end

    context 'when user has no role' do
      before do
        allow(user).to receive(:get_poster_affiliated_party_id).and_return(nil)
        allow(user).to receive(:has_premium_layout?).and_return(false)
        allow(user).to receive(:get_badge_role_including_unverified).and_return(nil)
        allow(user).to receive(:get_user_joined_party_and_leader_ids).with(add_limit: true).and_return([104, 105])
      end

      it 'uses party and leader ids plus state and public circle ids' do
        PosterFeed.poster_feed_query(
          user: user,
          per_page: per_page,
          circle_id: circle_id,
          loaded_feed_item_ids: loaded_feed_item_ids,
          specific_doc_id: specific_doc_id
        )

        expect(Searchkick).to have_received(:search) do |_, options, &block|
          body = {}
          block.call(body)

          filter_data = body[:query][:function_score][:query][:bool][:filter]
          expect(filter_data).to include(
            { term: { active: true } },
            { terms: { circle_ids: [104, 105, 201, 301] } }
          )
        end
      end
    end
  end

  describe '.poster_feed_filters' do
    let(:user_circle_ids) { [101, 102, 103] }
    let(:search_results) do
      [
        double(circle_ids: [101, 104]),
        double(circle_ids: [102, 105]),
        double(circle_ids: [103, 106])
      ]
    end

    before do
      allow(PosterFeed).to receive(:index_name).and_return('test_poster_feeds')
      allow(Searchkick).to receive(:search).and_return(search_results)
    end

    it 'returns filtered circle ids' do
      result = PosterFeed.poster_feed_filters(user_circle_ids: user_circle_ids)

      expect(result).to match_array([101, 102, 103])
      expect(Searchkick).to have_received(:search)
    end

    it 'returns empty array when user_circle_ids is blank' do
      result = PosterFeed.poster_feed_filters(user_circle_ids: [])

      expect(result).to eq([])
      expect(Searchkick).not_to have_received(:search)
    end

    it 'returns empty array when user_circle_ids is nil' do
      result = PosterFeed.poster_feed_filters(user_circle_ids: nil)

      expect(result).to eq([])
      expect(Searchkick).not_to have_received(:search)
    end
  end
end
