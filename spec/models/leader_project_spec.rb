require 'rails_helper'

RSpec.describe LeaderProject, type: :model do
  # pending "add some examples to (or delete) #{__FILE__}"

  describe "leader project" do
    context "validations" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
      end
      it "user_id valid with digits" do
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).to be_valid
      end

      it "user_id not valid when user doesn't exists" do
        @leader_project = FactoryBot.build(:leader_project, user_id: 0, user_role: @user_role)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect { @leader_project.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: User must exist")
      end

      it "record is not valid when given user_role of other user" do
        @user2 = FactoryBot.create(:user)
        @leader_project = FactoryBot.build(:leader_project, user: @user2, user_role: @user_role)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect { @leader_project.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: User role User role is not associated with given user")
      end

      it "user_role_id valid with digits" do
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).to be_valid
      end

      it "user_role_id not valid when user_role doesn't exists" do
        @leader_project = FactoryBot.build(:leader_project, user_id: @user.id, user_role_id: 0)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect { @leader_project.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: User role must exist")
      end

      it "record is not valid when given user_role of other user" do
        @user2 = FactoryBot.create(:user)
        @user_role2 = FactoryBot.create(:user_role, user: @user2, role: @role, parent_circle_id: @circle.id)
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role2)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect { @leader_project.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: User role User role is not associated with given user")
      end

      it "record is not valid when given user_role of other user" do
        title = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role, title: title)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).not_to be_valid
      end

      it "record is not valid when given user_role of other user" do
        body = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
                xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role, body: body)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).not_to be_valid
      end

      it "valid only if user has max of five leader projects" do
        5.times do
          lp = FactoryBot.build(:leader_project, user: @user, user_role: @user_role)
          lp.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
          lp.save!
          sleep(1.second)
        end
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect { @leader_project.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Active User already has five active leader projects, can't create more active leader projects records")
      end

      it "not valid if photo not attached" do
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role, photo: nil)
        expect(@leader_project).not_to be_valid
      end

      it "valid if photo is attached" do
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).to be_valid
      end

      it "valid if project date is within user role duration" do
        date = @user_role.start_date
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role, project_date: date.next_day)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).to be_valid
      end

      it "is not valid if project date isn't within user role duration" do
        date = @user_role.start_date
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role, project_date: date.prev_day)
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        expect(@leader_project).not_to be_valid
      end

    end
  end
end


