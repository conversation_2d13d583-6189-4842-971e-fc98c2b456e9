require 'rails_helper'

RSpec.describe PostersController, type: :controller do
  describe "GET #index" do
    context "check poster" do
      before :each do
        @user = FactoryBot.create(:user)
        @party_circle = FactoryBot.create(:circle)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "status ok if poster is present" do
        get :index, params: { poster_id: @poster.id }
        expect(response).to have_http_status(:ok)
      end

      it "status not found if poster is not present" do
        get :index, params: { poster_id: nil }
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "PUT #mark_as_seen_poster_card" do
    context "check mark as seen poster" do
      before :each do
        @user = FactoryBot.create(:user)
        @party_circle = FactoryBot.create(:circle)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "adds user into poster redis key" do
        put :mark_as_seen_poster_card, params: { poster_id: @poster.id }

        is_user_id_present = $redis.sismember("poster_share_card_#{@poster.id}", @user.id.to_s)
        expect(is_user_id_present).to be_truthy
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "GET #get_share_text_with_short_link" do
    context "check share text" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "for political party circle" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @party_circle = FactoryBot.create(:circle)
        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])

        get :get_share_text_with_short_link, params: { poster_id: @poster.id }
        body = JSON.parse(response.body)

        expect(body["text"]).not_to be_empty
        expect(response).to have_http_status(:ok)
      end

      it "for political leader circle" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @leader_circle = FactoryBot.create(:circle, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @poster = FactoryBot.create(:poster, circle: @leader_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])

        get :get_share_text_with_short_link, params: { poster_id: @poster.id }
        body = JSON.parse(response.body)

        expect(body["text"]).not_to be_empty
        expect(response).to have_http_status(:ok)
      end

      it "other circle" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @poster = FactoryBot.create(:poster, circle: @state_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])

        get :get_share_text_with_short_link, params: { poster_id: @poster.id }
        body = JSON.parse(response.body)

        expect(body["text"]).not_to be_empty
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#share" do
    before :all do
      @user = FactoryBot.create(:user)
      @frame = FactoryBot.create(:frame)
      image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
      @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, primary: true, event: nil,
                                           photo_v2: @admin_medium_2, creative_kind: :schemes)
      @token = @user.generate_jwt_token
    end

    before :each do
      AppVersionSupport.new('2404.15.00')
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2404.15.00"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    context "share frame & creative" do
      it "should return bad request when method is blank" do
        post :share, params: { method: '', creative_id: @poster_creative.id, frame_id: @frame.id }
        expect(response).to have_http_status(:bad_request)
      end

      it "should return bad request when creative_id is blank" do
        post :share, params: { method: 'whatsapp', creative_id: '', frame_id: @frame.id }
        expect(response).to have_http_status(:bad_request)
      end

      it "should return bad request when method is invalid" do
        post :share, params: { method: 'invalid', creative_id: @poster_creative.id, frame_id: @frame.id }
        expect(response).to have_http_status(:bad_request)
      end

      it "should fail if db insert fails" do
        allow(PosterShare).to receive(:create!).and_raise(StandardError)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id, frame_id: @frame.id }
        expect(response).to have_http_status(:internal_server_error)
      end

      it "should return success" do
        allow(PosterShare).to receive(:create!).and_return(true)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id, frame_id: @frame.id }
        expect(response).to have_http_status(:ok)
      end
    end

    context "creative only share" do
      it "should return bad request when method is blank" do
        post :share, params: { method: '', creative_id: @poster_creative.id }
        expect(response).to have_http_status(:bad_request)
      end

      it "should return bad request when creative_id is blank" do
        post :share, params: { method: 'whatsapp', creative_id: '' }
        expect(response).to have_http_status(:bad_request)
      end

      it "should return bad request when method is invalid" do
        post :share, params: { method: 'invalid', creative_id: @poster_creative.id }
        expect(response).to have_http_status(:bad_request)
      end

      it "should fail if db insert fails" do
        allow(PosterShare).to receive(:create!).and_raise(StandardError)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id }
        expect(response).to have_http_status(:internal_server_error)
      end

      it "should return success" do
        allow(PosterShare).to receive(:create!).and_return(true)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'deeplink url for premium experience' do
      before :each do
        AppVersionSupport.new('2410.08.00')
        @request.headers['X-App-Version'] = "2410.08.00"
      end

      it 'when deeplink url is present' do
        allow_any_instance_of(User).to receive(:deeplink_url_in_poster_share).and_return('/premium-experience?source=whatsapp')
        allow(PosterShare).to receive(:create!).and_return(true)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id, frame_id: @frame.id }
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['deeplink_url']).to eq('/premium-experience?source=whatsapp')
      end

      it 'when deeplink url is absent' do
        allow_any_instance_of(User).to receive(:deeplink_url_in_poster_share).and_return(nil)
        allow(PosterShare).to receive(:create!).and_return(true)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id, frame_id: @frame.id }
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['deeplink_url']).to be_nil
      end

    end

    context 'shared creative with basic frame' do
      before :each do
        AppVersionSupport.new('2501.23.00')
        @request.headers['X-App-Version'] = "2501.23.00"
        @basic_frame = FactoryBot.create(:frame, frame_type: :basic, gold_border: false,
                                         has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                         identity_type: :flat_user)
      end

      it 'expect premium pitch to create if user has leader profession' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        political_leader = FactoryBot.create(:profession, name: 'Political Leader', name_en: 'Political Leader',
                                             admin_medium: admin_medium, ordinal: 1)
        allow_any_instance_of(User).to receive(:leader_profession?).and_return(true)
        FactoryBot.create(:user_profession, user: @user, profession: political_leader)
        allow(PosterShare).to receive(:create!).and_return(true)
        post :share, params: { method: 'whatsapp', creative_id: @poster_creative.id, frame_id: @basic_frame.id }
        expect(response).to have_http_status(:ok)
        expect(@user.premium_pitch).to be_present
        expect(@user.premium_pitch.source).to eq("LEADER_POSTER_SHARE")
      end
    end
  end

  describe "#frames_mark_as_seen_bulk" do
    before :all do
      @user = FactoryBot.create(:user)
      @frame = FactoryBot.create(:frame)
      @token = @user.generate_jwt_token
    end

    before :each do
      AppVersionSupport.new('2404.15.00')
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2404.15.00"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "should return bad request when frame_ids is blank" do
      post :frames_mark_as_seen_bulk, params: { frame_ids: '' }
      expect(response).to have_http_status(:bad_request)
    end

    it "should return success" do
      post :frames_mark_as_seen_bulk, params: { frame_ids: { @frame.id.to_s => Time.zone.now.to_i } }
      expect(response).to have_http_status(:ok)
      expect(QueueFrameViews).to have_enqueued_sidekiq_job(@user.id, hash_including(@frame.id.to_s => (Time.zone.now.to_i - 1).to_s..Time.zone.now.to_i.to_s))
    end
  end

  describe "#poster_creatives_mark_as_seen_bulk" do
    before :all do
      @user = FactoryBot.create(:user)
      image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
      @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, primary: true, event: nil,
                                           photo_v2: @admin_medium_2, creative_kind: :schemes)
      @token = @user.generate_jwt_token
    end

    before :each do
      AppVersionSupport.new('2404.15.00')
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2404.15.00"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "should return bad request when poster_creative_ids is blank" do
      post :poster_creatives_mark_as_seen_bulk, params: { poster_creative_ids: '' }
      expect(response).to have_http_status(:bad_request)
    end

    it "should return success" do
      time_now = Time.zone.now.to_i
      post :poster_creatives_mark_as_seen_bulk, params: { poster_creative_ids: { @poster_creative.id.to_s => time_now } }
      expect(response).to have_http_status(:ok)
      expect(QueuePosterCreativeViews).to have_enqueued_sidekiq_job(@user.id, { @poster_creative.id.to_s => time_now.to_s })
    end

    context "when user has not yet shared a premium poster after trial enabled" do
      before :each do
        @user = FactoryBot.create(:user)
        @premium_pitch = FactoryBot.create(:premium_pitch, user: @user, status: :trail_enabled)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, creative_kind: :wishes, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @token = @user.generate_jwt_token
        AppVersionSupport.new('2404.15.00')
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2404.15.00"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "should add to redis key & return success" do
        allow(@user).to receive(:premium_poster_usage_count_after_trial_enabled).and_return(0)
        post :poster_creatives_mark_as_seen_bulk, params: { poster_creative_ids: { @poster_creative.id.to_s => Time.zone.now.to_i } }
        expect(response).to have_http_status(:ok)
        expect(QueuePosterCreativeViews).to have_enqueued_sidekiq_job(@user.id, { @poster_creative.id.to_s => Time.zone.now.to_i.to_s })
        @premium_pitch.reload
        expect(@premium_pitch.status).to eq("trail_enabled")
        expect($redis.sismember(Constants.passed_milestone_1_creatives_criteria_redis_key, @user.id)).to be_truthy
      end
    end

    context "when user has shared a premium poster after trial enabled" do
      before :each do
        @user = FactoryBot.create(:user)
        # Metadatum.save_user_trial_data(@user, Time.zone.today, 15)
        @premium_pitch = FactoryBot.create(:premium_pitch, user: @user, status: :trail_enabled)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, creative_kind: :wishes, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @frame = FactoryBot.create(:frame)
        PosterShare.create!(user: @user, poster_creative: @poster_creative, frame: @frame, actioned_at: Time.zone.now)
        @token = @user.generate_jwt_token
        AppVersionSupport.new('2404.15.00')
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2404.15.00"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "should move to milestone_1 & return success" do
        allow_any_instance_of(User).to receive(:premium_poster_usage_count_after_trial_enabled).and_return(2)
        now = Time.zone.now.to_i
        post :poster_creatives_mark_as_seen_bulk, params: { poster_creative_ids: { @poster_creative.id.to_s => now } }
        expect(response).to have_http_status(:ok)
        expect(QueuePosterCreativeViews).to have_enqueued_sidekiq_job(@user.id, { @poster_creative.id.to_s => now.to_s })
        @premium_pitch.reload
        expect(@premium_pitch.status).to eq("milestone_1")
        expect($redis.sismember(Constants.passed_milestone_1_creatives_criteria_redis_key, @user.id)).to be_falsey
      end
    end
  end

  describe 'POST #help' do
    context 'check help API' do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2404.16.0"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it 'GoogleSheets worker should be enqueued with success response' do
        post :help
        expect(response).to have_http_status(:ok)

        expect(ExportDataToGoogleSheets).to have_enqueued_sidekiq_job(@user.id, kind_of(Array), kind_of(String))
      end
    end
  end
end
