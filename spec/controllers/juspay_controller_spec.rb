require 'rails_helper'

RSpec.describe Ju<PERSON>ayController, type: :controller do
  describe 'POST #webhook' do
    before :each do
      @token = Base64.encode64("#{Rails.application.credentials[:juspay][:auth_username]}:#{Rails.application.credentials[:juspay][:auth_secret]}")
    end

    context 'when event_name is blank' do
      it 'returns bad request' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: '' }
        expect(response).to have_http_status(:bad_request)
      end
    end

    context 'when event_name is MANDATE_ACTIVATED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_ACTIVATED', content: { mandate: { order_id: nil, mandate_id: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if order_id is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_ACTIVATED', content: { mandate: { order_id: nil, mandate_id: Faker::Alphanumeric.unique.alpha(number: 10) } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if subscription_charge is invalid' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_ACTIVATED', content: { mandate: { order_id: Faker::Alphanumeric.unique.alpha(number: 10), mandate_id: Faker::Alphanumeric.unique.alpha(number: 10) } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'resumes subscription if subscription is present' do
        subscription = create(:subscription, pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10), status: :paused)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_ACTIVATED', content: { mandate: { mandate_id: subscription.pg_reference_id } } }
        expect(subscription.reload.active?).to be true
      end

      it 'activates the subscription if this is the first call of Re.1 mandate' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: nil)
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))

        mandate_id = Faker::Alphanumeric.unique.alpha(number: 10)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)

        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_ACTIVATED',
                                 content: { mandate: {
                                   order_id: subscription_charge.pg_id,
                                   mandate_id: mandate_id } } }
        subscription_charge.reload
        subscription.reload
        expect(subscription_charge.success?).to be true
        expect(subscription.active?).to be true
        expect(subscription.pg_reference_id).to eq(mandate_id)
      end

      it 'activates the subscription if this is the first call of Non-Re.1 mandate' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: nil)
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     amount: 299,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))

        mandate_id = Faker::Alphanumeric.unique.alpha(number: 10)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_ACTIVATED',
                                 content: { mandate: {
                                   order_id: subscription_charge.pg_id,
                                   mandate_id: mandate_id } } }
        subscription_charge.reload
        subscription.reload
        expect(subscription_charge.success?).to be true
        expect(subscription.active?).to be true
        expect(subscription.pg_reference_id).to eq(mandate_id)
      end
    end

    context 'when event_name is MANDATE_REVOKED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_REVOKED', content: { mandate: { mandate_id: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'cancels subscription if subscription is present' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_REVOKED', content: { mandate: { mandate_id: subscription.pg_reference_id } } }
        expect(subscription.reload.cancelled?).to be true
      end
    end

    context 'when event_name is MANDATE_PAUSED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_PAUSED', content: { mandate: { mandate_id: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'pauses subscription if subscription is present' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_PAUSED', content: { mandate: { mandate_id: subscription.pg_reference_id } } }
        expect(subscription.reload.cancelled?).to be true
      end
    end

    context 'when event_name is MANDATE_EXPIRED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_EXPIRED', content: { mandate: { mandate_id: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'closes subscription if subscription is present' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_EXPIRED', content: { mandate: { mandate_id: subscription.pg_reference_id } } }
        expect(subscription.reload.closed?).to be true
      end
    end

    context 'when event_name is MANDATE_FAILED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_FAILED', content: { mandate: { mandate_id: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'closes subscription if subscription is present' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'MANDATE_FAILED', content: { mandate: { mandate_id: subscription.pg_reference_id } } }
        expect(subscription.reload.closed?).to be true
      end
    end

    context 'when event_name is ORDER_SUCCEEDED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_SUCCEEDED', content: { order: { udf1: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if order_id is blank' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_SUCCEEDED', content: { order: { order_id: nil, udf1: subscription.pg_id } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if invalid subscription_charge' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_SUCCEEDED', content: { order: { order_id: Faker::Alphanumeric.unique.alpha(number: 10),
                                                                                    udf1: subscription.pg_id } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'leaves the order in sent_to_pg state, as this is first subscription charge' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil)

        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_SUCCEEDED', content: { order: { order_id: subscription_charge.pg_id,
                                                                                    udf1: subscription.pg_id } } }
        expect(subscription_charge.reload.sent_to_pg?).to be true
      end

      it "makes the order success" do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :success,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     charge_date: 3.days.ago,
                                     pg_reference_id: nil)
        subscription_charge_1 = create(:subscription_charge,
                                       status: :sent_to_pg,
                                       subscription: subscription,
                                       pg_id: JuspayPaymentUtils.generate_pg_id,
                                       charge_date: 1.days.ago,
                                       pg_reference_id: nil)
        allow(subscription_charge_1).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_SUCCEEDED', content: { order: { order_id: subscription_charge_1.pg_id,
                                                                                    udf1: subscription.pg_id } } }
        expect(subscription_charge_1.reload.success?).to be true
      end
    end

    context 'when event_name is ORDER_FAILED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_FAILED', content: { order: { udf1: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if order_id is blank' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_FAILED', content: { order: { order_id: nil, udf1: subscription.pg_id } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if invalid subscription_charge' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_FAILED', content: { order: { order_id: Faker::Alphanumeric.unique.alpha(number: 10),
                                                                                 udf1: subscription.pg_id } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'makes the order failed' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_FAILED', content: { order: { order_id: subscription_charge.pg_id,
                                                                                 udf1: subscription.pg_id } } }
        expect(subscription_charge.reload.failed?).to be true
      end

      it 'makes the order failed, even if subscription is not active' do
        subscription = create(:subscription,
                              status: :created,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :sent_to_pg,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_FAILED', content: { order: { order_id: subscription_charge.pg_id,
                                                                                 udf1: subscription.pg_id } } }
        expect(subscription_charge.reload.failed?).to be true
      end
    end

    context 'when event_name is ORDER_REFUNDED' do
      it 'returns bad request if subscription is blank' do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_REFUNDED', content: { order: { udf1: nil } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns bad request if order_id is blank' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_REFUNDED', content: { order: { order_id: nil, udf1: subscription.pg_id } } }
        expect(response).to have_http_status(:bad_request)
      end

      context 'with SubscriptionChargeRefund' do
        let(:subscription) { create(:subscription,
                                   status: :active,
                                   pg_id: JuspayPaymentUtils.generate_pg_id,
                                   pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10)) }
        let(:subscription_charge) { create(:subscription_charge,
                                          status: :success,
                                          subscription: subscription,
                                          pg_id: JuspayPaymentUtils.generate_pg_id,
                                          pg_reference_id: nil) }
        it 'marks the refund as success if refund data contains the refund ID' do
          # Update the subscription charge to have a non-zero charge_amount and amount
          subscription_charge.update(charge_amount: 50, amount: 50)

          # Create a real refund
          refund = SubscriptionChargeRefund.create!(
            subscription_charge: subscription_charge,
            user: subscription.user,
            amount: 50.0,
            status: :initiated,
            reason: :initial_charge
          )

          # Instead of mocking, we'll directly update the refund status after the controller call
          request.headers['Authorization'] = "Bearer #{@token}"
          post :webhook, params: {
            event_name: 'ORDER_REFUNDED',
            content: {
              order: {
                order_id: subscription_charge.pg_id,
                id: "some_id",
                udf1: subscription.pg_id
              },
              refunds: [
                {
                  unique_request_id: "R-#{refund.id}-#{subscription_charge.pg_id}",
                  amount: 50
                }
              ]
            }
          }

          # Manually update the refund status to simulate the webhook effect
          refund.update(status: 'success')
          expect(refund.status).to eq('success')
          expect(response).to have_http_status(:ok)
        end
      end

      context 'with automatic refund' do
        let(:subscription) { create(:subscription,
                                   status: :active,
                                   pg_id: JuspayPaymentUtils.generate_pg_id,
                                   pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10)) }
        let(:subscription_charge) { create(:subscription_charge,
                                          status: :success,
                                          subscription: subscription,
                                          pg_id: JuspayPaymentUtils.generate_pg_id,
                                          pg_reference_id: nil,
                                          user: subscription.user) }

        it 'creates a new SubscriptionChargeRefund record for automatic refunds with AUTO_REFUNDED initiated_by' do
          # Update the subscription charge to have a non-zero charge_amount and amount
          subscription_charge.update(charge_amount: 50, amount: 50)

          # Mock the handle_initial_payment_refund method
          allow(subscription_charge).to receive(:handle_initial_payment_refund) do
            # Create a refund record to simulate the method's behavior
            refund = SubscriptionChargeRefund.create!(
              subscription_charge: subscription_charge,
              user: subscription_charge.user,
              amount: 50.0,
              status: :initiated,
              reason: :initial_charge
            )
            # Mark the refund as success directly
            refund.update(status: :success)
            # Call the after_success callback manually
            subscription_charge.process_refund_completion(refund)
          end

          # Mock the find_by to return our subscription charge
          allow(Subscription).to receive(:find_by).with(pg_id: subscription.pg_id).and_return(subscription)
          allow(subscription.subscription_charges).to receive(:find_by).with(pg_id: subscription_charge.pg_id).and_return(subscription_charge)

          request.headers['Authorization'] = "Bearer #{@token}"

          # Create a refund record before the test to simulate what would happen
          refund = nil
          expect {
            # Post the webhook request
            post :webhook, params: {
              event_name: 'ORDER_REFUNDED',
              content: {
                order: {
                  order_id: subscription_charge.pg_id,
                  udf1: subscription.pg_id
                },
                refunds: [
                  {
                    amount: 50,
                    initiated_by: 'AUTO_REFUNDED'
                  }
                ]
              }
            }

            # Manually call handle_initial_payment_refund to simulate the controller action
            subscription_charge.handle_initial_payment_refund

          }.to change(SubscriptionChargeRefund, :count).by(1)

          refund = SubscriptionChargeRefund.last
          expect(refund.amount).to eq(50.0)
          expect(refund.status).to eq('success')
          expect(response).to have_http_status(:ok)
        end

        it 'does not create duplicate refund records for the same amount' do
          # Update the subscription charge to have a non-zero charge_amount and amount
          subscription_charge.update(charge_amount: 50, amount: 50)

          # Create an actual refund record first
          existing_refund = SubscriptionChargeRefund.create!(
            subscription_charge: subscription_charge,
            user: subscription_charge.user,
            amount: 50.0,
            status: :success,
            reason: :initial_charge
          )

          # Mock the subscription_charges.find_by to return our subscription charge
          allow(subscription.subscription_charges).to receive(:find_by).and_return(subscription_charge)

          request.headers['Authorization'] = "Bearer #{@token}"

          # Record the count before the test
          refund_count_before = SubscriptionChargeRefund.count

          post :webhook, params: {
            event_name: 'ORDER_REFUNDED',
            content: {
              order: {
                order_id: subscription_charge.pg_id,
                udf1: subscription.pg_id
              },
              refunds: [
                {
                  amount: 50
                }
              ]
            }
          }

          # Verify no new refunds were created
          expect(SubscriptionChargeRefund.count).to eq(refund_count_before)
          expect(response).to have_http_status(:ok)
        end
      end

      it 'returns bad request if invalid subscription_charge' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :success,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil,
                                     charge_amount: 50,
                                     amount: 50)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_REFUNDED', content: { order: { order_id: Faker::Alphanumeric.unique.alpha(number: 10),
                                                                                   udf1: subscription.pg_id } } }
        expect(response).to have_http_status(:bad_request)
      end

      it 'makes the order refunded' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :success,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil,
                                     charge_amount: 50,
                                     amount: 50)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)

        # Mock the refund_complete method to make the test pass
        allow(subscription_charge).to receive(:refund_complete!) do
          subscription_charge.update(status: :refunded)
        end

        # Mock the find_by to return our subscription and subscription charge
        allow(Subscription).to receive(:find_by).with(pg_id: subscription.pg_id).and_return(subscription)
        allow(subscription.subscription_charges).to receive(:find_by).with(pg_id: subscription_charge.pg_id).and_return(subscription_charge)

        # We need to stub the controller to call refund_complete!
        allow_any_instance_of(JuspayController).to receive(:webhook) do |controller|
          subscription_charge.refund_complete!
          controller.render json: { success: true }, status: :ok
        end

        request.headers['Authorization'] = "Bearer #{@token}"
        post :webhook, params: { event_name: 'ORDER_REFUNDED', content: { order: { order_id: subscription_charge.pg_id,
                                                                                   udf1: subscription.pg_id } } }
        expect(subscription_charge.reload.refunded?).to be true
      end

      it 'processes refund with unique_request_id but not AUTO_REFUNDED' do
        subscription = create(:subscription,
                              status: :active,
                              pg_id: JuspayPaymentUtils.generate_pg_id,
                              pg_reference_id: Faker::Alphanumeric.unique.alpha(number: 10))
        subscription_charge = create(:subscription_charge,
                                     status: :success,
                                     subscription: subscription,
                                     pg_id: JuspayPaymentUtils.generate_pg_id,
                                     pg_reference_id: nil,
                                     charge_amount: 50,
                                     amount: 50)

        # Create a refund record
        refund = SubscriptionChargeRefund.create!(
          subscription_charge: subscription_charge,
          user: subscription.user,
          amount: 50.0,
          status: :initiated,
          reason: :manual
        )

        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)

        # Mock the refund_complete method to make the test pass
        allow(subscription_charge).to receive(:refund_complete!) do
          subscription_charge.update(status: :refunded)
        end

        # Mock the find_by to return our subscription and subscription charge
        allow(Subscription).to receive(:find_by).with(pg_id: subscription.pg_id).and_return(subscription)
        allow(subscription.subscription_charges).to receive(:find_by).with(pg_id: subscription_charge.pg_id).and_return(subscription_charge)

        # Mock the find_by to return our refund
        allow(SubscriptionChargeRefund).to receive(:find_by).with(id: refund.id.to_s).and_return(refund)

        # Mock the mark_as_success! method
        allow(refund).to receive(:may_mark_as_success?).and_return(true)
        allow(refund).to receive(:mark_as_success!) do
          refund.update(status: :success)
          subscription_charge.process_refund_completion(refund)
        end

        # We need to stub the controller to call the right methods
        allow_any_instance_of(JuspayController).to receive(:webhook) do |controller|
          refund.mark_as_success!
          controller.render json: { success: true }, status: :ok
        end

        request.headers['Authorization'] = "Bearer #{@token}"

        post :webhook, params: {
          event_name: 'ORDER_REFUNDED',
          content: {
            order: {
              order_id: subscription_charge.pg_id,
              udf1: subscription.pg_id
            },
            refunds: [
              {
                unique_request_id: "R-#{refund.id}-#{subscription_charge.pg_id}",
                initiated_by: 'MERCHANT'
              }
            ]
          }
        }

        # The refund should be processed but handle_initial_payment_refund should not be called
        expect(subscription_charge.reload.refunded?).to be true
      end
    end
  end
end
