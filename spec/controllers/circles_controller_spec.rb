require 'rails_helper'
require "sidekiq/testing"
# Sidekiq::Testing.inline!

RSpec.describe CirclesController, type: :controller do
  describe "GET #index" do
    before :each do
      @circle = FactoryBot.create(:circle)
      @user = FactoryBot.create(:user)
      AppVersionSupport.new("2808.01.01")
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2808.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "returns a success response" do
      get :index, params: { hashid: @circle.hashid }
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET #show_v1" do
    context "check is_user_joined after user joining that circle" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.2"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response which includes is user joined true" do

        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["is_user_joined"]).to eq(true)
      end
    end

    context "check is_user_joined if user didn't joined a circle" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.2"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response which includes is user joined false" do

        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["is_user_joined"]).to eq(false)
      end
    end

    context "Profile photo should be present" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, photo: FactoryBot.create(:photo))

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.2"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "check profile photo" do
        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)
        expect(body["profile_photo"]["url"]).to eq(@circle.photo.compressed_url(size: 1024))
      end
    end

    context "Info tab need to present if website url present" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, website_url: "www.google.com")

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.2"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "check circle tabs whether it has info tab" do

        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["circle_tabs"]).to include("info")
      end
    end

    context "Info tab should not present if website url is nil" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, website_url: nil)

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.2"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "check circle tabs whether it has info tab" do

        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["circle_tabs"]).not_to include(["info"])
      end
    end

    context "check preload feed items count" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)

        @post_1 = FactoryBot.create(:post, user: @user)
        @post_circle_1 = FactoryBot.create(:post_circle, post: @post_1, circle: @circle)

        @post_2 = FactoryBot.create(:post, user: @user)
        @post_circle_2 = FactoryBot.create(:post_circle, post: @post_2, circle: @circle)

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.2"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "checks preload feed items count" do

        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["preload_feed_items"].length).to eq(2)
      end
    end

    context "check circle tag config" do
      it "can_tag need to be true" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission,
                                                         permission_group_id: @permission_group.id,
                                                         permission_identifier: :add_tag)

        @circle_permission_group = FactoryBot.create(:circle_permission_group, circle_type: nil,
                                                     circle_level: nil, circle: @circle, permission_group: @permission_group)

        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)

        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.1"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["circle_tag_config"]["can_tag"]).to eq(true)
      end

      it "can_tag to be false" do
        @user = FactoryBot.create(:user, app_version: Gem::Version.new('1.17.1'))
        @circle = FactoryBot.create(:circle, circle_type: :governing_body, level: :legislative)
        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.1"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id }

        body = JSON.parse(response.body)

        expect(body["circle_tag_config"]["can_tag"]).to eq(false)
      end

    end

    context 'for feed_option being generic' do
      before :each do
        @user = FactoryBot.create(:user, app_version: Gem::Version.new('1.17.1'))
        @circle = FactoryBot.create(:circle)
        AppVersionSupport.new("1.17.2")
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.1"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it 'should return feed_option as generic' do
        get :show_v1, params: { circle_id: @circle.id, feed_option: 'generic' }
        body = JSON.parse(response.body)
        expect(body["feed_options"]).to eq([{ "display_name" => "కొత్త పోస్ట్స్", "key" => "generic" }, { "display_name" => "ట్రెండింగ్", "key" => "trending" }])
      end

    end

    # check poster hash in show_v1 page
    context "check posters hash" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @party = FactoryBot.create(:circle)
        @circle_photo = FactoryBot.create(:circle_photo, circle: @party)

        # to return framed poster requires user profile photo
        @photo = FactoryBot.create(:photo)
        @user = FactoryBot.create(:user, photo_id: @photo.id, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

        AppVersionSupport.new('2302.28.01')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2302.28.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "for political party normal type poster" do

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])
        get :show_v1, params: { circle_id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["preload_feed_items"]).not_to be_empty
        expect(body["preload_feed_items"][0]["feed_type"]).to eq("posters")
        expect(body["preload_feed_items"][0]["posters"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"].count).to eq(2)
        expect(body["preload_feed_items"][0]["posters"][0]["id"]).to eq(@poster.id)
        expect(body["preload_feed_items"][0]["posters"][0]["user"]["id"]).to eq(@user.id)

        # for poster photo one
        expect(body["preload_feed_items"][0]["posters"][0]["photo_url"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["poster_variant"]).to eq("NORMAL")
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"].count).to be(1)
        expect(body["preload_feed_items"][0]["posters"][0]["leader_photo_ring_color"]).to be(0xff000000)

        # for poster photo two
        expect(body["preload_feed_items"][0]["posters"][1]["photo_url"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][1]["poster_variant"]).to eq("NORMAL")
        expect(body["preload_feed_items"][0]["posters"][1]["leaders_photo_urls"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][1]["leaders_photo_urls"].count).to be(1)
        expect(body["preload_feed_items"][0]["posters"][1]["leader_photo_ring_color"]).to be(0xffFFFFFF)
      end

      it "for political party frame type poster" do

        # send data attribute to pass validations
        frame_poster_photo = fixture_file_upload("app/assets/images/poster_frame.png", "image/png")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_type: :frame,
                                    poster_photos: [
                                      FactoryBot.build(:poster_photo,
                                                       blob_data: frame_poster_photo,
                                                       photo: FactoryBot.build(:admin_medium, blob_data: frame_poster_photo),
                                                       photo_orientation: :portrait),
                                      FactoryBot.build(:poster_photo,
                                                       blob_data: frame_poster_photo,
                                                       photo: FactoryBot.build(:admin_medium, blob_data: frame_poster_photo),
                                                       photo_orientation: :landscape)
                                    ])

        get :show_v1, params: { circle_id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["preload_feed_items"]).not_to be_empty
        expect(body["preload_feed_items"][0]["feed_type"]).to eq("posters")
        expect(body["preload_feed_items"][0]["posters"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"].count).to eq(2)
        expect(body["preload_feed_items"][0]["posters"][0]["id"]).to eq(@poster.id)
        expect(body["preload_feed_items"][0]["posters"][0]["user"]["id"]).to eq(@user.id)

        # for poster photo one
        expect(body["preload_feed_items"][0]["posters"][0]["photo_url"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["poster_variant"]).to eq("PORTRAIT_FRAME")
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"].count).to be(1)

        # for poster photo two
        expect(body["preload_feed_items"][0]["posters"][1]["photo_url"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][1]["poster_variant"]).to eq("LANDSCAPE_FRAME")
        expect(body["preload_feed_items"][0]["posters"][1]["leaders_photo_urls"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][1]["leaders_photo_urls"].count).to be(1)
      end

      it "for political leader normal type poster" do
        @leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader, photo: FactoryBot.create(:photo),
                                    circle_photos: [FactoryBot.build(:circle_photo,
                                                                     photo: FactoryBot.create(:photo),
                                                                     photo_type: :poster,
                                                                     photo_order: 1)])
        @circles_relation = FactoryBot.create(:circles_relation, first_circle: @leader, second_circle: @party, relation: "Leader2Party")

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @leader,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark)
                                      ])
        get :show_v1, params: { circle_id: @leader.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["preload_feed_items"]).not_to be_empty
        expect(body["preload_feed_items"][0]["feed_type"]).to eq("posters")
        expect(body["preload_feed_items"][0]["posters"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["id"]).to eq(@poster.id)

        # for poster photo one
        expect(body["preload_feed_items"][0]["posters"][0]["photo_url"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"].count).to be(2)
      end

      it "for political leader get party poster" do
        @leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader, photo: FactoryBot.create(:photo),
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @circles_relation = FactoryBot.create(:circles_relation, first_circle: @leader, second_circle: @party, relation: "Leader2Party")

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark)
                                      ])
        get :show_v1, params: { circle_id: @leader.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["preload_feed_items"]).not_to be_empty
        expect(body["preload_feed_items"][0]["feed_type"]).to eq("posters")
        expect(body["preload_feed_items"][0]["posters"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["id"]).to eq(@poster.id)

        # for poster photo one
        expect(body["preload_feed_items"][0]["posters"][0]["photo_url"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"]).not_to be_empty
        expect(body["preload_feed_items"][0]["posters"][0]["leaders_photo_urls"].count).to be(2)
      end
    end

    context "check suggested_users_list flag enabled" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      end

      it "should return show_suggested_users_list to be true if supports app version and circle is users affiliated party" do
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.update(affiliated_party_circle_id: @circle.id)

        AppVersionSupport.new('2305.04.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2305.04.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)

        body = JSON.parse(response.body)
        expect(body["show_suggested_users_list"]).to be_truthy
      end

      it "should return show_suggested_users_list to be false if not supports app version" do
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.update(affiliated_party_circle_id: @circle.id)

        AppVersionSupport.new('2304.01.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2304.01.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)

        body = JSON.parse(response.body)
        expect(body["show_suggested_users_list"]).to be_falsey
      end

      it "should return show_suggested_users_list to be false if circle is not users affiliated party" do
        AppVersionSupport.new('2305.04.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2305.04.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)

        body = JSON.parse(response.body)
        expect(body["show_suggested_users_list"]).to be_falsey
      end
    end

    context "private level circle check for show_v1 api" do
      it "should return error if circle is private and user not joined the circle" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        get :show_v1, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:forbidden)
        expect(body["success"]).to eq(false)
        expect(body["message"]).to eq("Unauthorized")
      end

      it "should return circle if circle is private and user joined the circle" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        get :show_v1, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["id"]).to eq(@circle.id)
      end
    end

    context "send conversation_type" do
      before :each do
        @user = FactoryBot.create(:user)
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :channel)
        @circle2 = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle2)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2311.11.03'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "should return conversation as channel" do
        get :show_v1, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["conversation_type"]).to eq("channel")
      end

      it "should return conversation as none" do
        get :show_v1, params: { circle_id: @circle2.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["conversation_type"]).to eq("none")
      end
    end
  end

  describe "GET #show" do
    context "check posters hash for version < 1.14.5" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

        # to return framed poster requires user profile photo
        @photo = FactoryBot.create(:photo)
        @user = FactoryBot.create(:user, photo_id: @photo.id, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

        AppVersionSupport.new('1.14.4')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '1.14.4'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "for a political party poster" do
        @party = FactoryBot.create(:circle)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end

      it "for a poster with circle id as 31403" do
        @party = FactoryBot.create(:circle, id: 31403)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end

      it "for a poster with circle id as 31402" do
        @party = FactoryBot.create(:circle, id: 31402)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.hashid }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end

      it "for a poster with circle id as 31406" do
        @party = FactoryBot.create(:circle, id: 31406)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end

      it "for a poster with circle id as 31401" do
        @party = FactoryBot.create(:circle, id: 31401)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end

      it "for a poster with circle id as 31398" do
        @party = FactoryBot.create(:circle, id: 31398)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end

      it "for a poster with circle id as 31405" do
        @party = FactoryBot.create(:circle, id: 31405)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @party,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :dark),
                                       FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo),
                                                        leader_photo_ring_color: :light)
                                      ])

        gradients = @poster.get_gradients_v1(@user)

        get :show, params: { id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)

        # gradients for neutral user
        expect(body["poster"]["gradients"]["footer_color"]).to eq(gradients[:footer_color])
        expect(body["poster"]["gradients"]["footer_text_color"]).to eq(gradients[:footer_text_color])
        expect(body["poster"]["gradients"]["badge_text_color"]).to eq(gradients[:badge_text_color])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_x"]).to eq(gradients[:gradient_direction][:begin_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["begin_y"]).to eq(gradients[:gradient_direction][:begin_y])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_x"]).to eq(gradients[:gradient_direction][:end_x])
        expect(body["poster"]["gradients"]["gradient_direction"]["end_y"]).to eq(gradients[:gradient_direction][:end_y])
        expect(body["poster"]["gradients"]["upload_tool_tip_color"]).to eq(gradients[:upload_tool_tip_color])
        expect(body["poster"]["gradients"]["upload_tool_tip_text_color"]).to eq(gradients[:upload_tool_tip_text_color])
        expect(body["poster"]["gradients"]["background_gradients"]["colors"]).to eq(gradients[:background_gradients][:colors])
        expect(body["poster"]["gradients"]["background_gradients"]["stops"]).to eq(gradients[:background_gradients][:stops])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["colors"]).to eq([0x00ffffff, 0x00ffffff])
        expect(body["poster"]["gradients"]["badge_banner_gradients"]["stops"]).to eq([0, 1])
      end
    end
    context "private level circle" do
      it "should return error if circle is private and user not joined the circle" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        get :show, params: { id: @circle.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:forbidden)
        expect(body["success"]).to eq(false)
        expect(body["message"]).to eq("Unauthorized")
      end

      it "should return circle if circle is private and user joined the circle" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        get :show, params: { id: @circle.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["id"]).to eq(@circle.id)
      end
    end
  end

  describe "GET #get_feed_v1" do
    context "check feed items count" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)

        @post_1 = FactoryBot.create(:post, user: @user)
        @post_circle_1 = FactoryBot.create(:post_circle, post: @post_1, circle: @circle)

        @post_2 = FactoryBot.create(:post, user: @user)
        @post_circle_2 = FactoryBot.create(:post_circle, post: @post_2, circle: @circle)

        @post_3 = FactoryBot.create(:post, user: @user)
        @post_circle_3 = FactoryBot.create(:post_circle, post: @post_3, circle: @circle)

        @post_4 = FactoryBot.create(:post, user: @user)
        @post_circle_4 = FactoryBot.create(:post_circle, post: @post_4, circle: @circle)

        @post_5 = FactoryBot.create(:post, user: @user)
        @post_circle_5 = FactoryBot.create(:post_circle, post: @post_5, circle: @circle)

        @post_6 = FactoryBot.create(:post, user: @user)
        @post_circle_6 = FactoryBot.create(:post_circle, post: @post_6, circle: @circle)

        @post_7 = FactoryBot.create(:post, user: @user)
        @post_circle_7 = FactoryBot.create(:post_circle, post: @post_7, circle: @circle)

        @post_8 = FactoryBot.create(:post, user: @user)
        @post_circle_8 = FactoryBot.create(:post_circle, post: @post_8, circle: @circle)

        @post_9 = FactoryBot.create(:post, user: @user)
        @post_circle_9 = FactoryBot.create(:post_circle, post: @post_9, circle: @circle)

        @post_10 = FactoryBot.create(:post, user: @user)
        @post_circle_10 = FactoryBot.create(:post_circle, post: @post_10, circle: @circle)

        AppVersionSupport.new('1.17.2')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '1.17.2'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "checks the feed count" do

        get :get_feed_v1, params: { circle_id: @circle.id, offset: 0, count: 10 }

        body = JSON.parse(response.body)

        expect(body["feed_items"].length).to eq(10)
      end
    end
    context "check feed items for non political circles" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])

        @post1 = FactoryBot.create(:post, user: @user)
        @post_circle_1 = FactoryBot.create(:post_circle, post: @post1, circle: @circle)

        @post2 = FactoryBot.create(:post, user: @user)
        @post_circle_2 = FactoryBot.create(:post_circle, post: @post2, circle: @circle)

        @post3 = FactoryBot.create(:post, user: @user)
        @post_circle_3 = FactoryBot.create(:post_circle, post: @post3, circle: @circle)

        AppVersionSupport.new('1.17.2')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '1.17.2'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "checks the feed count" do
        allow(ES_CLIENT).to receive(:search).and_return(
          { "hits" =>
              { "hits" =>
                  [
                    {
                      "fields" =>
                        { "id" => [@post2.id.to_s], "created_at" => [(@post2.created_at).to_s], "user_id" => [@post2.user_id.to_s] }
                    },
                    {
                      "fields" =>
                        { "id" => [@post1.id.to_s], "created_at" => [(@post1.created_at).to_s], "user_id" => [@post1.user_id.to_s] }
                    }
                  ]
              }
          }
        )
        get :get_feed_v1, params: { circle_id: @circle.id, offset: 0, count: 10, loaded_feed_item_ids: [@post3.id] }

        body = JSON.parse(response.body)
        expect(body["feed_items"].length).to eq(2)
      end
    end
  end

  # about page test case
  describe "GET #show_v1" do
    context "about page" do
      it "if version < 1.17.2 then return only two tabs" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.1')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.1"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id, offset: 0, count: 10 }

        body = JSON.parse(response.body)

        expect(body["circle_tabs"].length).to eq(2)
      end
      it "if version > 1.17.2 and not a political_leader circle then return only two tabs" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id, offset: 0, count: 10 }

        body = JSON.parse(response.body)

        expect(body["circle_tabs"].length).to eq(2)
      end
      it "if version > 1.17.2 and owner for circle was not assigned return only two tabs" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: @permission_group.id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id, offset: 0, count: 10 }

        body = JSON.parse(response.body)

        expect(body["circle_tabs"].length).to eq(2)
      end
      it "if version > 1.17.2 and owner for circle was assigned return only three tabs" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        get :show_v1, params: { circle_id: @circle.id, offset: 0, count: 10 }

        body = JSON.parse(response.body)

        expect(body["circle_tabs"].length).to eq(3)
      end
    end
  end

  describe "GET #get_info" do
    context "about page" do
      before :each do
        user_dob = Date.parse("2000-02-20")
        @circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @circle,
                                              second_circle: @party_circle,
                                              active: true,
                                              relation: 'Leader2Party')

        @purview_circle_parent = FactoryBot.create(:circle,
                                                   name: Faker::Name.unique.name,
                                                   name_en: Faker::Name.unique.name,
                                                   active: true,
                                                   members_count: 100,
                                                   circle_type: :location,
                                                   level: :mp_constituency)

        @purview_circle = FactoryBot.create(:circle,
                                            name: Faker::Name.unique.name,
                                            name_en: Faker::Name.unique.name,
                                            active: true,
                                            members_count: 100,
                                            circle_type: :location,
                                            level: :mla_constituency,
                                            parent_circle_id: @purview_circle_parent.id)

        @user = FactoryBot.create(:user,
                                  dob: user_dob,
                                  birth_place_id: @circle.id,
                                  education: Faker::Name.unique.name,
                                  office_address: Faker::Address.street_address,
                                  contact_email: Faker::Internet.email)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mla_constituency,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @party_circle.id, purview_circle_id: @purview_circle.id,
                                       start_date: Date.parse("2024-01-01"),
                                       end_date: Date.parse("2029-01-01"))

        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user,
                                                          circle: @circle,
                                                          permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "section not being null" do
        get :get_info, params: { circle_id: @circle.hashid }
        body = JSON.parse(response.body)
        expect(body["about_leader"].present?).to eq(true)
      end
      it "user being user" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["about_leader"]["user"]["id"]).to eq(@user.id)
      end
      it "party being leaders related political party" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["about_leader"]["party_circle"]["id"]).to eq(@party_circle.id)
      end
      it "party being leaders related political party" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["about_leader"]["party_circle"]["id"]).to eq(@party_circle.id)
      end
      it "sections details size being 6" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["about_leader"]["details"].count).to eq(6)
      end
      it "leader age matches with dob" do
        user_dob = Date.parse("2000-02-20").strftime("%d %B %Y")
        age = (Date.current - Date.parse("#{user_dob}")).to_i / 365
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["about_leader"]["details"][1]["content"]).to eq("#{user_dob}" + "\n" + "(age #{age} years)")
      end
    end

    context "positions section" do
      before :each do
        user_dob = Date.parse("2000-02-20")
        @circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @circle,
                                              second_circle: @party_circle,
                                              active: true,
                                              relation: 'Leader2Party')

        @purview_circle_parent = FactoryBot.create(:circle,
                                                   name: Faker::Name.unique.name,
                                                   name_en: Faker::Name.unique.name,
                                                   active: true,
                                                   members_count: 100,
                                                   circle_type: :location,
                                                   level: :mp_constituency)

        @purview_circle = FactoryBot.create(:circle,
                                            name: Faker::Name.unique.name,
                                            name_en: Faker::Name.unique.name,
                                            active: true,
                                            members_count: 100,
                                            circle_type: :location,
                                            level: :mla_constituency,
                                            parent_circle_id: @purview_circle_parent.id)

        @user = FactoryBot.create(:user,
                                  dob: user_dob,
                                  birth_place_id: @circle.id,
                                  education: Faker::Name.unique.name,
                                  office_address: Faker::Address.street_address,
                                  contact_email: Faker::Internet.email)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_secondary_circle: true,
                                  has_purview: true,
                                  purview_level: :mla_constituency,
                                  active: true,
                                  badge_icon_ribbon: true)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @party_circle.id, start_date: Date.parse("2010-01-01"),
                                       end_date: Date.parse("2012-01-01"), show_on_about_page: false,
                                       purview_circle_id: @purview_circle.id)
        @user_role2 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2012-01-02"),
                                        end_date: Date.parse("2014-01-01"), show_on_about_page: false,
                                        purview_circle_id: @purview_circle.id)
        @user_role3 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2014-01-02"),
                                        end_date: Date.parse("2016-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role4 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2016-01-02"),
                                        end_date: Date.parse("2018-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role5 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2018-01-02"),
                                        end_date: Date.parse("2020-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role6 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2020-01-02"),
                                        end_date: Date.parse("2022-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role7 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2022-01-02"),
                                        end_date: Date.parse("2024-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)

        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle,
                                                          permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "section not being null" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["positions_section"].present?).to eq(true)
      end
      it "test the role data in response hash" do
        travel_to("2024-01-01") do
          get :get_info, params: { circle_id: @circle.id }
          body = JSON.parse(response.body)
          expect(body["positions_section"]["positions"][0]["title"]).to eq(@user_role7.role.name + " " +
                                                                             @user_role7.purview_circle.name)
          expect(body["positions_section"]["positions"][0]["sub_title"]).to eq(@party_circle.name)
          expect(body["positions_section"]["positions"][0]["start_date"]).to eq(@user_role7.start_date.strftime("%Y"))
          expect(body["positions_section"]["positions"][0]["end_date"]).to eq("ప్రస్తుతం")
        end
      end
      it "check the show_view_all_button" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["positions_section"]["show_view_all_button"]).to eq(true)
      end
      it "no of max psotions here is five" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["positions_section"]["positions"].count).to eq(5)
      end
    end

    context "projects section" do
      before :each do
        @circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @circle,
                                              second_circle: @party_circle,
                                              active: true,
                                              relation: 'Leader2Party')

        @purview_circle_parent = FactoryBot.create(:circle,
                                                   name: Faker::Name.unique.name,
                                                   name_en: Faker::Name.unique.name,
                                                   active: true,
                                                   members_count: 100,
                                                   circle_type: :location,
                                                   level: :mp_constituency)

        @purview_circle = FactoryBot.create(:circle,
                                            name: Faker::Name.unique.name,
                                            name_en: Faker::Name.unique.name,
                                            active: true,
                                            members_count: 100,
                                            circle_type: :location,
                                            level: :mla_constituency,
                                            parent_circle_id: @purview_circle_parent.id)

        @user = FactoryBot.create(:user)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_secondary_circle: true,
                                  has_purview: true,
                                  purview_level: :mla_constituency,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @party_circle.id, start_date: Date.parse("2010-01-01"),
                                       end_date: Date.parse("2012-01-01"), show_on_about_page: false,
                                       purview_circle_id: @purview_circle.id)
        @user_role2 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2018-01-02"),
                                        end_date: Date.parse("2020-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role3 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2020-01-02"),
                                        end_date: Date.parse("2022-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role4 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2022-01-02"),
                                        end_date: Date.parse("2024-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role5 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2024-01-02"),
                                        end_date: Date.parse("2026-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)

        # added sleep to ignore photo blob key duplicate
        sleep(1.second)
        @leader_project = FactoryBot.build(:leader_project, user: @user, user_role: @user_role, project_date: Date.parse("2010-01-02"))
        @leader_project.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        @leader_project.save!

        sleep(1.second)
        @leader_project2 = FactoryBot.build(:leader_project, user: @user, user_role: @user_role2, project_date: Date.parse("2018-01-03"))
        @leader_project2.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        @leader_project2.save!

        sleep(1.second)
        @leader_project3 = FactoryBot.build(:leader_project, user: @user, user_role: @user_role3, project_date: Date.parse("2020-01-03"))
        @leader_project3.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        @leader_project3.save!

        sleep(1.second)
        @leader_project4 = FactoryBot.build(:leader_project, user: @user, user_role: @user_role4, project_date: Date.parse("2022-01-03"))
        @leader_project4.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        @leader_project4.save!

        sleep(1.second)
        @leader_project5 = FactoryBot.build(:leader_project, user: @user, user_role: @user_role5, project_date: Date.parse("2024-01-03"))
        @leader_project5.photo.attach(io: File.open(Rack::Test::UploadedFile.new("#{Rails.root}/spec/fixtures/files/praja.buzz.png")), filename: "praja.buzz.png", key: "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_praja.buzz.png")
        @leader_project5.save!

        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "section not being null" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["projects_section"].present?).to eq(true)
      end

      it "no of projects being 5" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["projects_section"]["projects"].count).to eq(5)
      end

      it "no of projects being 5" do
        get :get_info, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["projects_section"]["projects"][0]["user"]["badge"]["id"]).to eq(@user_role.id)
      end

    end
  end

  describe "GET #get_all_leader_circle_owner_positions" do
    context "view all owner positions" do
      before :each do
        user_dob = Date.parse("2000-02-20")
        @circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @party_circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        @circles_relation = FactoryBot.create(:circles_relation,
                                              first_circle: @circle,
                                              second_circle: @party_circle,
                                              active: true,
                                              relation: 'Leader2Party')

        @purview_circle_parent = FactoryBot.create(:circle,
                                                   name: Faker::Name.unique.name,
                                                   name_en: Faker::Name.unique.name,
                                                   active: true,
                                                   members_count: 100,
                                                   circle_type: :location,
                                                   level: :mp_constituency)

        @purview_circle = FactoryBot.create(:circle,
                                            name: Faker::Name.unique.name,
                                            name_en: Faker::Name.unique.name,
                                            active: true,
                                            members_count: 100,
                                            circle_type: :location,
                                            level: :mla_constituency,
                                            parent_circle_id: @purview_circle_parent.id)

        @user = FactoryBot.create(:user,
                                  dob: user_dob,
                                  birth_place_id: @circle.id,
                                  education: Faker::Name.unique.name,
                                  office_address: Faker::Address.street_address,
                                  contact_email: Faker::Internet.email)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mla_constituency,
                                  active: true,
                                  badge_icon_ribbon: true)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @party_circle.id, start_date: Date.parse("2010-01-01"),
                                       end_date: Date.parse("2012-01-01"), show_on_about_page: false,
                                       purview_circle_id: @purview_circle.id)
        @user_role2 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2012-01-02"),
                                        end_date: Date.parse("2014-01-01"), show_on_about_page: false,
                                        purview_circle_id: @purview_circle.id)
        @user_role3 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2014-01-02"),
                                        end_date: Date.parse("2016-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role4 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2016-01-02"),
                                        end_date: Date.parse("2018-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role5 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2018-01-02"),
                                        end_date: Date.parse("2020-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role6 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2020-01-02"),
                                        end_date: Date.parse("2022-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)
        @user_role7 = FactoryBot.create(:user_role, user: @user, role: @role,
                                        parent_circle_id: @party_circle.id, start_date: Date.parse("2022-01-02"),
                                        end_date: Date.parse("2024-01-01"), show_on_about_page: true,
                                        purview_circle_id: @purview_circle.id)

        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "response json size count" do
        get :get_all_leader_circle_owner_positions, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body.count).to eq(3)
      end
      it "all owner roles count" do
        get :get_all_leader_circle_owner_positions, params: { circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(body["positions"].count).to eq(7)
      end
    end
    context "for non political leader circles" do
      it "returns error to show positions" do
        circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        get :get_all_leader_circle_owner_positions, params: { circle_id: circle.id }
        expect(response).to have_http_status(:bad_request)
        body = JSON.parse(response.body)
        expect(body["message"]).to eq('Positions shown only on political leader circle')
      end
    end
  end

  # test cases for circle trending_feed
  describe "POST #trending_feed" do
    context "trending feed" do
      before :each do
        @circle = FactoryBot.create(:circle, name: Faker::Name.unique.name, name_en: Faker::Name.unique.name,
                                    active: true, circle_type: :interest, level: :political_party)
        @user = FactoryBot.create(:user)
        @post = FactoryBot.create(:post, user: @user)
        @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post) }

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns trending feed" do

        post :trending_feed, params: { circle_id: @circle.id, offset: 0, count: 10 }

        expect(response).to have_http_status(:success)
        body = JSON.parse(response.body)
        expect(body["feed_items"]).to be_present
      end

      it "returns trending feed by avoiding already loaded posts" do

        loaded_feed_item_ids = [@post.id]
        post :trending_feed, params: { circle_id: @circle.id, offset: 0, count: 10, loaded_feed_item_ids: loaded_feed_item_ids }

        expect(response).to have_http_status(:success)
        body = JSON.parse(response.body)

        # here body["feed_items"] should not include @post.id because we already loaded it
        expect(body["feed_items"].map { |x| x["id"] }).not_to include(@post.id)
      end

      it "returns trending feed from my_sql if is_sql true" do
        30.times { FactoryBot.create(:post_like, post: @post) }
        post :trending_feed, params: { circle_id: @circle.id, offset: 0, count: 10, is_sql: true }

        expect(response).to have_http_status(:success)
        body = JSON.parse(response.body)
        expect(body["is_sql"]).to eq(true)
      end

      it "returns trending feed where feed_items has poster feed" do
        30.times { FactoryBot.create(:post_like, post: @post) }
        @post1 = FactoryBot.create(:post, user: @user)
        @post_circle = FactoryBot.create(:post_circle, post: @post1, circle: @circle)

        # create poster for that circle
        @poster = FactoryBot.build(:poster, circle: @circle)
        @poster_photo = FactoryBot.build(:poster_photo, poster: @poster)
        @poster.save(validate: false)
        @poster_photo.poster_id = @poster.id
        @poster_photo.save(validate: false)

        allow(ES_CLIENT).to receive(:search).and_return({ "hits" => { "hits" => [
          { "fields" => { "id" => [@post.id.to_s], "created_at" => [(@post.created_at).to_s],
                          "user_id" => [@post.user_id.to_s] } },
          { "fields" => { "id" => [@post.id.to_s], "created_at" => [(@post.created_at).to_s],
                          "user_id" => [@post.user_id.to_s] } },
          { "fields" => { "id" => [@post1.id.to_s], "created_at" => [(@post1.created_at).to_s],
                          "user_id" => [@post1.user_id.to_s] } },

        ] } })

        post :trending_feed, params: { circle_id: @circle.id, offset: 0, count: 10 }
        body = JSON.parse(response.body)

        expect(body["feed_items"].first["feed_item_id"]).to eq("poster")
      end
    end

    context "send unique user posts for each page" do
      before :each do
        @circle = FactoryBot.create(:circle, name: Faker::Name.unique.name, name_en: Faker::Name.unique.name,
                                    active: true, circle_type: :interest, level: :political_party)
        @user = FactoryBot.create(:user)
        @post = FactoryBot.create(:post, user: @user)
        @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post) }

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        @user1 = FactoryBot.create(:user)
        @post1 = FactoryBot.create(:post, user: @user1)
        @post_circle1 = FactoryBot.create(:post_circle, post: @post1, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post1) }

        @user2 = FactoryBot.create(:user)
        @post2 = FactoryBot.create(:post, user: @user2)
        @post_circle2 = FactoryBot.create(:post_circle, post: @post2, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post2) }

        @post3 = FactoryBot.create(:post, user: @user)
        @post_circle3 = FactoryBot.create(:post_circle, post: @post3, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post3) }

        @post4 = FactoryBot.create(:post, user: @user1)
        @post_circle4 = FactoryBot.create(:post_circle, post: @post4, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post4) }

        @user3 = FactoryBot.create(:user)
        @post5 = FactoryBot.create(:post, user: @user3)
        @post_circle5 = FactoryBot.create(:post_circle, post: @post5, circle: @circle)
        30.times { FactoryBot.create(:post_like, post: @post5) }
      end
      it "returns trending feed where unique user posts only" do
        allow(ES_CLIENT).to receive(:search).and_return({ "hits" => { "hits" => [
          { "fields" => { "id" => [@post.id.to_s], "created_at" => [(@post.created_at).to_s],
                          "user_id" => [@post.user_id.to_s] } },
          { "fields" => { "id" => [@post1.id.to_s], "created_at" => [(@post1.created_at).to_s],
                          "user_id" => [@post1.user_id.to_s] } },
          { "fields" => { "id" => [@post2.id.to_s], "created_at" => [(@post2.created_at).to_s],
                          "user_id" => [@post2.user_id.to_s] } },
          { "fields" => { "id" => [@post5.id.to_s], "created_at" => [(@post5.created_at).to_s],
                          "user_id" => [@post5.user_id.to_s] } }

        ] } })
        post :trending_feed, params: { circle_id: @circle.id, offset: 0, count: 4 }

        expect(response).to have_http_status(:success)
        body = JSON.parse(response.body)
        expect(body["feed_items"].count).to eq(4)
        expect(body["feed_items"].map { |x| x["id"] }).not_to include(@post3.id)
        expect(body["feed_items"].map { |x| x["id"] }).not_to include(@post4.id)
      end
    end
  end

  describe "POST #trending_feed suggested users" do
    it "returns trending feed where feed_items has suggested_users_list feed" do
      @circle = FactoryBot.create(:circle, name: Faker::Name.unique.name, name_en: Faker::Name.unique.name,
                                  active: true, circle_type: :interest, level: :political_party)
      # create user with all details
      @state_level_circle = FactoryBot.create(:circle,
                                              name: Faker::Name.unique.name,
                                              name_en: Faker::Name.unique.name,
                                              active: true,
                                              members_count: 0,
                                              circle_type: :location,
                                              level: :state)
      @circle1 = FactoryBot.create(:circle,
                                   name: 'District',
                                   name_en: 'District EN',
                                   active: true,
                                   members_count: 100,
                                   circle_type: :location,
                                   level: :district,
                                   parent_circle: @state_level_circle)
      @circle2 = FactoryBot.create(:circle,
                                   name: 'Mandal',
                                   name_en: 'Mandal EN',
                                   active: true,
                                   members_count: 100,
                                   circle_type: :location,
                                   level: :mandal,
                                   parent_circle: @circle1)
      @circle3 = FactoryBot.create(:circle,
                                   name: 'Village',
                                   name_en: 'Village En',
                                   active: true,
                                   members_count: 100,
                                   circle_type: :location,
                                   level: :village,
                                   parent_circle: @circle2)

      @mp_constituency_circle = FactoryBot.create(:circle,
                                                  name: Faker::Name.unique.name,
                                                  name_en: Faker::Name.unique.name,
                                                  active: true,
                                                  circle_type: :location,
                                                  level: :mp_constituency)
      @mla_constituency_circle = FactoryBot.create(:circle,
                                                   name: Faker::Name.unique.name,
                                                   name_en: Faker::Name.unique.name,
                                                   active: true,
                                                   circle_type: :location,
                                                   level: :mla_constituency,
                                                   parent_circle: @mp_constituency_circle)

      @role = FactoryBot.create(:role,
                                name: Faker::Name.unique.name,
                                has_badge: true,
                                badge_ring: true,
                                badge_color: :GOLD,
                                quota_type: :no_limit,
                                quota_value: nil,
                                grade_level: :grade_2,
                                parent_circle_level: :political_party,
                                has_purview: false,
                                active: true)
      @user = FactoryBot.create(:user, village_id: @circle3.id, mandal_id: @circle2.id, district_id: @circle1.id,
                                state_id: @state_level_circle.id, mp_constituency_id: @mp_constituency_circle.id,
                                mla_constituency_id: @mla_constituency_circle.id)
      @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle3)
      @post = FactoryBot.create(:post, user: @user)
      @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)
      30.times { FactoryBot.create(:post_like, post: @post) }
      @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)

      5.times do
        user_1 = FactoryBot.create(:user, village_id: @circle3.id, mandal_id: @circle2.id, district_id: @circle1.id,
                                   state_id: @state_level_circle.id, mp_constituency_id: @mp_constituency_circle.id,
                                   mla_constituency_id: @mla_constituency_circle.id)
        FactoryBot.create(:user_circle, user: user_1, circle: @circle3)

        FactoryBot.create(:user_role, user: user_1, role: @role, parent_circle_id: @circle.id,
                          )

      end

      # create suggested users list for that circle

      AppVersionSupport.new('1.17.3')
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "1.17.3"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      post :trending_feed, params: { circle_id: @circle.id, offset: 20, count: 10 }
      body = JSON.parse(response.body)
      expect(body["feed_items"].first["feed_type"]).to eq("suggested_list")
    end
  end

  describe "GET #get_suggested_users_lists" do
    before :each do
      @circle = FactoryBot.create(:circle)
      @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
      @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle: @mp_constituency)

      @user = FactoryBot.create(:user, mla_constituency_id: @mla_constituency.id)
      @circle = FactoryBot.create(:circle)
      @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)

      @role = FactoryBot.create(:role, grade_level: :grade_3, quota_type: :no_limit)

      @suggesting_user = FactoryBot.create(:user, mla_constituency_id: @mla_constituency.id)
      @su_user_role = FactoryBot.create(:user_role, user: @suggesting_user, role: @role, parent_circle_id: @circle.id)

      @suggesting_user1 = FactoryBot.create(:user, mla_constituency_id: @mla_constituency.id)
      @su_user_role1 = FactoryBot.create(:user_role, user: @suggesting_user1, role: @role, parent_circle_id: @circle.id)

      @suggesting_user2 = FactoryBot.create(:user, mla_constituency_id: @mla_constituency.id)
      @su_user_role2 = FactoryBot.create(:user_role, user: @suggesting_user2, role: @role, parent_circle_id: @circle.id)

      @suggesting_user3 = FactoryBot.create(:user, mla_constituency_id: @mla_constituency.id)
      @su_user_role3 = FactoryBot.create(:user_role, user: @suggesting_user3, role: @role, parent_circle_id: @circle.id)

      @suggesting_user4 = FactoryBot.create(:user, mla_constituency_id: @mla_constituency.id)
      @su_user_role4 = FactoryBot.create(:user_role, user: @suggesting_user4, role: @role, parent_circle_id: @circle.id)
    end

    it "returns suggested users list" do
      AppVersionSupport.new('2305.04.01')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_suggested_users_lists, params: { circle_id: @circle.id }
      expect(response).to have_http_status(:success)

      response_body = JSON.parse(response.body)
      expect(response_body['suggested_users_lists']).to be_present
    end
  end

  describe "GET #get_political_parties" do
    context "get political parties" do
      it "returns political parties" do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2305.04.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2305.04.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @political_circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        get :get_political_parties
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
    end
  end

  describe "POST #create_private circle" do
    context "create private circle" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2305.04.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2305.04.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns private circle" do
        post :create_private, params: { name: "test", name_en: "test" }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["level"]).to eq("private")
      end

      it "returns error if name is not present for circle" do
        post :create_private, params: { name_en: "test" }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq('name is required')
      end

      it "returns error if name_en is not present for circle" do
        post :create_private, params: { name: "test" }
        expect(response).to have_http_status(:unprocessable_entity)
        response_body = JSON.parse(response.body)
        expect(response_body["name_en"]).to eq(["can't be blank"])
      end
    end
  end

  describe "#get_share_text in circles_controller" do
    context "get share text" do
      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns unauthorised error if user tries to share private circle who is not head user" do
        @head_user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, level: :private, circle_type: :my_circle,
                                    head_user_id: @head_user.id)

        get :get_share_text, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:forbidden)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to eq(false)
      end

      it "returns share text for interest circle where app version > '1.11.3'" do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        circle_url = "https://www.praja.net"
        allow(Singular).to receive(:shorten_link).and_return(circle_url)

        get :get_share_text, params: { circle_id: @circle.id }
        text =I18n.t('circle_share_text.interest_circle_type.share_text', circle_name: @circle.name, members_count: @circle.get_members_count.to_s.gsub(/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/, "\\1,"))
        msg = I18n.t('circle_share_text.latest_version.message', text: text, circle_url: circle_url)
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["text"]).to eq(msg)
      end

      it "returns share text for location circle where app version > '1.11.3'" do
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        circle_url = "https://www.praja.net"
        allow(Singular).to receive(:shorten_link).and_return(circle_url)

        get :get_share_text, params: { circle_id: @circle.id }
        text = I18n.t('circle_share_text.location_circle_type.share_text', circle_name: @circle.name)
        msg = I18n.t('circle_share_text.latest_version.message', text: text, circle_url: circle_url)
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["text"]).to eq(msg)
      end
      it "returns share text for governing body circle where app version > '1.11.3'" do
        @circle = FactoryBot.create(:circle, circle_type: :governing_body, level: :executive)
        circle_url = "https://www.praja.net"
        allow(Singular).to receive(:shorten_link).and_return(circle_url)
        text = I18n.t('circle_share_text.default_share_text', circle_name: @circle.name, circle_name_en: @circle.name_en)
        msg = I18n.t('circle_share_text.latest_version.message', text: text, circle_url: circle_url)
        get :get_share_text, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["text"]).to eq(msg)
      end
      it "returns share text for interest circle where app version < '1.11.3'" do
        AppVersionSupport.new('1.11.2')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '1.11.2'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        circle_url = 'https://m.praja.buzz/circles/' + @circle.hashid

        get :get_share_text, params: { circle_id: @circle.id }
        text = I18n.t('circle_share_text.interest_circle_type.share_text', circle_name: @circle.name, members_count: @circle.get_members_count.to_s.gsub(/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/, "\\1,"))
        dynamic_link = 'test'

        msg = I18n.t('circle_share_text.older_version.message', text: text, circle_url: circle_url, older_version_message: I18n.t('circle_share_text.app_download_text') ,link: dynamic_link)
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["text"]).to eq(msg)
      end
    end
  end

  describe "GET #get_users" do
    context "get users" do
      before :each do
        # creating village circle
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns members of political circle" do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user1 = FactoryBot.create(:user, village_id: @village.id)
        @user2 = FactoryBot.create(:user, village_id: @village.id)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [@user1.id] } },
                { "fields" => { "id" => [@user2.id] } }
              ]
            }
          }
        )

        get :get_users, params: { circle_id: @circle.id, offset: 0, count: 10 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
      it "returns members of location circle" do
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @user1 = FactoryBot.create(:user, village_id: @village.id)
        @user2 = FactoryBot.create(:user, village_id: @village.id)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [@user1.id] } },
                { "fields" => { "id" => [@user2.id] } }
              ]
            }
          }
        )

        get :get_users, params: { circle_id: @circle.id, offset: 0, count: 10 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
      it "returns members of a cirlce whose app_version <= '1.12.0'" do
        AppVersionSupport.new('1.12.0')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '1.12.0'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user1 = FactoryBot.create(:user, village_id: @village.id)
        @user2 = FactoryBot.create(:user, village_id: @village.id)
        FactoryBot.create(:user_circle, user: @user1, circle: @circle)
        FactoryBot.create(:user_circle, user: @user2, circle: @circle)
        FactoryBot.create(:user_follower, user: @user1, follower: @user)
        FactoryBot.create(:user_follower, user: @user2, follower: @user)
        get :get_users, params: { circle_id: @circle.id, offset: 0, count: 10 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
      it "return error if user don't have village " do
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @user1 = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [@user1.id] } },
                { "fields" => { "id" => [@user2.id] } }
              ]
            }
          }
        )

        get :get_users, params: { circle_id: @circle.id, offset: 0, count: 10 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to eq(false)
      end
    end
  end

  describe 'GET #get_posts' do
    context 'get posts of a circle' do
      it 'returns an array of posts' do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @circle = FactoryBot.create(:circle)
        @post = FactoryBot.create(:post, user: @user)
        FactoryBot.create(:post_circle, post: @post, circle: @circle)
        get :get_posts, params: { circle_id: @circle.id, offset: 0, count: 10 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(1)
        expect(response_body.first['id']).to eq(@post.id)
      end
    end
  end

  describe "GET #get_feed" do
    context "get feed of a circle" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.update_affiliated_party_circle_id
        @post = FactoryBot.create(:post, user: @user)
        @post1 = FactoryBot.create(:post, user: @user)
        FactoryBot.create(:post_circle, post: @post, circle: @circle)
        FactoryBot.create(:post_circle, post: @post1, circle: @circle)
      end
      it "returns an array of feed items" do
        get :get_feed, params: { circle_id: @circle.id, offset: 0, count: 10 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
        expect(response_body.map { |x| x["id"] }).to match_array([@post.id, @post1.id])
      end

      it "returns suggested users list for badge user in page 2 in version greater than 1.16.1" do
        @user1 = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user)
        @user3 = FactoryBot.create(:user)
        @user4 = FactoryBot.create(:user)
        @user5 = FactoryBot.create(:user)
        users_list = [
          { id: @user1.id, name: @user1.name, photo: @user1.photo, hashid: @user1.hashid, badge: @user1.get_badge_role&.get_json, avatar_color: @user1.avatar_color },
          { id: @user2.id, name: @user2.name, photo: @user2.photo, hashid: @user2.hashid, badge: @user2.get_badge_role&.get_json, avatar_color: @user2.avatar_color },
          { id: @user3.id, name: @user3.name, photo: @user3.photo, hashid: @user3.hashid, badge: @user3.get_badge_role&.get_json, avatar_color: @user3.avatar_color },
          { id: @user4.id, name: @user4.name, photo: @user4.photo, hashid: @user4.hashid, badge: @user4.get_badge_role&.get_json, avatar_color: @user4.avatar_color },
          { id: @user5.id, name: @user5.name, photo: @user5.photo, hashid: @user5.hashid, badge: @user5.get_badge_role&.get_json, avatar_color: @user5.avatar_color }
        ]

        suggested_users_list = {
          header: 'మీ నాయకులుని ఫాలో అవ్వండి',
          users: users_list,
          custom_properties: {}
        }
        allow_any_instance_of(User).to receive(:get_suggested_users_feed_v1).with([], true).and_return(suggested_users_list)

        get :get_feed, params: { circle_id: @circle.id, offset: 10, count: 10 }

        expect(response).to have_http_status(:ok)

        body = JSON.parse(response.body)
        expect(body[0]["header"]).to eq("మీ నాయకులుని ఫాలో అవ్వండి")
        expect(body[0]["users"].size).to eq(5)
        expect(body[0]["users"][0]["id"]).to eq(@user1.id)
      end
    end
  end

  describe "PUT #add_user" do
    context "add user to a circle" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "return error when user to join in location circle" do
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        put :add_user, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq(I18n.t('feature_unavailable'))
      end
      it "return user joined message if user tries to join political circle" do
        @user_group = FactoryBot.create(:user_group, user: @user)
        @user_group_member = FactoryBot.create(:user_group_member, user_group: @user_group, user: @user)
        @user_group2 = FactoryBot.create(:user_group, user: @user)
        @user_group_member2 = FactoryBot.create(:user_group_member, user_group: @user_group2, user: @user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        put :add_user, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq(I18n.t('circle_user_join.joined'))
      end

      it "return user joined message if user tries to join political circle after leaving channel of that party" do
        @user_group = FactoryBot.create(:user_group, user: @user)
        @user_group_member = FactoryBot.create(:user_group_member, user_group: @user_group, user: @user)
        @user_group2 = FactoryBot.create(:user_group, user: @user)
        @user_group_member2 = FactoryBot.create(:user_group_member, user_group: @user_group2, user: @user)
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))

        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party, conversation_type: :channel)
        FactoryBot.create(:excluded_user_circle, user: @user, circle: @circle, excluded_by_user: @user, conversation_type: @circle.conversation_type)
        put :add_user, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
        exclude_user_circle = ExcludedUserCircle.find_by(user_id: @user.id, circle_id: @circle.id)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq(I18n.t('circle_user_join.joined'))
        expect(exclude_user_circle).to be_nil
      end

    end
  end

  describe "PUT #remove_user" do
    context "unjoin user from a circle" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "remove user from a circle" do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        put :remove_user, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq(I18n.t('circle_user_unjoin.unjoined'))
      end
    end
  end

  describe "GET #all_districts" do
    context "get all districts" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns all districts" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @district1 = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        get :all_districts
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
    end
  end

  describe "GET #district_mandals" do
    context "get district mandals" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns district mandals" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @mandal1 = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        get :district_mandals, params: { district_id: @district.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
    end
  end

  describe "GET #mandal_villages" do
    context "get mandal villages" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns mandal villages" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @village1 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        get :mandal_villages, params: { mandal_id: @mandal.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body.count).to eq(2)
      end
    end
  end

  describe "PUT #suggested_seen" do
    context "circle suggested seen" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.10.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "mark suggested circle as seen" do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        allow_any_instance_of(Circle).to receive(:mark_suggested_as_seen).with(@user.id).and_return(nil)
        put :suggested_seen, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'POST #upload-creative' do

    before :each do
      @user = FactoryBot.create(:user)
      @circle = FactoryBot.create(:circle)
      AppVersionSupport.new('2308.10.01')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2308.10.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    context 'check permissions' do
      it 'should return unauthorized if user doesn\'t have upload permissions' do
        allow(@user).to receive(:get_all_circle_permissions).with(@circle.id).and_return([])
        post :upload_creative, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:forbidden)
        body = JSON.parse(response.body)
        expect(body['message']).to eq(I18n.t('authorisation_error'))
      end
    end

    context 'validate request body' do
      it 'should return bad request if creative is not present' do
        allow_any_instance_of(CirclePolicy).to receive(:can_upload_creative?).and_return(true)
        post :upload_creative, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:bad_request)
        body = JSON.parse(response.body)
        expect(body['message']).to eq(I18n.t('upload_creative.no_photo_found'))
      end
    end

    context 'upload creative' do
      let(:photo) { Photo.new(url: "https://cdn.thecircleapp.in/#{Rails.env}/photos/#{@user.id}/test-md5-hash.jpg", user: @user, service: :aws) }

      it 'should return ok if creative is uploaded' do
        allow_any_instance_of(CirclePolicy).to receive(:can_upload_creative?).and_return(true)
        allow_any_instance_of(PosterCreative).to receive(:check_photos_aspect_ratio).and_return(true)
        allow_any_instance_of(PosterCreative).to receive(:presence_of_photo_v3).and_return(true)
        allow_any_instance_of(PosterCreative).to receive(:validate_whether_event_has_primary_creative).and_return(true)

        poster_creative = double('poster_creative')

        id = double('id')
        allow(poster_creative).to receive(:save).and_return(true)
        allow(poster_creative).to receive(:id).and_return(id)
        allow(poster_creative).to receive(:circle_id).and_return(@circle.id)
        allow(poster_creative).to receive(:creative_kind).and_return(:info)

        allow_any_instance_of(Circle).to receive(:upload_creative).and_return(poster_creative)

        post :upload_creative, params: { circle_id: @circle.id, photo: :image }

        expect(@response).to have_http_status(:ok)
      end
    end
  end

  describe "GET #share_channel" do
    context "get share channel" do
      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2311.22.03')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns share channel if circle has channel" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :channel)
        get :share_channel, params: { circle_id: @circle.id }
        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(response_body["share_text"]).to be_present
      end
      it "returns bad request if circle is has no channel enabled" do
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        get :share_channel, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
        expect(response_body["message"]).to eq("circle has no enabled channel")
      end
    end
  end

  describe 'GET #get_preview_html' do
    context 'get preview html' do
      it 'returns success when circle exists' do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        get :get_preview_html, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
        # TODO: check response html has appropriate meta tags
      end
    end
  end

  describe 'GET #get_channel_preview_html' do
    context 'get channel preview html' do
      it 'returns success when circle exists' do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        get :get_channel_preview_html, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:ok)
        # TODO: check response html has appropriate meta tags
      end
    end
  end

  describe 'POST #get_conversation_members' do
    context 'get conversation members' do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2401.01.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2401.01.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @circle = FactoryBot.create(:circle)
      end

      it 'returns not_found when circle not exists' do
        post :get_conversation_members, params: { circle_id: -1 }
        expect(response).to have_http_status(:not_found)
      end

      it 'get users with given user as Owner but will be type of member as he have no permission related to channel and one owner with count ' do
        user1 = FactoryBot.create(:user)
        user2 = FactoryBot.create(:user)
        FactoryBot.create(:user_circle, user: user1, circle: @circle)
        FactoryBot.create(:user_circle, user: user2, circle: @circle)

        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :channel_send_message)
        user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: user2,
                                                         circle: @circle,
                                                         permission_group_id: Constants.owner_permission_group_id)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [user2.id] } },
                { "fields" => { "id" => [user1.id] } }
              ]
            }
          }
        )
        post :get_conversation_members, params: { circle_id: @circle.id, count: 2 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['members'].count).to eq(2)
        expect(response_body['members'].first['user']['id']).to eq(user2.id)
        expect(response_body['members'].first['type']).to eq('owner')
        expect(response_body['members'].first['label']).to eq('Owner')
        expect(response_body['members'].second['type']).to eq('member')
        expect(response_body['members'].second['label']).to eq('Member')
      end

      it 'get users with user who have no permission  with count 1' do
        @user2 = FactoryBot.create(:user)
        FactoryBot.create(:user_circle, user: @user2, circle: @circle)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [@user2.id] } },
              ]
            }
          }
        )
        post :get_conversation_members, params: { circle_id: @circle.id, count: 1 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['members'].count).to eq(1)
        expect(response_body['members'].first['user']['id']).to eq(@user2.id)
        expect(response_body['members'].first['type']).to eq('member')
        expect(response_body['members'].first['label']).to eq('Member')
      end

      it 'get users with user who have no permission to send message in private group conversation ' do
        @user2 = FactoryBot.create(:user)
        private_group_circle = FactoryBot.create(:circle, conversation_type: :private_group)
        FactoryBot.create(:user_circle, user: @user2, circle: private_group_circle)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [@user2.id] } },
              ]
            }
          }
        )
        post :get_conversation_members, params: { circle_id: private_group_circle.id, count: 1 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['members'].count).to eq(1)
        expect(response_body['members'].first['user']['id']).to eq(@user2.id)
        expect(response_body['members'].first['type']).to eq('passive')
        expect(response_body['members'].first['label']).to eq('Viewer')
      end

      it 'get users with user who have no permission  with count 1 when user is excluded user' do
        FactoryBot.create(:excluded_user_circle, user: @user, circle: @circle, excluded_by_user: @user, conversation_type: @circle.conversation_type)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
              ]
            }
          }
        )
        post :get_conversation_members, params: { circle_id: @circle.id, count: 1 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['members'].count).to eq(0)
      end

      it " get list of users including default permission group for circle users as members" do
        user = FactoryBot.create(:user)
        circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: user, circle: circle)

        permission_group = FactoryBot.build(:permission_group, name: "default")
        permission_group.save(validate: false)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: permission_group.id,
                          permission_identifier: :channel_send_message)
        FactoryBot.create(:circle_permission_group, circle: circle, permission_group: permission_group)
        allow(ES_CLIENT).to receive(:search).and_return(
          {
            "hits" => {
              "hits" => [
                { "fields" => { "id" => [user.id] } }
              ]
            }
          }
        )
        post :get_conversation_members, params: { circle_id: circle.id, count: 1 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['members'].count).to eq(1)
        expect(response_body['members'].first['user']['id']).to eq(user.id)
        expect(response_body['members'].first['type']).to eq('member')
        expect(response_body['members'].first['label']).to eq('Member')
      end

    end

  end

  describe "PUT #leave_channel" do
    context "leave channel api" do

      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2402.12.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "returns 200 if circle has channel" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :channel)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        put :leave_channel, params: { circle_id: @circle.id }
        response_body = JSON.parse(response.body)
        excluded_userCircle = ExcludedUserCircle.where(user_id: @user.id, circle_id: @circle.id).first
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(excluded_userCircle).to be_present
      end

      it "returns 200 if circle has channel and user already left the channel" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :channel)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        FactoryBot.create(:excluded_user_circle, user: @user, circle: @circle, excluded_by_user: @user, conversation_type: @circle.conversation_type)
        put :leave_channel, params: { circle_id: @circle.id }
        response_body = JSON.parse(response.body)
        excluded_userCircle = ExcludedUserCircle.where(user_id: @user.id, circle_id: @circle.id).first
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(excluded_userCircle).to be_present
      end

      it "returns bad request if user not joined in circle or circle is not channel enabled" do
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        put :leave_channel, params: { circle_id: @circle.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

    end
  end

  describe "PUT #leave_private_group" do
    context "leave private group api" do

      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2402.23.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "returns 200 if non user created circle has private_group" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        FactoryBot.create(:user_circle, user: @user, circle: circle)
        put :leave_private_group, params: { circle_id: circle.id }
        response_body = JSON.parse(response.body)
        excluded_userCircle = ExcludedUserCircle.where(user_id: @user.id, circle_id: circle.id).first
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(excluded_userCircle).to be_present
      end

      it "returns 200 if user created type circle has private_group" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        FactoryBot.create(:user_circle, user: @user, circle: circle)
        put :leave_private_group, params: { circle_id: circle.id }
        response_body = JSON.parse(response.body)
        excluded_userCircle = ExcludedUserCircle.where(user_id: @user.id, circle_id: circle.id).first
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(excluded_userCircle).to be_nil

      end

      it "returns 200 if user created circle has private_group and user already left the private group" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        FactoryBot.create(:user_circle, user: @user, circle: circle)
        UserCircle.where(user_id: @user.id, circle_id: circle.id).first.destroy
        put :leave_private_group, params: { circle_id: circle.id }
        response_body = JSON.parse(response.body)
        excluded_userCircle = ExcludedUserCircle.where(user_id: @user.id, circle_id: circle.id).first
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(excluded_userCircle).to be_nil
      end

      it "returns 200 if non user created circle has private_group and user already left the private group" do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        FactoryBot.create(:user_circle, user: @user, circle: circle)
        FactoryBot.create(:excluded_user_circle, user: @user, circle: circle, excluded_by_user: @user, conversation_type: circle.conversation_type)
        put :leave_private_group, params: { circle_id: circle.id }
        response_body = JSON.parse(response.body)
        excluded_userCircle = ExcludedUserCircle.where(user_id: @user.id, circle_id: circle.id).first
        expect(response).to have_http_status(:ok)
        expect(response_body["success"]).to be_truthy
        expect(excluded_userCircle).to be_present
      end

      it "returns bad request if user not joined in circle or circle is not private group enabled" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        put :leave_private_group, params: { circle_id: circle.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

    end
  end

  describe "PUT #add_as_admin" do
    context "Add user as admin" do
      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2402.26.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @permission_group = FactoryBot.build(:permission_group, name: 'admin')
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission,
                                                         permission_group_id: @permission_group.id,
                                                         permission_identifier: :add_tag)
      end

      it "returns 400 if circle don't have conversation type as private_group" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        put :add_as_admin, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 404 if user created type circle has private_group but participant is not found" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :add_admin)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        put :add_as_admin, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 403 if user created type circle has private_group but user is not owner" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        participant = FactoryBot.create(:user)
        put :add_as_admin, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:forbidden)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 200 if user created type circle has private_group and user is owner" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :add_admin)
        participant = FactoryBot.create(:user)
        put :add_as_admin, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

    end
  end

  describe "PUT #remove_as_admin" do
    context "Remove user as admin" do
      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2402.26.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @permission_group = FactoryBot.build(:permission_group, name: 'admin')
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission,
                                                         permission_group_id: @permission_group.id,
                                                         permission_identifier: :add_tag)
      end

      it "returns 400 if circle don't have conversation type as private_group" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        put :remove_as_admin, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 404 if user created type circle has private_group but participant is not found" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :remove_admin)
        put :remove_as_admin, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 403 if user created type circle has private_group but user is not owner" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        participant = FactoryBot.create(:user)
        UserCirclePermissionGroup.where(user_id: participant.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id).first&.destroy
        put :remove_as_admin, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:forbidden)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 200 if user created type circle has private_group and user is owner if given participant is not admin already" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :remove_admin)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        participant = FactoryBot.create(:user)
        put :remove_as_admin, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

      it "return 200 if user created type circle has private_group and user is owner if given participant is admin" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :remove_admin)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        participant = FactoryBot.create(:user)
        circle.add_user_as_admin(participant.id)
        user_circle_permissions_as_admin = UserCirclePermissionGroup.where(user_id: participant.id, circle_id: circle.id)
        expect(user_circle_permissions_as_admin).to be_present
        put :remove_as_admin, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        user_circle_permissions_after_removing_as_admin = UserCirclePermissionGroup.where(user_id: participant.id, circle_id: circle.id)
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
        expect(user_circle_permissions_after_removing_as_admin).to be_blank
      end
    end
  end

  describe "PUT #add_user_as_sender_to_private_group" do
    context "Add user as sender to private group" do
      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2402.26.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @permission_group = FactoryBot.build(:permission_group, name: 'admin')
        @permission_group.save(validate: false)

        @permission_group_permission = FactoryBot.create(:permission_group_permission,
                                                         permission_group_id: @permission_group.id,
                                                         permission_identifier: :add_tag)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :add_sender)
      end

      it "returns 400 if circle don't have conversation type as private_group" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        put :add_user_as_sender_to_private_group, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 404 if user created type circle has private_group but participant is not found" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        put :add_user_as_sender_to_private_group, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 403 if circle has private_group but user is not owner or admin" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        participant = FactoryBot.create(:user)
        put :add_user_as_sender_to_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:forbidden)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 200 if circle has private_group and user is owner" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        participant = FactoryBot.create(:user)
        put :add_user_as_sender_to_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

      it "return 200 if circle has private_group and user is admin" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: @permission_group.id,
                          permission_identifier: :add_sender)
        admin_permission_group = PermissionGroup.find_by(name: 'admin').id
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: admin_permission_group)
        participant = FactoryBot.create(:user)
        put :add_user_as_sender_to_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

      it "return 200 if circle has private group and user is already sender" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: @permission_group.id,
                          permission_identifier: :add_sender)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: @permission_group.id)
        participant = FactoryBot.create(:user)
        ExcludedUserCirclePermission.where(user_id: participant.id, circle_id: circle.id).destroy_all
        put :add_user_as_sender_to_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

    end
  end

  describe "PUT #remove_user_as_sender_from_private_group" do
    context "Add remove as sender from private group" do
      before :each do
        @user = FactoryBot.create(:user, dynamic_link: 'test')
        AppVersionSupport.new('2402.26.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
        @permission_group = FactoryBot.build(:permission_group, name: 'admin')
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission,
                                                         permission_group_id: @permission_group.id,
                                                         permission_identifier: :add_tag)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: @permission_group.id,
                          permission_identifier: :remove_sender)
      end

      it "returns 400 if circle don't have conversation type as private_group" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :none)
        put :remove_user_as_sender_from_private_group, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 404 if user created type circle has private_group but participant is not found" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        put :remove_user_as_sender_from_private_group, params: { circle_id: circle.id, user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 403 if circle has private_group but user is not owner or admin" do
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        participant = FactoryBot.create(:user)
        put :remove_user_as_sender_from_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:forbidden)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_falsey
      end

      it "return 200 if circle has private_group and user is owner" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: Constants.owner_permission_group_id)
        FactoryBot.create(:permission_group_permission,
                          permission_group_id: Constants.owner_permission_group_id,
                          permission_identifier: :remove_sender)
        participant = FactoryBot.create(:user)
        put :remove_user_as_sender_from_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

      it "return 200 if circle has private_group and user is admin" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        admin_permission_group = PermissionGroup.find_by(name: 'admin').id
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: admin_permission_group)
        participant = FactoryBot.create(:user)
        put :remove_user_as_sender_from_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

      it "return 200 if circle has private group and user is already removed as sender" do
        circle = FactoryBot.create(:circle, circle_type: :user_created, level: :private_group, conversation_type: :private_group)
        admin_permission_group = PermissionGroup.find_by(name: 'admin').id
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user.id, circle_id: circle.id, permission_group_id: admin_permission_group)
        ExcludedUserCirclePermission.where(user_id: @user.id, circle_id: circle.id, permission_identifier: :private_group_send_message).first_or_create
        participant = FactoryBot.create(:user)
        put :remove_user_as_sender_from_private_group, params: { circle_id: circle.id, user_id: @user.id, participant_id: participant.id }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to be_truthy
      end

    end
  end

  describe 'GET #show_v2' do
    context 'check circle feed creative carousel' do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @party = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @leader = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @circle_photo = FactoryBot.create(:circle_photo, circle: @party)

        # to return framed poster requires user profile photo
        @photo = FactoryBot.create(:photo)
        @user = FactoryBot.create(:user, photo_id: @photo.id, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

        AppVersionSupport.new('2402.15.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2402.15.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it 'no creative carousel if there are no creatives' do
        get :show_v2, params: { circle_id: @party.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body['preload_feed_items']).to be_empty
      end

      it 'returns creative carousel with non expired active creatives' do
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        @admin_medium1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        event1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.zone.now + 2.hours)
        @poster_creative1 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          primary: true,
          event: event1
        )
        event2 = FactoryBot.create(:event, start_time: Time.zone.now - 3.hour, end_time: Time.zone.now - 1.hours)
        @poster_creative2 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          primary: true,
          event: event2
        )

        FactoryBot.create(:event_circle, event: event1, circle: @party)
        FactoryBot.create(:event_circle, event: event2, circle: @party)

        get :show_v2, params: { circle_id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body['preload_feed_items']).to be_present
        expect(body['preload_feed_items'][0]['feed_type']).to eq('creative_carousel')
        expect(body['preload_feed_items'][0]['items'].length).to eq(1)
        expect(body['preload_feed_items'][0]['items'][0]['id']).to eq(@poster_creative1.id.to_s)
      end

      it 'should not return creatives that are expired even though event is not' do
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        @admin_medium1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        event1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.zone.now + 2.hours)
        @poster_creative1 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          primary: true,
          event: event1
        )
        event2 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.zone.now + 2.hours)
        @poster_creative2 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          start_time: Time.zone.now - 3.hours,
          end_time: Time.zone.now - 1.hours,
          primary: true,
          event: event2
        )

        random_circle = FactoryBot.create(:circle)
        event_in_random_circle = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.zone.now + 2.hours)
        @poster_creative3 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          primary: true,
          event: event_in_random_circle
        )

        FactoryBot.create(:event_circle, event: event1, circle: @party)
        FactoryBot.create(:event_circle, event: event2, circle: @party)
        FactoryBot.create(:event_circle, event: event_in_random_circle, circle: random_circle)

        get :show_v2, params: { circle_id: @party.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body['preload_feed_items']).to be_present
        expect(body['preload_feed_items'][0]['feed_type']).to eq('creative_carousel')
        expect(body['preload_feed_items'][0]['items'].length).to eq(1)
        expect(body['preload_feed_items'][0]['items'][0]['id']).to eq(@poster_creative1.id.to_s)
      end

      it 'returns creative carousel with non expired creatives' do
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        @admin_medium1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        event1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.zone.now + 2.hours)
        @poster_creative1 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          primary: true,
          event: event1
        )
        event2 = FactoryBot.create(:event, start_time: Time.zone.now - 3.hour, end_time: Time.zone.now - 1.hours)
        @poster_creative2 = FactoryBot.create(
          :poster_creative,
          photo_v3: @admin_medium1,
          photo_v2: @admin_medium2,
          primary: true,
          event: event2
        )

        FactoryBot.create(:event_circle, event: event1, circle: @leader)
        FactoryBot.create(:event_circle, event: event2, circle: @leader)

        get :show_v2, params: { circle_id: @leader.id }
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body['preload_feed_items']).to be_present
        expect(body['preload_feed_items'][0]['feed_type']).to eq('creative_carousel')
        expect(body['preload_feed_items'][0]['items'].length).to eq(1)
        expect(body['preload_feed_items'][0]['items'][0]['id']).to eq(@poster_creative1.id.to_s)
      end
    end
  end
end
