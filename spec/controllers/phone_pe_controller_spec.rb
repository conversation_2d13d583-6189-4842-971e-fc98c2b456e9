# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PhonePeController, type: :controller do
  describe 'Authentication' do
    context 'with valid credentials' do
      before do
        allow(Rails.application.credentials).to receive(:[]).with(:phonepe).and_return({
          auth_username: 'phonepe',
          auth_secret: 'test_secret'
        })

        # Set up the authorization header with valid credentials
        auth_string = Base64.strict_encode64('phonepe:test_secret')
        request.headers['Authorization'] = "Basic #{auth_string}"
      end

      it 'allows access to subscription_callback' do
        post :subscription_callback, params: { event: 'test_event', payload: {} }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid credentials' do
      before do
        # Set up the authorization header with invalid credentials
        auth_string = Base64.strict_encode64('invalid:credentials')
        request.headers['Authorization'] = "Basic #{auth_string}"
      end

      it 'denies access to subscription_callback' do
        post :subscription_callback, params: { event: 'test_event', payload: {} }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'subscription_callback' do
    before do
      # Set up valid authentication for all tests in this context
      allow(Rails.application.credentials).to receive(:[]).with(:phonepe).and_return({
        auth_username: 'phonepe',
        auth_secret: 'test_secret'
      })

      auth_string = Base64.strict_encode64('phonepe:test_secret')
      request.headers['Authorization'] = "Basic #{auth_string}"

      # Allow other credential lookups
      allow(Rails.application.credentials).to receive(:[]).and_call_original

      # Mock the set_service method to bypass authentication
      allow_any_instance_of(ExternalServiceController).to receive(:set_service).and_return('phonepe')
    end

    context 'with subscription.setup.order.completed event' do
      let(:subscription) { create(:subscription, pg_id: 'test_merchant_subscription_id', status: :created, pg_reference_id: nil) }
      let(:subscription_charge) { create(:subscription_charge, subscription: subscription, status: :created) }

      let(:payload) do
        {
          state: "COMPLETED",
          paymentFlow: {
            merchantSubscriptionId: subscription.pg_id,
            subscriptionId: 'phonepe_subscription_id'
          }
        }
      end

      before do
        subscription_charge # Create the charge
      end

      it 'updates the subscription and activates it' do
        expect(subscription.status).to eq('created')

        post :subscription_callback, params: {
          event: 'subscription.setup.order.completed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
        subscription.reload
        expect(subscription.pg_reference_id).to eq('phonepe_subscription_id')
        expect(subscription.status).to eq('active')
      end

      it 'updates the subscription charge to success' do
        expect(subscription_charge.status).to eq('created')

        post :subscription_callback, params: {
          event: 'subscription.setup.order.completed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
        subscription_charge.reload
        expect(subscription_charge.status).to eq('success')
      end
    end

    context 'with subscription.setup.order.failed event' do
      let(:subscription) { create(:subscription, pg_id: 'test_merchant_subscription_id', status: :created) }
      let(:merchant_order_id) { "MO#{Time.zone.now.to_i}" }
      let(:subscription_charge) { create(:subscription_charge, subscription: subscription, status: :created, pg_id: merchant_order_id) }

      let(:payload) do
        {
          state: "FAILED",
          merchantOrderId: merchant_order_id,
          paymentFlow: {
            merchantSubscriptionId: subscription.pg_id
          }
        }
      end

      before do
        subscription_charge # Create the charge
      end

      it 'updates the subscription and closes it' do
        expect(subscription.status).to eq('created')

        post :subscription_callback, params: {
          event: 'subscription.setup.order.failed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
        subscription.reload
        expect(subscription.status).to eq('closed')
      end

      it 'updates the subscription charge to failed' do
        expect(subscription_charge.status).to eq('created')

        post :subscription_callback, params: {
          event: 'subscription.setup.order.failed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
        subscription_charge.reload
        expect(subscription_charge.status).to eq('failed')
      end

      it 'logs a notification when subscription charge is not found' do
        # Delete the subscription charge
        subscription_charge.destroy

        # Expect Honeybadger notification
        expect(Honeybadger).to receive(:notify).with('Subscription charge not found', context: { params: anything })

        post :subscription_callback, params: {
          event: 'subscription.setup.order.failed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
      end

      it 'logs a notification when state is not FAILED' do
        # Change the state to something unexpected
        payload[:state] = "UNKNOWN"

        # Expect Honeybadger notification
        expect(Honeybadger).to receive(:notify).with('Unexpected state for subscription_charge in PhonePe setup failed',
                                                    context: hash_including(:subscription_charge_id, :state))

        post :subscription_callback, params: {
          event: 'subscription.setup.order.failed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with subscription state change events' do
      let(:subscription) { create(:subscription, pg_id: 'test_merchant_subscription_id', status: :active) }

      let(:cancelled_payload) do
        {
          merchantSubscriptionId: subscription.pg_id,
          state: "CANCELLED"
        }
      end

      let(:paused_payload) do
        {
          merchantSubscriptionId: subscription.pg_id,
          state: "PAUSED"
        }
      end

      let(:active_payload) do
        {
          merchantSubscriptionId: subscription.pg_id,
          state: "ACTIVE"
        }
      end

      it 'handles subscription cancelled event' do
        expect(subscription.status).to eq('active')

        post :subscription_callback, params: {
          type: 'SUBSCRIPTION_CANCELLED',
          payload: cancelled_payload
        }

        expect(response).to have_http_status(:ok)
        subscription.reload
        expect(subscription.status).to eq('cancelled')
      end

      it 'handles subscription paused event' do
        expect(subscription.status).to eq('active')

        post :subscription_callback, params: {
          type: 'SUBSCRIPTION_PAUSED',
          payload: paused_payload
        }

        expect(response).to have_http_status(:ok)
        subscription.reload
        expect(subscription.status).to eq('paused')
      end

      it 'handles subscription unpaused event' do
        # First pause the subscription
        subscription.update(status: :paused)
        expect(subscription.status).to eq('paused')

        post :subscription_callback, params: {
          type: 'SUBSCRIPTION_UNPAUSED',
          payload: active_payload
        }

        expect(response).to have_http_status(:ok)
        subscription.reload
        expect(subscription.status).to eq('active')
      end
    end

    context 'with redemption transaction events' do
      let(:subscription) { create(:subscription, pg_id: 'test_merchant_subscription_id', status: :active) }
      let(:merchant_order_id) { "MO#{Time.zone.now.to_i}" }

      let(:payload) do
        {
          state: "COMPLETED",
          merchantOrderId: merchant_order_id,
          amount: 10000, # 100 rupees in paise
          paymentFlow: {
            merchantSubscriptionId: subscription.pg_id
          }
        }
      end

      it 'creates a new subscription charge for completed transaction' do
        expect {
          post :subscription_callback, params: {
            event: 'subscription.redemption.transaction.completed',
            payload: payload
          }
        }.to change(SubscriptionCharge, :count).by(1)

        expect(response).to have_http_status(:ok)
        charge = SubscriptionCharge.last
        expect(charge.subscription_id).to eq(subscription.id)
        expect(charge.status).to eq('success')
        expect(charge.amount).to eq(100.0) # Converted from paise to rupees
      end

      it 'creates a new subscription charge for failed transaction' do
        payload[:state] = "FAILED"

        expect {
          post :subscription_callback, params: {
            event: 'subscription.redemption.transaction.failed',
            payload: payload
          }
        }.to change(SubscriptionCharge, :count).by(1)

        expect(response).to have_http_status(:ok)
        charge = SubscriptionCharge.last
        expect(charge.subscription_id).to eq(subscription.id)
        expect(charge.status).to eq('failed')
        expect(charge.amount).to eq(100.0) # Converted from paise to rupees
      end

      it 'updates an existing subscription charge for completed transaction' do
        # Create a charge with the same pg_id as merchantOrderId
        charge = create(:subscription_charge,
                        subscription: subscription,
                        pg_id: merchant_order_id,
                        status: :sent_to_pg)

        post :subscription_callback, params: {
          event: 'subscription.redemption.transaction.completed',
          payload: payload
        }

        expect(response).to have_http_status(:ok)
        charge.reload
        expect(charge.status).to eq('success')
      end
    end
  end
end
