require 'rails_helper'

RSpec.describe SingularController, type: :controller do
  describe 'POST #postback' do

    before do
      allow(SingularDatum).to receive(:create!)
      allow(MixpanelIntegration).to receive(:perform_async)
    end

    it 'returns http success for facebook signups' do
      user_id = 1

      post :postback, body: {
        :user_id => user_id,
        :event_name => "Signup",
        :partner_site => "Facebook",
        :campaign => "Some campaign",
        :pcrid => "1681116565",
        :install_utc_timestamp => 1681116565
      }.to_json, as: :json

      expect(response).to have_http_status(:success)

      expected_attribution = SingularUserAttribution.new(
        :medium => "Social media",
        :source => "Facebook",
        :campaign => "Some campaign",
        :creative_id => "1681116565",
        :installed_on => 1681116565
      )

      expect(SingularDatum).to have_received(:create!).with({ :utm_medium => expected_attribution.medium,
                                                              :utm_source => expected_attribution.source,
                                                              :utm_campaign => expected_attribution.campaign,
                                                              :creative_id => expected_attribution.creative_id,
                                                              :invited_via => expected_attribution.invited_via,
                                                              :invited_by => nil,
                                                              :installed_on => Time.zone.at(1681116565),
                                                              :user_id => user_id })

      expect(MixpanelIntegration).to have_received(:perform_async).with(user_id, {
        "UTM Source" => expected_attribution.source,
        "UTM Medium" => expected_attribution.medium,
        "UTM Campaign" => expected_attribution.campaign,
        "Attributed Creative ID" => expected_attribution.creative_id,
      })
    end

    it 'returns http success for whatsapp campaigns' do
      user_id = rand(1000)
      campaigns = %w[GTcampaign2badgeusers GTCampaign1PartyNames GT3Campaign Gt2campaign2badgewithlink Gtcampaign3withlink Target_users_based_on_g2_leaders updated2_onboarding_badgeusers_withlink]
      expect(SingularController::WHATSAPP_CAMPAIGNS).to eq(campaigns.map(&:downcase))

      campaigns.each do |campaign|
        post :postback, body: {
          :user_id => user_id,
          :event_name => "Signup",
          :partner_site => "Whatsapp",
          :campaign => campaign,
          :pcrid => "1681116565",
          :install_utc_timestamp => 1681116565
        }.to_json, as: :json

        expect(response).to have_http_status(:success)

        expected_attribution = SingularUserAttribution.new(
          :medium => "Social",
          :source => "Whatsapp",
          :campaign => campaign,
          :creative_id => "1681116565",
          :installed_on => 1681116565
        )

        expect(SingularDatum).to have_received(:create!).with({ :utm_medium => expected_attribution.medium,
                                                                :utm_source => expected_attribution.source,
                                                                :utm_campaign => expected_attribution.campaign,
                                                                :creative_id => expected_attribution.creative_id,
                                                                :invited_via => expected_attribution.invited_via,
                                                                :invited_by => nil,
                                                                :installed_on => Time.zone.at(1681116565),
                                                                :user_id => user_id })

        expect(MixpanelIntegration).to have_received(:perform_async).with(user_id, {
          "UTM Source" => expected_attribution.source,
          "UTM Medium" => expected_attribution.medium,
          "UTM Campaign" => expected_attribution.campaign,
          "Attributed Creative ID" => expected_attribution.creative_id,
        })
      end
    end

    it 'returns http success for wati social & growth campaings' do
      user_id = rand(1000)
      campaigns = %w[wati social growth]
      campaigns.each do |campaign|
        post :postback, body: {
          :user_id => user_id,
          :event_name => "Signup",
          :partner_site => "Whatsapp",
          :campaign => campaign,
          :pcrid => "1681116565",
          :install_utc_timestamp => 1681116565
        }.to_json, as: :json

        expect(response).to have_http_status(:success)

        expected_attribution = SingularUserAttribution.new(
          :medium => "Social",
          :source => "Whatsapp",
          :campaign => campaign,
          :creative_id => "1681116565",
          :installed_on => 1681116565
        )

        expect(SingularDatum).to have_received(:create!).with({ :utm_medium => expected_attribution.medium,
                                                                :utm_source => expected_attribution.source,
                                                                :utm_campaign => expected_attribution.campaign,
                                                                :creative_id => expected_attribution.creative_id,
                                                                :invited_via => expected_attribution.invited_via,
                                                                :invited_by => nil,
                                                                :installed_on => Time.zone.at(1681116565),
                                                                :user_id => user_id })

        expect(MixpanelIntegration).to have_received(:perform_async).with(user_id, {
          "UTM Source" => expected_attribution.source,
          "UTM Medium" => expected_attribution.medium,
          "UTM Campaign" => expected_attribution.campaign,
          "Attributed Creative ID" => expected_attribution.creative_id,
        })
      end
    end

    it 'returns http success for referral campaigns' do
      campaign_mappings = { "postshare1" => "Post share",
                            "post_share_new" => "Post share",
                            "mweb_install" => "Mobile web install",
                            "Profile Share 1" => "Profile invite",
                            "Profile Share 2" => "Profile invite",
                            "profile_share_new" => "Profile invite",
                            "circleshare1" => "Group invite",
                            "circle_share_new" => "Group invite",
                            "channel_share" => "Channel share",
                            "badge Icon status" => "Badge_Icon_status",
                            "hashtagshare 1" => "Hashtag share",
                            "hashtag_share_new" => "Hashtag share",
                            "Poster Share" => "Poster share",
                            "poster_share_new" => "Poster share",
                            "premium_poster_share" => "Premium Poster share",
                            "poster_event_share" => "Poster Event share",
                            "poster_channel_share" => "Poster Channel share",
                            "tg_election_results_share" => "TG Election Results share",
                            "constituency_winner_share" => "Constituency Winner share",
                            "YSR Birthday" => "Poster share" }

      expect(SingularController::REFERRAL_CAMPAIGN_MAPPINGS).to eq(campaign_mappings)

      campaign_mappings.each do |campaign, expected_campaign|
        puts 'running for campaign: ' + campaign + ' and expected_campaign: ' + expected_campaign

        user_id = rand(1000)
        circle_id = rand(1000)
        post :postback, body: {
          :user_id => user_id,
          :campaign => campaign,
          :event_name => "Signup",
          :pcrid => "1681116565",
          :install_utc_timestamp => 1681116565,
          :deeplink => "praja://circles/#{circle_id}&dl=praja",
          :partner_affiliate_id => "1681116565"
        }.to_json, as: :json

        expect(response).to have_http_status(:success)

        expected_attribution = SingularUserAttribution.new(
          :medium => "Referrals",
          :source => "Shares",
          :campaign => expected_campaign,
          :creative_id => "1681116565",
          :invited_via => circle_id.to_s,
          :installed_on => 1681116565,
          :inviter => 1681116565
        )

        expect(SingularDatum).to have_received(:create!).with({ :utm_medium => expected_attribution.medium,
                                                                :utm_source => expected_attribution.source,
                                                                :utm_campaign => expected_attribution.campaign,
                                                                :creative_id => expected_attribution.creative_id,
                                                                :invited_via => expected_attribution.invited_via,
                                                                :invited_by => 1681116565,
                                                                :installed_on => Time.zone.at(1681116565),
                                                                :user_id => user_id })

        expect(MixpanelIntegration).to have_received(:perform_async).with(user_id, {
          "UTM Source" => expected_attribution.source,
          "UTM Medium" => expected_attribution.medium,
          "UTM Campaign" => expected_attribution.campaign,
          "Inviter" => "1681116565",
          "Invited Via" => expected_attribution.invited_via,
          "Attributed Creative ID" => expected_attribution.creative_id,
        })
      end
    end

    it 'support inserts for unknown campaign' do
      user_id = rand(1000)
      circle_id = rand(1000)
      post :postback, body: {
        :user_id => user_id,
        :campaign => 'Bad Campaign Value',
        :event_name => "Signup",
        :pcrid => "1681116565",
        :install_utc_timestamp => 1681116565,
        :deeplink => "praja://circles/#{circle_id}&dl=praja",
        :partner_affiliate_id => "1681116565"
      }.to_json, as: :json

      expect(response).to have_http_status(:success)

      expected_attribution = SingularUserAttribution.new(
        :medium => "Unknown",
        :source => "Unknown",
        :campaign => "Bad Campaign Value",
        :creative_id => "1681116565",
        :invited_via => circle_id.to_s,
        :installed_on => 1681116565,
        :inviter => 1681116565
      )

      expect(SingularDatum).to have_received(:create!).with({ :utm_medium => expected_attribution.medium,
                                                              :utm_source => expected_attribution.source,
                                                              :utm_campaign => expected_attribution.campaign,
                                                              :creative_id => expected_attribution.creative_id,
                                                              :invited_via => expected_attribution.invited_via,
                                                              :invited_by => 1681116565,
                                                              :installed_on => Time.zone.at(1681116565),
                                                              :user_id => user_id })

      expect(MixpanelIntegration).to have_received(:perform_async).with(user_id, {
        "UTM Source" => expected_attribution.source,
        "UTM Medium" => expected_attribution.medium,
        "UTM Campaign" => expected_attribution.campaign,
        "Inviter" => "1681116565",
        "Invited Via" => expected_attribution.invited_via,
        "Attributed Creative ID" => expected_attribution.creative_id,
      })
    end

    it 'returns unprocessable_entity for any exception raised' do
      user_id = rand(1000)
      circle_id = rand(1000)
      allow(SingularDatum).to receive(:create!).and_raise('Exception')

      post :postback, body: {
        :user_id => user_id,
        :campaign => 'Bad Campaign Value',
        :event_name => "Signup",
        :pcrid => "1681116565",
        :install_utc_timestamp => 1681116565,
        :deeplink => "praja://circles/#{circle_id}&dl=praja",
        :partner_affiliate_id => "1681116565"
      }.to_json, as: :json

      expect(response).to have_http_status(:unprocessable_entity)
    end

  end
end
