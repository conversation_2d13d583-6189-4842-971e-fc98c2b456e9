require 'rails_helper'

RSpec.describe OrdersController, type: :controller do
  describe "GET #get_checkout_url" do
    context "get checkout url for order" do
      before :each do
        @user = FactoryBot.create(:user)

        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        checkout_url_response =
          {
            success: true,
            url: 'https://www.circleapp.in',
            method: 'POST',
            transactionId: 123,
            share_text: "Checkout url",
          }
        allow(PaymentUtils).to receive(:generate_checkout_url).and_return(checkout_url_response)
      end
      it "returns data when order is pending" do
        @order = FactoryBot.create(:order, status: :pending, user_id: @user.id)
        get :get_checkout_url, params: { order_id: @order.id }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)["success"]).to eq(false)
        expect(JSON.parse(response.body)["message"]).to eq("Payment is still processing")
      end
      it "returns data when order is successful" do
        @item_price = FactoryBot.create(
          :item_price,
          item_id: Constants.get_poster_product_id,
          item_type: 'Product',
          price: 1,
          maintenance_price: 0,
          duration_in_months: 1)
        @order = FactoryBot.create(:order, user_id: @user.id, status: :opened, order_items: [
          FactoryBot.build(
            :order_item,
            item_id: Constants.get_poster_product_id,
            item_type: 'Product',
            item_price_id: @item_price.id,
            duration_in_months: 1,
            total_item_price: 1
          )
        ])
        @order.update(status: :successful)
        get :get_checkout_url, params: { order_id: @order.id }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)["success"]).to eq(false)
        expect(JSON.parse(response.body)["message"]).to eq("Already paid")
      end
      it "returns data when order is opened" do
        @order = FactoryBot.create(:order, status: :opened, user_id: @user.id)
        get :get_checkout_url, params: { order_id: @order.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["success"]).to eq(true)
        expect(JSON.parse(response.body)["url"]).to eq("https://praja.app/orders/#{@order.id}/cashfree-checkout")
      end
    end
  end

  describe "GET #get_poster_order" do
    context "get poster order" do
      before :each do
        @user = FactoryBot.create(:user)

        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns data when order is pending" do
        @order = FactoryBot.create(:order, status: :pending, user_id: @user.id)
        get :get_poster_order, params: { order_id: @order.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["id"]).to eq(@order.id)
      end
    end
  end

  describe "GET #show in orders" do
    context "get order" do
      before :each do
        @user = FactoryBot.create(:user)

        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2308.10.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "returns data when order is pending" do
        @order = FactoryBot.create(:order, status: :pending, user_id: @user.id)
        FactoryBot.create(:order_transaction, order_id: @order.id, status: :pending)
        allow(PaymentUtils).to receive(:get_transaction_status).and_return(nil)
        get :show, params: { id: @order.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["id"]).to eq(@order.id)
      end
    end
  end

  describe '#order_params' do
    it 'should permit order' do
      permitted_params = %i[user_id status total_amount payable_amount discount_amount referred_by]
      # Simulate a request with additional, non-permitted parameters
      params = ActionController::Parameters.new(
        order: {
          user_id: 1,
          status: "pending",
          total_amount: 100,
          payable_amount: 100,
          discount_amount: 0,
          referred_by: 2
        }
      )

      # Assign params to the controller
      controller.params = params

      filtered_params = controller.send(:order_params)
      # Check that only permitted parameters are present
      expect(filtered_params.keys).to contain_exactly(*permitted_params.map(&:to_s))
    end
  end
  #
  # describe '#premium_bottom_sheet' do
  #   context 'when user has orders' do
  #     before :each do
  #       @user = FactoryBot.create(:user)
  #       @order = FactoryBot.create(:order, :with_order_items, user: @user, duration_in_months: 6, old_version_default: false)
  #       @order1 = FactoryBot.create(:order, :with_order_items, user: @user, duration_in_months: 12, old_version_default: true)
  #       @token = @user.generate_jwt_token
  #       request.headers['Authorization'] = "Bearer #{@token}"
  #       request.headers['X-App-Version'] = '2407.18.01'
  #       request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
  #     end
  #     it 'returns orders array' do
  #       get :premium_bottom_sheet
  #       expect(response).to have_http_status(:ok)
  #       body = JSON.parse(response.body)
  #       orders = body['orders']
  #       expect(orders).not_to be_empty
  #       expect(body['title']).not_to be_nil
  #       expect(orders.size).to eq(2)
  #       expect(orders.first['selected']).to be true
  #       expect(orders.first['id']).to eq(@order1.id)
  #       expect(orders.last['selected']).to be false
  #     end
  #   end
  # end

end
