require 'rails_helper'

RSpec.describe Poster<PERSON>reativesController, type: :controller do
  include <PERSON><PERSON><PERSON>

  describe "GET #get_creatives_of_category" do
    context "get poster creatives of an event or creative kind" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2306.27.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2306.27.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it 'get poster creatives by creative_id' do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        get :get_creatives_of_category, params: { creative_id: @poster_creative.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['creatives'].size).to eq(1)
        expect(response_body['layouts']).to be_present
      end
      it 'get poster creatives by creative_id' do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        get :get_creatives_of_category, params: { creative_id: @poster_creative.id, circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['creatives'].size).to eq(1)
        expect(response_body['layouts']).to be_present
      end
      it "get poster creatives of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        get :get_creatives_of_category, params: { id: @event.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["creatives"].size).to eq(1)
        expect(response_body["layouts"]).to be_present
      end

      it "get poster creatives of a creative kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        get :get_creatives_of_category, params: { category_kind: "schemes", circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["creatives"].size).to eq(1)
        expect(response_body["layouts"]).to be_present
      end

      it "send success request for poster creatives of a creative kind info who is a same party badge user" do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        get :get_creatives_of_category, params: { category_kind: "info", circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["creatives"].size).to eq(1)
        expect(response_body["layouts"]).to be_present
      end
      it "send unauthorized request for political party poster creatives of a creative kind info who is a opposite party badge user" do
        @interest_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @interest_circle.id)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        get :get_creatives_of_category, params: { category_kind: "info", circle_id: @circle.id }
        expect(response).to have_http_status(:forbidden)
      end

      it "send unauthorized request for political leader circle poster creatives of a creative kind info who is a opposite party badge user" do
        @interest_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @interest_circle.id)
        @leader_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                           circle_photos:
                                             [FactoryBot.build(:circle_photo,
                                                               photo: FactoryBot.create(:photo),
                                                               photo_type: :poster,
                                                               photo_order: 1)])
        @political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @political_circle, relation: :Leader2Party)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @leader_circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @leader_circle)
        get :get_creatives_of_category, params: { category_kind: "info", circle_id: @leader_circle.id }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end
  describe "GET #dm_channel_poster_attachment_preview" do
    context "get poster creative of a given id" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2311.0.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2311.0.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end
      it "get poster creatives of given id with an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @event_circle2 = FactoryBot.create(:event_circle, event: @event)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id, category_kind: @poster_creative.creative_kind,
                                                             circle_id: @event_circle.circle_id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["title"]).to eq(@event.name)
        expect(response_body["image_urls"]).to eq([@poster_creative.photo_v2.url])
        expect(response_body["params"]["creative_id"]).to eq(@poster_creative.id)
        expect(response_body["circle"]).not_to be_nil
      end

      it "get poster creative with a given id without any event" do
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, primary: true, event: nil,
                                             photo_v2: @admin_medium_2, creative_kind: :schemes)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id,
                                                             category_kind: @poster_creative.creative_kind,
                                                             circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["title"]).to eq("")
        expect(response_body["image_urls"]).to eq([@poster_creative.photo_v2.url])
        expect(response_body["params"]["creative_id"]).to eq(@poster_creative.id)
        expect(response_body["circle"]["id"]).to eq(@circle.id)
        expect(response_body["share_text"]).to include(@circle.name)
      end
    end

    context 'get poster creatives of a given id of event' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2311.0.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2311.0.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "get poster creatives of given id with an active event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1_v1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_1_v2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1_v1,
                                              photo_v2: @admin_medium_1_v2, primary: true, start_time: Time.zone.now - 1.hour,
                                              end_time: Time.now + 2.hours)

        @admin_medium_2_v1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2_v2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_2_v1,
                                              photo_v2: @admin_medium_2_v2, primary: true)

        get :dm_channel_poster_attachment_preview, params: { id: @event.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['title']).to eq(@event.name)
        expect(response_body['image_urls']).to eq([@poster_creative1.photo_v2.url, @poster_creative2.photo_v2.url])
        expect(response_body['params']['id']).to eq(@event.id)
        expect(response_body['circle']).not_to be_nil
      end

      it 'get poster creatives given id of a completed event' do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_v1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_v2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_v1,
                                             photo_v2: @admin_medium_v2, primary: true, start_time: Time.zone.now - 1.hour,
                                             end_time: Time.zone.now + 1.hour)
        @permission_group = FactoryBot.build(:permission_group, name: "test_permission_group")
        @permission_group.save(validate: false)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)

        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :add_tag)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :remove_tag)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :circle_share_poster)

        get :dm_channel_poster_attachment_preview, params: { id: @event.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['title']).to eq(@event.name)
        expect(response_body['image_urls']).to eq([@poster_creative.photo_v2.url])
        expect(response_body['params']['id']).to eq(@event.id)
        expect(response_body['circle']).not_to be_nil
        expect(response_body['disable_cta_message']).to eq(nil)
      end
    end

    context 'get poster creatives of a kind and circle id' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2311.0.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2311.0.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "get poster creatives of given a kind and circle id" do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1_v1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_1_v2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1_v1,
                                             photo_v2: @admin_medium_1_v2, primary: true, creative_kind: :schemes)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { category_kind: @poster_creative.creative_kind, circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['title']).to eq('')
        expect(response_body['image_urls']).to eq([@poster_creative.photo_v2.url])
        expect(response_body['params']['category_kind']).to eq(@poster_creative.creative_kind)
        expect(response_body['params']['circle_id']).to eq(@circle.id)
        expect(response_body['circle']).not_to be_nil
      end
    end

    context 'disable cta message' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2311.0.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2311.0.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)

        @permission_group = FactoryBot.build(:permission_group, name: "test_permission_group")
        @permission_group.save(validate: false)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)

        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :add_tag)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :remove_tag)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group, permission_identifier: :circle_share_poster)
      end

      it 'return nil disable_cta_message if user is not badge user' do
        @poster_creative = FactoryBot.create(:poster_creative, event: nil, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 1.hour,
                                             end_time: Time.now + 1.hour)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id, circle_id: @circle.id,
                                                             category_kind: @poster_creative.creative_kind }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to be_nil

        @event = FactoryBot.create(:event, start_time: Time.zone.now - 2.hour, end_time: Time.now + 2.hours)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, start_time: Time.zone.now - 1.hour,
                                             end_time: Time.now + 1.hour)

        get :dm_channel_poster_attachment_preview, params: { id: @event.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to be_nil

        get :dm_channel_poster_attachment_preview, params: { category_kind: @poster_creative.creative_kind, circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to be_nil
      end

      it 'return nil disable_cta_message if user is not other party badge user' do
        @role = FactoryBot.create(:role)
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)

        @poster_creative = FactoryBot.create(:poster_creative, event: nil, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id, circle_id: @circle.id,
                                                             category_kind: @poster_creative.creative_kind }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to be_nil

        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        get :dm_channel_poster_attachment_preview, params: { id: @event.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to be_nil

        get :dm_channel_poster_attachment_preview, params: { category_kind: @poster_creative.creative_kind, circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to be_nil
      end

      it 'return disable_cta_message if user is a other party badge user' do
        other_party_circle = FactoryBot.create(:circle)
        @role = FactoryBot.create(:role)
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: other_party_circle.id)

        @poster_creative = FactoryBot.create(:poster_creative, event: nil, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id, circle_id: @circle.id,
                                                             category_kind: @poster_creative.creative_kind }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to eq('ప్రతిపక్ష పార్టీ నాయకులకు ఈ పోస్టర్లు అందుబాటులో లేవు')

        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        get :dm_channel_poster_attachment_preview, params: { id: @event.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to eq('ప్రతిపక్ష పార్టీ నాయకులకు ఈ పోస్టర్లు అందుబాటులో లేవు')

        get :dm_channel_poster_attachment_preview, params: { category_kind: @poster_creative.creative_kind, circle_id: @circle.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['disable_cta_message']).to eq('ప్రతిపక్ష పార్టీ నాయకులకు ఈ పోస్టర్లు అందుబాటులో లేవు')
      end
    end

    context "returns not found" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2310.29.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2310.29.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end

      it "if poster creative is inactive" do
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, active: false,
                                             start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id, category_kind: @poster_creative.creative_kind,
                                                             circle_id: @poster_creative_circle.circle_id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['title']).to eq("")
        expect(response_body['image_urls']).to eq([@poster_creative.photo_v2.url])
        expect(response_body['params']['creative_id']).to eq(@poster_creative.id)
        expect(response_body['circle']).not_to be_nil
        expect(response_body['disable_cta_message']).to eq('ఈ పోస్టర్లు అందుబాటులో లేవు')
      end

      it "time is not in between start and end time" do
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now - 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)

        get :dm_channel_poster_attachment_preview, params: { creative_id: @poster_creative.id, category_kind: @poster_creative.creative_kind,
                                                             circle_id: @poster_creative_circle.circle_id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body['title']).to eq("")
        expect(response_body['image_urls']).to eq([@poster_creative.photo_v2.url])
        expect(response_body['params']['creative_id']).to eq(@poster_creative.id)
        expect(response_body['circle']).not_to be_nil
        expect(response_body['disable_cta_message']).to eq('ఈ పోస్టర్లు అందుబాటులో లేవు')
      end
    end
  end

  describe "GET #preview_html" do
    context 'get preview html' do
      before :each do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end

      it 'returns success when creative exists' do
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        get :preview_html, params: { poster_creative_id: @poster_creative.id }
        expect(response).to have_http_status(:success)
        # TODO: check response html has appropriate meta tags
      end
    end
  end

  describe 'GET #get_posters_feed' do
    context 'when there are no poster creatives' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2310.29.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2310.29.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      end
      it 'returns not found status' do
        poster_feed_data = {}
        allow(PosterFeed).to receive(:poster_feed_query).and_return(poster_feed_data)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now - 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        get :get_posters_feed, params: { offset: 0, count: 3 }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq('No posters found')
      end
    end

    context 'when there are poster creatives' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2310.29.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2310.29.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, event_id: nil,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now + 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        poster_feed_data = { 1 => { creative_ids: [@poster_creative.id], primary_creative_id: @poster_creative.id } }
        allow(PosterFeed).to receive(:poster_feed_query).and_return(poster_feed_data)
      end
      it 'returns success status and the poster creatives' do
        get :get_posters_feed, params: { offset: 0, count: 3 }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['feed_items']).not_to be_empty
      end
    end

    context 'with different filter data' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2310.29.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2310.29.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, event_id: nil,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now + 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        poster_feed_data = { 2 => { creative_ids: [@poster_creative.id], primary_creative_id: @poster_creative.id },
                             1 => { creative_ids: [@poster_creative.id], primary_creative_id: @poster_creative.id } }
        allow(PosterFeed).to receive(:poster_feed_query).and_return(poster_feed_data)
      end

      it 'returns success status and the filtered poster creatives' do
        get :get_posters_feed, params: { offset: 0, count: 3, filter_data: { circle_id: @circle.id,
                                                                             creative_id: 2 } }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['feed_items']).not_to be_empty
      end
    end

    context 'when premium pitch is eligible for user and the signed up is not today' do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2410.17.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2410.17.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, event_id: nil,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now + 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        @event1 = FactoryBot.create(:event, start_time: Time.zone.now + 1.day, priority: :high)
        @event_circle1 = FactoryBot.create(:event_circle, event: @event1, circle: @circle)
        @poster_creative1 = FactoryBot.create(:poster_creative, event: @event1, start_time: Time.zone.now + 1.day,
                                              primary: true)
        poster_feed_data = { 1 => { creative_ids: [@poster_creative.id], primary_creative_id: @poster_creative.id } }
        allow(PosterFeed).to receive(:poster_feed_query).and_return(poster_feed_data)
      end

      it 'returns success status and the poster creatives' do
        get :get_posters_feed, params: { offset: 0, count: 3 }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['feed_items']).not_to be_empty
        expect(response_body['feed_items'].first['feed_item_id']).to eq("event_#{@event1.id}")
      end
    end

    context 'when premium pitch is eligible for user and the signed up is today' do
      before :each do
        @user = FactoryBot.create(:user, status: :pre_signup)
        @user.update(status: :active)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2410.17.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2410.17.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, event_id: nil,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now + 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        poster_feed_data = { 1 => { creative_ids: [@poster_creative.id], primary_creative_id: @poster_creative.id } }
        allow(PosterFeed).to receive(:poster_feed_query).and_return(poster_feed_data)
      end

      it 'returns success status and the poster creatives' do
        get :get_posters_feed, params: { offset: 0, count: 3 }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['feed_items']).not_to be_empty
      end
    end

    context 'when user is eligible for upgrade feed item' do
      before :each do
        target_plan = FactoryBot.create(:plan, duration_in_months: 12, amount: 2000)
        allow(Plan).to receive(:get_plan_based_on_duration).and_return(target_plan)
        @user = FactoryBot.create(:user)
        FactoryBot.create(:user_plan, user: @user, amount: 1000)
        @circle = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @user, circle: @circle)
        AppVersionSupport.new('2501.08.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2501.08.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, event_id: nil,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.now + 1.hour)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                                                    circle: @circle)
        @poster_creative1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, event_id: nil,
                                              photo_v2: @admin_medium_2, primary: true,
                                              start_time: Time.zone.now - 2.hours, end_time: Time.now + 1.hour)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative1, circle: @circle)
        poster_feed_data = { 1 => { creative_ids: [@poster_creative.id], primary_creative_id: @poster_creative.id },
                             2 => { creative_ids: [@poster_creative1.id], primary_creative_id: @poster_creative1.id } }
        allow(PosterFeed).to receive(:poster_feed_query).and_return(poster_feed_data)
        allow_any_instance_of(User).to receive(:get_plan_amount_based_on_duration).and_return(1000)
      end
      it 'returns upgrade feed item at 3 rd position along with poster creatives if campaign present' do
        allow_any_instance_of(User).to receive(:show_upgrade_package_feed_item_in_posters_feed?).and_return(true)
        campaign = FactoryBot.create(:campaign)
        allow_any_instance_of(User).to receive(:user_eligible_1_year_campaign).and_return(campaign)
        get :get_posters_feed, params: { count: 3 }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['feed_items']).not_to be_empty
        expect(parsed_response['feed_items'].last.dig("feed_type")).to eq("upgrade")
      end

      it "don't send upgrade feed item in second page along with poster creatives if campaign present" do
        allow_any_instance_of(User).to receive(:show_upgrade_package_feed_item_in_posters_feed?).and_return(true)
        campaign = FactoryBot.create(:campaign)
        allow_any_instance_of(User).to receive(:user_eligible_1_year_campaign).and_return(campaign)
        get :get_posters_feed, params: { count: 3, loaded_feed_item_ids: [10, 9, 11] }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['feed_items']).not_to be_empty
        expect(parsed_response['feed_items'].all? { |item| item.dig("feed_type") != "upgrade" }).to be_truthy
      end

      it 'returns upgrade feed item at 2nd position in third page along with poster creatives if campaign present' do
        allow_any_instance_of(User).to receive(:show_upgrade_package_feed_item_in_posters_feed?).and_return(true)
        campaign = FactoryBot.create(:campaign)
        allow_any_instance_of(User).to receive(:user_eligible_1_year_campaign).and_return(campaign)
        get :get_posters_feed, params: { count: 3, loaded_feed_item_ids: [10, 9, 11, 12, 13, 16] }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['feed_items']).not_to be_empty
        expect(parsed_response['feed_items'].second.dig("feed_type")).to eq("upgrade")
      end

      it 'returns upgrade feed item at 20th position along with poster creatives if campaign absent' do
        allow_any_instance_of(User).to receive(:show_upgrade_package_feed_item_in_posters_feed?).and_return(true)
        allow_any_instance_of(User).to receive(:user_eligible_1_year_campaign).and_return(nil)
        get :get_posters_feed, params: { count: 3,
                                         loaded_feed_item_ids: [10, 9, 11, 12, 13, 16, 17, 18, 19, 20, 21, 24, 25, 26,
                                                                27, 28, 29, 30] }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['feed_items']).not_to be_empty
        expect(parsed_response['feed_items'].second.dig("feed_type")).to eq("upgrade")
      end

      it 'returns upgrade feed item at 40th position along with poster creatives if campaign absent' do
        allow_any_instance_of(User).to receive(:show_upgrade_package_feed_item_in_posters_feed?).and_return(true)
        allow_any_instance_of(User).to receive(:user_eligible_1_year_campaign).and_return(nil)
        get :get_posters_feed, params: { count: 3,
                                         loaded_feed_item_ids: [10, 9, 11, 12, 13, 16, 17, 18, 19, 20, 21, 24, 25, 26,
                                                                27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
                                                                41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51] }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['feed_items']).not_to be_empty
        expect(parsed_response['feed_items'].first.dig("feed_type")).to eq("upgrade")
      end

      it "don't send upgrade feed item in second page along with poster creatives if campaign absent" do
        allow_any_instance_of(User).to receive(:show_upgrade_package_feed_item_in_posters_feed?).and_return(true)
        allow_any_instance_of(User).to receive(:user_eligible_1_year_campaign).and_return(nil)
        get :get_posters_feed, params: { count: 3, loaded_feed_item_ids: [10, 9, 11] }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['feed_items']).not_to be_empty
        expect(parsed_response['feed_items'].all? { |item| item.dig("feed_type") != "upgrade" }).to be_truthy
      end
    end
  end
end
