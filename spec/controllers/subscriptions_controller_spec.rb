require 'rails_helper'

RSpec.describe SubscriptionsController, type: :controller do
  describe '#show' do
    before :each do
      @user = FactoryBot.create(:user)
      @subscription = FactoryBot.create(:subscription, user: @user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2408.13.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    context 'when subscription exists and is active' do
      it 'returns successful status' do
        @subscription.update(status: :active)
        get :show, params: { id: @subscription.id }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body, symbolize_names: true)
        expect(json_response[:status]).to eq('successful')
      end
    end

    context 'when subscription exists and is created' do
      it 'returns pending status' do
        @subscription.update(status: :created)
        get :show, params: { id: @subscription.id }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body, symbolize_names: true)
        expect(json_response[:status]).to eq('pending')
      end
    end

    context 'when subscription exists and is paused' do
      it 'returns closed status' do
        @subscription.update(status: :paused)
        get :show, params: { id: @subscription.id }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body, symbolize_names: true)
        expect(json_response[:status]).to eq('failed')
      end
    end

    context 'when subscription exists and is cancelled' do
      it 'returns closed status' do
        @subscription.update(status: :cancelled)
        get :show, params: { id: @subscription.id }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body, symbolize_names: true)
        expect(json_response[:status]).to eq('failed')
      end
    end

    context 'when subscription exists and is closed' do
      it 'returns closed status' do
        @subscription.update(status: :closed)
        get :show, params: { id: @subscription.id }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body, symbolize_names: true)
        expect(json_response[:status]).to eq('failed')
      end
    end

    context 'when subscription does not exist' do
      it 'returns not found status' do
        get :show, params: { id: 'non_existent_id' }
        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body, symbolize_names: true)
        expect(json_response[:message]).to eq(I18n.t('subscription_not_found'))
      end
    end
  end
end
