require 'rails_helper'

RSpec.describe CirclesController, type: :controller do

  before :all do
    Circle.reindex
  end

  describe "Should be able to search" do
    before :each do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
      @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
      @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
      @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle: @mp_constituency)

      @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id, mla_constituency_id: @mla_constituency.id, mp_constituency_id: @mp_constituency.id)

      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "returns circles with matching the search criteria" do
      @query = "circle"
      get :get_circles_search_results
      expect(response).to have_http_status(:ok)
    end
  end
end
