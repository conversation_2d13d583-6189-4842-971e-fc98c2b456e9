require 'rails_helper'

RSpec.describe RelevantFeedItemConcern do
  include RelevantFeedItemConcern
  describe '#self_trial_nudge_feed_item' do
    let(:loaded_feed_item_ids) { [] }

    before :each do
      @user = FactoryBot.create(:user)
      allow(AppVersionSupport).to receive(:supports_autopay?).and_return(true)
      allow(@user).to receive(:increment_self_trial_nudge_seen_count)
    end

    context 'when autopay is not supported' do
      it 'returns nil' do
        allow(@user).to receive(:self_trial_nudge_seen_count_within_limit?).and_return(true)
        allow(AppVersionSupport).to receive(:supports_autopay?).and_return(false)
        expect(self_trial_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end

    context 'when feed item id is already loaded' do
      it 'returns nil' do
        allow(@user).to receive(:self_trial_nudge_seen_count_within_limit?).and_return(true)
        loaded_feed_item_ids << 'self_trial_nudge'
        expect(self_trial_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end

    context 'when user has seen the nudge more than the limit' do
      it 'returns nil' do
        allow(@user).to receive(:self_trial_nudge_seen_count_within_limit?).and_return(false)
        expect(self_trial_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end

    context 'when all conditions are met' do
      it 'returns the nudge json' do
        allow(@user).to receive(:self_trial_nudge_seen_count_within_limit?).and_return(true)
        expected_nudge_json = {
          feed_type: 'payment',
          feed_item_id: 'self_trial_nudge',
          text: nil,
          sub_text: I18n.t('trial_nudge.sub_text', duration: Constants.poster_trial_default_duration),
          discount_text: nil,
          discount_text_color: nil,
          deeplink: '/premium-experience?source=payment',
          analytics_params: {
            deeplink: '/premium-experience?source=payment',
            feed_item_id: 'self_trial_nudge',
          }
        }
        expect(self_trial_nudge_feed_item(loaded_feed_item_ids)).to eq(expected_nudge_json)
      end
    end
  end

  describe '#upgrade_package_nudge_feed_item' do
    let(:loaded_feed_item_ids) { [] }
    let(:user) { create(:user) }
    let(:user_plan) { create(:user_plan, user: user, amount: 100) }
    let(:target_plan) { create(:plan, duration_in_months: 12, amount: 1000) }

    before do
      @user = user
      allow(user).to receive(:show_upgrade_package_nudge?).and_return(true)
      allow(UserPlan).to receive(:find_by).with(user_id: user.id).and_return(user_plan)
      allow(Plan).to receive(:get_plan_based_on_duration).with(user: user, duration_in_months: 12).and_return(target_plan)
    end

    context 'when user is eligible for upgrade package nudge' do
      it 'returns the nudge json' do
        monthly_savings = user_plan.amount - target_plan.amount / target_plan.duration_in_months
        expected_nudge_json = {
          feed_type: 'payment',
          feed_item_id: 'upgrade_package_nudge',
          text: nil,
          sub_text: I18n.t('upgrade_package_nudge.text', duration: 12),
          discount_text: I18n.t('upgrade_package_nudge.sub_text', monthly_savings: monthly_savings),
          discount_text_color: nil,
          deeplink: '/upgrade?source=upgrade_nudge',
          analytics_params: {
            deeplink: '/upgrade?source=upgrade_nudge',
            feed_item_id: 'upgrade_package_nudge'
          }
        }
        expect(upgrade_package_nudge_feed_item(loaded_feed_item_ids)).to eq(expected_nudge_json)
      end
    end

    context 'when user is not eligible for upgrade package nudge' do
      it 'returns nil' do
        allow(user).to receive(:show_upgrade_package_nudge?).and_return(false)
        expect(upgrade_package_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end

    context 'when feed item id is already loaded' do
      it 'returns nil' do
        loaded_feed_item_ids << 'upgrade_package_nudge'
        expect(upgrade_package_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end

    context 'when user plan is blank' do
      it 'returns nil' do
        allow(UserPlan).to receive(:find_by).with(user_id: user.id).and_return(nil)
        expect(upgrade_package_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end

    context 'when target plan is blank' do
      it 'returns nil' do
        allow(Plan).to receive(:get_plan_based_on_duration).with(user: user, duration_in_months: 12).and_return(nil)
        expect(upgrade_package_nudge_feed_item(loaded_feed_item_ids)).to be_nil
      end
    end
  end

  describe '#offer_nudge_feed_item' do
    let(:loaded_feed_item_ids) { [] }
    let(:user) { create(:user) }
    let(:campaign) { create(:campaign, name: 'Special Offer') }

    before do
      @user = user
      allow(@user).to receive(:user_eligible_1_year_campaign).and_return(campaign)
      allow(@user).to receive(:upgrade_package_using_offer_conditions_met?).and_return(true)
      allow(AppVersionSupport).to receive(:supports_upgrade_package_sheet?).and_return(true)
    end

    it 'returns the offer nudge json when campaign is present' do
      expected_nudge_json = {
        feed_type: 'offer',
        feed_item_id: 'offer_nudge',
        text: I18n.t('offer_nudge.text', campaign_name: 'Special Offer'),
        sub_text: I18n.t('offer_nudge.sub_text'),
        button_text: I18n.t('offer_nudge.button_text'),
        deeplink: "/upgrade?source=offer_nudge&campaign_id=#{campaign.id}&offer=true",
        analytics_params: {
          deeplink: "/upgrade?source=offer_nudge&campaign_id=#{campaign.id}&offer=true",
          feed_item_id: 'offer_nudge',
          campaign_name: 'Special Offer',
          campaign_id: campaign.id,
        }.compact
      }
      expect(offer_nudge_feed_item(loaded_feed_item_ids)).to eq(expected_nudge_json)
    end

    it 'returns nil when campaign is not present' do
      allow(@user).to receive(:user_eligible_1_year_campaign).and_return(nil)
      expect(offer_nudge_feed_item(loaded_feed_item_ids)).to be_nil
    end

    it 'returns nil when feed item id is already loaded' do
      loaded_feed_item_ids << 'offer_nudge'
      expect(offer_nudge_feed_item(loaded_feed_item_ids)).to be_nil
    end
  end
end
