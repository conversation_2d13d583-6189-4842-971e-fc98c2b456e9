require 'rails_helper'

RSpec.describe ApiController, type: :controller do
  describe "#get_initial_data" do
    # Checking poster response in the get initial data
    context "check poster in response" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @state,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))
                                      ])

        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2302.28.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "status ok if poster is present" do
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).not_to be_empty
        expect(body["poster"]["id"]).to eq(@poster.id)
        expect(body["poster"]["user"]["id"]).to eq(@user.id)
        expect(body["poster"]["poster_variant"]).to eq("NORMAL")
        expect(body["poster"]["leader_photo_ring_color"]).to eq(0xff000000)
      end

      it "group dm enabled" do
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["group_dm_enabled"]).to eq(false)
      end

      it "dm channels enabled" do
        @request.headers['X-App-Version'] = '2311.30.01'
        allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["dm_channels_enabled"]).to eq(true)

        @request.headers['X-App-Version'] = '2302.28.01'
        allow_any_instance_of(User).to receive(:is_test_user?).and_return(false)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["dm_channels_enabled"]).to eq(false)
      end

      it "dm private groups enabled" do
        @request.headers['X-App-Version'] = '2311.29.01'
        allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["dm_private_groups_enabled"]).to eq(true)

        @request.headers['X-App-Version'] = '2302.28.01'
        allow_any_instance_of(User).to receive(:is_test_user?).and_return(false)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["dm_private_groups_enabled"]).to eq(false)
      end

      it "enable poster category share" do
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["enable_poster_category_share"]).to eq(false)

        allow_any_instance_of(User).to receive(:is_test_user?).and_return(true)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["enable_poster_category_share"]).to eq(true)
      end

      it 'should not send poster if poster carousel feed item is supported in that version' do
        @request.headers['X-App-Version'] = '2404.16.00'
        get :get_initial_data
        body = JSON.parse(response.body)
        expect(body["poster"]).to be_nil
      end
    end

    context "check show_follow_contacts_screen in response if poster present" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster,
                                    circle: @state,
                                    poster_photos:
                                      [FactoryBot.build(:poster_photo,
                                                        blob_data: normal_poster_photo,
                                                        photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))
                                      ])

        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2304.01.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "show_follow_contacts_screen is false if redis key returns true and poster is present" do
        @user.set_show_contacts_screen_value_to_redis(true, 90.days.to_i)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        # expect(body["poster"]).not_to be_empty #TODO:: uncomment this lines while writing poster tab test cases
        # expect(body["show_follow_contacts_screen"]).to eq(false)
      end
      it "show_follow_contacts_screen is false if redis key returns false and poster is present" do
        @user.set_show_contacts_screen_value_to_redis(false, 30.days.to_i)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        # expect(body["poster"]).not_to be_empty #TODO:: uncomment this line while writing poster tab test cases
        expect(body["show_follow_contacts_screen"]).to eq(false)
      end
    end

    context "check show_follow_contacts_screen in response if poster absent" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2304.01.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "show_follow_contacts_screen is true if redis key returns true and poster is absent" do
        @user.set_show_contacts_screen_value_to_redis(true, 90.days.to_i)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).to eq(nil)
        expect(body["show_follow_contacts_screen"]).to eq(true)
      end

      it "show_follow_contacts_screen is false if redis key returns false and poster is absent" do
        @user.set_show_contacts_screen_value_to_redis(false, 30.days.to_i)
        get :get_initial_data
        body = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(body["poster"]).to eq(nil)
        expect(body["show_follow_contacts_screen"]).to eq(false)
      end
    end

    context "check the internet connection" do
      before :each do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)
        @token = @user.generate_jwt_token
        AppVersionSupport.new('2302.28.01')
        request.headers['Authorization'] = "Bearer #{@token}"
        request.headers['X-App-Version'] = '2302.28.01'
        request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "should return video_auto_play as true if internet connection is wifi" do
        request.headers['X-Internet-Connection'] = 'wifi'
        get :get_initial_data
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["video_auto_play"]).to eq(false)
      end

      it "should return video_auto_play as false if internet connection is not wifi" do
        request.headers['X-Internet-Connection'] = 'mobile'
        get :get_initial_data
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["video_auto_play"]).to eq(false)
      end

      it "should return video_auto_play as false if internet connection is not present" do
        request.headers['X-Internet-Connection'] = nil
        get :get_initial_data
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["video_auto_play"]).to eq(false)
      end
    end

    context "check customer care user id" do
      before :each do
        praja_user = User.find_by_phone(Constants.praja_account_phone)
        praja_user = User.find_by_phone(Constants.praja_account_phone) if praja_user.nil?
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, parent_circle_id: @circle.id, parent_circle_level: nil)
        @token = @user.generate_jwt_token
        AppVersionSupport.new('2302.28.01')
        request.headers['Authorization'] = "Bearer #{@token}"
        request.headers['X-App-Version'] = '2302.28.01'
        request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "for badge user, should return customer care user id" do
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: nil)
        get :get_initial_data
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["customer_care_user_id"]).to eq(Constants.customer_care_user_id)
      end

      it "for non-badge user, should not return customer care user id" do
        get :get_initial_data
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["customer_care_user_id"]).to eq(0)
      end
    end

    context "popup_path" do
      before :each do
        @user = create(:user)
        request.headers['Authorization'] = "Bearer #{@user.generate_jwt_token}"
        request.headers['X-App-Version'] = '2407.01.02'
        request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "should return popup path as professions screen" do
        get :get_initial_data
        expect(JSON.parse(response.body)["popup_path"]).to eq("/professions?source=app_open_popup")
      end

      it "should return popup path as nil if profession is skipped by user" do
        UserMetadatum.create(user: @user, key: Constants.profession_selection_skipped_key, value: 'true')
        get :get_initial_data
        expect(JSON.parse(response.body)["popup_path"]).to be_nil
      end

      it "should return popup path as nil if profession is already selected by user" do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        political_leader = FactoryBot.create(:profession, name: 'Political Leader', name_en: 'Political Leader', admin_medium: admin_medium, ordinal: 1)

        UserProfession.create(user: @user, profession: political_leader)

        get :get_initial_data
        expect(JSON.parse(response.body)["popup_path"]).to be_nil
      end
    end

        context "set firebase app instance id" do
      before :each do
        @user = FactoryBot.create(:user)
        request.headers['Authorization'] = "Bearer #{@user.generate_jwt_token}"
        @device_id = 'device_id'
        request.headers['X-Device-Id'] = @device_id
      end

      it "should return firebase_app_instance_id if it is saved successfully with the respective user_id and device_id" do
        firebase_app_instance_id = "firebase_app_instance_id"
        user_device_token = FactoryBot.create(:user_device_token, user_id: @user.id, device_id: @device_id)
        post :get_initial_data, params: { firebase_app_instance_id: firebase_app_instance_id }
        user_device_token.reload
        expect(user_device_token.firebase_app_instance_id).to eq(firebase_app_instance_id)
      end

      it "should return nil because we are not sending any firebase_app_instance_id" do
        user_device_token = FactoryBot.create(:user_device_token, user_id: @user.id, device_id: @device_id)
        post :get_initial_data, params: {}
        user_device_token.reload
        expect(user_device_token.firebase_app_instance_id).to eq(nil)
      end

      it "ensures firebase_app_instance_id for unrelated device_id is not the same as the original one" do
        firebase_app_instance_id = "new_firebase_app_instance_id"
        old_user_device_token = FactoryBot.create(:user_device_token, user_id: @user.id, device_id: @device_id, firebase_app_instance_id: 'old_firebase_app_instance_id')
        user_device_token = FactoryBot.create(:user_device_token,user_id: @user.id, device_id: 'new_device_id')
        post :get_initial_data, params: {firebase_app_instance_id: firebase_app_instance_id}
        user_device_token.reload
        expect(user_device_token.firebase_app_instance_id).not_to eq(old_user_device_token.firebase_app_instance_id)
      end

      it "does not associate firebase_app_instance_id with a different device_id" do
        firebase_app_instance_id = "new_firebase_app_instance_id"
        user_device_token = FactoryBot.create(:user_device_token,user_id: @user.id, device_id: @device_id)
        post :get_initial_data, params: {firebase_app_instance_id: firebase_app_instance_id}
        firebase_app_instance_ids = UserDeviceToken.where(user_id: @user.id, device_id: 'unknown_device_id')
                                                   .pluck(:firebase_app_instance_id)
        expect(firebase_app_instance_ids).not_to include(firebase_app_instance_id)
      end

      it "does not set firebase_app_instance_id for an unrelated device_id" do
        firebase_app_instance_id = "new_firebase_app_instance_id"
        user_device_token = FactoryBot.create(:user_device_token, user_id: @user.id, device_id: 'unknown_device_id')
        post :get_initial_data, params: { firebase_app_instance_id: firebase_app_instance_id }
        user_device_token.reload
        expect(user_device_token.firebase_app_instance_id).to be_nil
      end

      it "should pass this test because firebase_app_instance_id is not present" do
        user_device_token = FactoryBot.create(:user_device_token, user_id: @user.id, device_id: @device_id)
        post :get_initial_data, params: {firebase_app_instance_id: ""}
        user_device_token.reload
        expect(user_device_token.firebase_app_instance_id).to eq(nil)
      end

      it "should return nil if there is no device_id" do
        firebase_app_instance_id = "new_firebase_app_instance_id"
        user_device_token = FactoryBot.create(:user_device_token, user_id: @user.id, device_id: '')
        post :get_initial_data, params: { firebase_app_instance_id: firebase_app_instance_id }
        user_device_token.reload
        expect(user_device_token.firebase_app_instance_id).to eq(nil)
      end
    end
  end

  describe "#set_app_version" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      AppVersionSupport.new('2302.28.01')
    end

    it "should set app version, pwa_version and app_os" do
      request.headers['X-App-Version'] = '2302.28.01'
      request.headers['X-PWA-Version'] = '2302.28.01'
      request.headers['X-App-OS'] = 'android'

      get :index

      app_version = controller.instance_variable_get('@app_version')
      pwa_version = controller.instance_variable_get('@pwa_version')
      app_os = controller.instance_variable_get('@app_os')

      expect(app_version).to eq(Gem::Version.new('2302.28.01'))
      expect(pwa_version).to eq(Gem::Version.new('2302.28.01'))
      expect(app_os).to eq('android')
    end

    it "should set app version, pwa_version and app_os to default if not present" do
      request.headers['X-App-Version'] = nil
      request.headers['X-PWA-Version'] = nil
      request.headers['X-App-OS'] = nil

      get :index

      app_version = controller.instance_variable_get('@app_version')
      pwa_version = controller.instance_variable_get('@pwa_version')
      app_os = controller.instance_variable_get('@app_os')

      expect(app_version).to eq(Gem::Version.new('0.3.6'))
      expect(pwa_version).to eq(Gem::Version.new('0.0.1'))
      expect(app_os).to be_nil
    end

    it "sets app_os as unknown is app_os is nil and only if app_version is greater than 1.5.1" do
      request.headers['X-App-Version'] = '1.5.1'
      request.headers['X-PWA-Version'] = '1.5.1'
      request.headers['X-App-OS'] = nil

      get :index

      app_os = controller.instance_variable_get('@app_os')

      expect(app_os).to eq('unknown')
    end
  end

  describe "#set_app_build_number" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
    end

    it "should set app build number from headers if present" do
      request.headers['X-App-BuildNumber'] = 200

      get :index

      app_build_number = controller.instance_variable_get('@app_build_number')

      expect(app_build_number).to eq(200)
    end

    it "should set app build number to default if not present" do
      request.headers['X-App-BuildNumber'] = nil
      request.headers['X-App-Version'] = "2302.28.01"

      get :index

      app_build_number = controller.instance_variable_get('@app_build_number')

      expect(app_build_number).to eq(10)
    end

    it "should return app build number if in headers app build number is nil but app version has app build number stored" do
      request.headers['X-App-BuildNumber'] = nil
      request.headers['X-App-Version'] = "1.17.0"

      get :index

      app_build_number_from_stored_json = User.get_build_number_from_app_version("1.17.0")
      app_build_number = controller.instance_variable_get('@app_build_number')

      expect(app_build_number).to eq(app_build_number_from_stored_json)
    end
  end

  describe "#check_api_key" do
    before :each do
      @user = FactoryBot.create(:user)
    end

    it "should return 401 if app version is greater than 1.7.9 and api key isn't present" do
      AppVersionSupport.new("1.7.10")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "1.7.10"
      @request.headers['X-Api-Key'] = nil

      get :index
      expect(response).to have_http_status(:unauthorized)
    end

    it "should return 401 if app version is greater than 1.7.9 and api key is present but not matched with api key in credentials" do
      AppVersionSupport.new("1.7.10")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "1.7.10"
      @request.headers['X-Api-Key'] = "wrong_api_key"

      get :index
      expect(response).to have_http_status(:unauthorized)
    end

    it "should return 200 if app version is greater than 1.7.9 and api key is present" do
      AppVersionSupport.new("1.7.10")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "1.7.10"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :index
      expect(response).to have_http_status(:ok)
    end
  end

  describe "#set_logged_in_user" do
    before :each do
      @user = FactoryBot.create(:user)
    end

    it "should return unauthorised if authorization in headers is not present" do
      AppVersionSupport.new("2302.28.01")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = nil
      @request.headers['X-App-Version'] = "2302.28.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_initial_data
      expect(response).to have_http_status(:unauthorized)
    end

    it "should return unauthorised if user is not found from token" do
      AppVersionSupport.new("2302.28.01")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2302.28.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      allow(controller).to receive(:get_user_and_send_jwt_token_header).with(@token).and_return([nil, nil])

      get :get_initial_data
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe "#set_logged_in_user_optional" do
    before :each do
      @user = FactoryBot.create(:user)
    end

    it "should return user as nil if authorization in headers is not present" do
      AppVersionSupport.new("2302.28.01")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = nil
      @request.headers['X-App-Version'] = "2302.28.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      post :save_fcm_token
      user = controller.instance_variable_get('@user')
      expect(user).to be_nil
    end

    it "should return user as nil if user is not found from token" do
      AppVersionSupport.new("2302.28.01")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2302.28.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      allow(controller).to receive(:get_user_and_send_jwt_token_header).with(@token).and_return([nil, nil])

      post :save_fcm_token
      user = controller.instance_variable_get('@user')
      expect(user).to be_nil
    end

    it "should return user if user is found from token" do
      AppVersionSupport.new("2302.28.01")
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2302.28.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      allow(controller).to receive(:get_user_and_send_jwt_token_header).with(@token).and_return([@user, @token])

      post :save_fcm_token
      user = controller.instance_variable_get('@user')
      expect(user).to eq(@user)
    end
  end

  describe "#refresh_token" do
    it "returns ok" do
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      AppVersionSupport.new('2302.28.01')
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :refresh_token
      expect(response).to have_http_status(:ok)
    end
  end

  describe "#save_fcm_token" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      AppVersionSupport.new('2302.28.01')
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = '2302.28.01'
      request.headers['X-App-OS'] = 'android'
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      request.headers['X-Device-Id'] = 'device_id'
      request.headers['X-Device-Make'] = 'device_make'
      request.headers['X-Device-Model'] = 'device_model'
    end

    it "should return ok if device token isn't in params" do
      post :save_fcm_token
      expect(response).to have_http_status(:ok)
    end

    it "should return ok and do nothing if device token is not sent" do
      user_device_token = FactoryBot.create(:user_device_token, user: @user)
      allow(UpdateDeviceToken).to receive(:perform_async)

      post :save_fcm_token, params: { device_token: '' }

      expect(UpdateDeviceToken).not_to have_received(:perform_async)
      expect(response).to have_http_status(:ok)
    end

    it "should return ok and create user device token if user device tokens with the device token doesn't exist" do
      allow(UpdateDeviceToken).to receive(:perform_async)

      post :save_fcm_token, params: { device_token: "device_token" }

      expect(UpdateDeviceToken).to have_received(:perform_async).with("device_token",
                                                                      @user.id,
                                                                      Gem::Version.new("2302.28.01").to_s,
                                                                      'android',
                                                                      'device_id',
                                                                      'device_make',
                                                                      'device_model')
      expect(response).to have_http_status(:ok)
    end

    it "should return ok and update app version & os if device token exist" do
      user_device_token = FactoryBot.create(:user_device_token, user: @user)

      allow(UpdateDeviceToken).to receive(:perform_async)

      request.headers['X-App-Version'] = '2305.28.01'
      post :save_fcm_token, params: { device_token: user_device_token.device_token }

      expect(response).to have_http_status(:ok)
      expect(UpdateDeviceToken).to have_received(:perform_async).with(user_device_token.device_token,
                                                                      @user.id,
                                                                      '2305.28.01',
                                                                      'android',
                                                                      'device_id',
                                                                      'device_make',
                                                                      'device_model')
    end
  end

  describe "#get_refer_text" do
    it "if user is internal journalist returns refer text with message" do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
      @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
      @user = FactoryBot.create(:user, internal_journalist: true, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)
      @token = @user.generate_jwt_token
      AppVersionSupport.new('2302.28.01')
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = '2302.28.01'
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]


      get :get_refer_text
      message = I18n.t('referral_texts.user_internal_journalist_refer.message', district_name: @district.name, members_count: @district.get_members_count, badge_users_count: @district.get_badge_users_count, link: "https://prajaapp.sng.link/A3x5b/7lmr?paffid=#{@user.id}")

      body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(body["text"]).to eq(message)
      expect(body["generate_invite_card"]).to be_falsey
      expect(body["invite_card_data"]).to be_nil
    end

    it "if user is badge user returns refer text with message" do
      @user = FactoryBot.create(:user)
      @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      @role = FactoryBot.create(:role, parent_circle_id: @circle.id, parent_circle_level: nil)
      @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: nil)
      @token = @user.generate_jwt_token
      AppVersionSupport.new('2302.28.01')
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = '2302.28.01'
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_refer_text
      message = I18n.t('referral_texts.user_badge_role_present_refer.message', circle_name: @user_role.get_primary_circle.name, user_role: @user_role.get_role_name, link: "https://prajaapp.sng.link/A3x5b/7lmr?paffid=#{@user.id}")

      body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(body["text"]).to eq(message)
      expect(body["generate_invite_card"]).to be_truthy
      expect(body["invite_card_data"]).to be_present
      expect(body["invite_card_data"]["user"]["id"]).to eq(@user.id)
    end

    it "if user is not badge user and not internal journalist returns refer text with message" do
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      AppVersionSupport.new('2302.28.01')
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = '2302.28.01'
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_refer_text
      message =  I18n.t('referral_texts.user_default_refer.message', link: "https://prajaapp.sng.link/A3x5b/7lmr?paffid=#{@user.id}")

      body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(body["text"]).to eq(message)
      expect(body["generate_invite_card"]).to be_truthy
      expect(body["invite_card_data"]).to be_nil
    end

    it "if user is not badge user and not internal journalist and app version is less than 1.11.3 returns refer text with message " do
      @user = FactoryBot.create(:user)
      AppVersionSupport.new('1.11.2')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = '1.11.2'
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_refer_text

      body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(body["generate_invite_card"]).to be_truthy
      expect(body["invite_card_data"]).to be_nil
    end
  end

  describe "#get_referrals_data" do
    let(:user) { FactoryBot.create(:user, phone: Faker::PhoneNumber.unique.cell_phone) }
    let(:rsa_private_key) { instance_double("OpenSSL::PKey::RSA") }

    before do
      allow(OpenSSL::PKey::RSA).to receive(:new).and_return(rsa_private_key)
      allow(rsa_private_key).to receive(:private_decrypt).and_return("decrypted_data")
      allow(controller).to receive(:referral_params).and_return({ signature: "base64_signature" })
      allow(Base64).to receive(:decode64).and_return("decrypted_data")
      allow(ActiveSupport::JSON).to receive(:decode).and_return({ 'user_id' => user.id })
      allow(User).to receive(:find).and_return(user)
      allow(user).to receive(:get_referral_data).and_return([{
                                                               name: user.name,
                                                               signup_date: user.created_at.strftime("%d %b, %Y"),
                                                               signup_type_sym: "normal_new_install",
                                                               signup_type: "Normal New Install",
                                                               points: 0,
                                                               phone: user.phone.to_s,
                                                             }])
      allow(user).to receive(:get_referral_summary).and_return({})
    end

    it "returns the correct response" do
      get :get_referrals_data
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to include("success" => true,
                                                   "total_points" => 0,
                                                   "summary_data" => {},
                                                   "data" => [{
                                                                "name" => user.name,
                                                                "signup_date" => user.created_at.strftime("%d %b, %Y"),
                                                                "signup_type_sym" => "normal_new_install",
                                                                "signup_type" => "Normal New Install",
                                                                "points" => 0,
                                                                "phone" => user.phone.to_s,
                                                              }]
                                           )
    end

    it "renders an error if decrypted data is nil" do
      allow(rsa_private_key).to receive(:private_decrypt).and_return(nil)
      get :get_referrals_data
      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)).to include("success" => false, "message" => "Invalid request")
    end

    it "renders an error if user_id is missing" do
      allow(ActiveSupport::JSON).to receive(:decode).and_return({})
      get :get_referrals_data
      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)).to include("success" => false, "message" => "Invalid request")
    end
  end

  describe "#referral_params" do
    it "returns the permitted params with required :signature" do
      params = ActionController::Parameters.new(signature: "base64_signature")
      allow(controller).to receive(:params).and_return(params)

      result = controller.send(:referral_params)

      expect(result.to_h).to eq({ "signature" => "base64_signature" })
    end

    it "permits only the :signature parameter" do
      params = ActionController::Parameters.new(signature: "base64_signature", other_param: "value")
      allow(controller).to receive(:params).and_return(params)

      result = controller.send(:referral_params)

      expect(JSON.parse(result.to_json)).to eq({ "signature" => "base64_signature" })
    end
  end
end
