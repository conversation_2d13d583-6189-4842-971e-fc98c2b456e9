require 'rails_helper'

RSpec.describe PosterSectionsController, type: :controller do
  describe "GET #my_poster_sections" do
    context "it returns subscription toast, subscription banner,events and category sections" do
      before :each do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2306.27.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2306.27.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "should return if subscription toast, subscription banner,events and category sections are present" do
        @circle = FactoryBot.create(:circle) # interest circle
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        get :my_poster_sections
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["feed_items"]).not_to be_nil
      end
    end
  end

  describe "GET #my_poster_sections_v2" do
    context "it returns subscription toast, subscription banner,events and category sections" do
      before :each do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2402.05.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2402.05.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "should return if subscription toast, subscription banner,events and category sections are present" do
        @circle = FactoryBot.create(:circle) # interest circle
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        get :my_poster_sections_v2
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["feed_items"]).not_to be_nil
      end
    end
  end
end
