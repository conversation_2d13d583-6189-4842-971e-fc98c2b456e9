require 'rails_helper'

RSpec.describe WebApp::Admin::CirclesController, type: :controller do
  describe '#get_overview' do
    before :each do
      @user = FactoryBot.create(:user)
      @random_user = FactoryBot.create(:user)
      @circle = FactoryBot.create(:circle)
      token, = @user.generate_web_app_jwt_token
      request.headers['Authorization'] = "Bearer #{token}"
      allow($redis).to receive(:sismember).and_return(true)
    end
    context 'should return not found' do
      it 'when circle is not found' do
        get :overview
        expect(response).to have_http_status(:bad_request)
      end
    end
    context 'should return bad request' do
      it 'when user does not have owner or admin permission' do
        @permission_group = FactoryBot.build(:permission_group, name: 'creator')
        @permission_group.save(validate: false)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)

        request.headers['X-Circle-HashId'] = @circle.hashid
        get :overview
        expect(response).to have_http_status(:forbidden)
      end
    end
    context 'should return success' do
      it 'when user has owner permission' do
        @circle = FactoryBot.create(:circle)
        @permission_group = PermissionGroup.find(Constants.owner_permission_group_id)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)

        @hashtag = FactoryBot.create(:hashtag)
        allow(Hashtag).to receive(:get_trending_hashtags).and_return([{ "id" => @hashtag.id, "name" => @hashtag.name, "active" => @hashtag.active }])
        allow_any_instance_of(Post).to receive(:get_total_views_count).and_return(10)

        @post = FactoryBot.create(:post, user: @random_user)
        FactoryBot.create(:post_hashtag, post: @post, hashtag: @hashtag)
        FactoryBot.create(:post_circle, post: @post, circle: @circle)

        @user_post = FactoryBot.create(:post, user: @user)
        FactoryBot.create(:post_circle, post: @user_post, circle: @circle)
        FactoryBot.create(:user_circle, circle: @circle)

        @post_1 = FactoryBot.create(:post, user: @random_user, created_at: Time.zone.now - 1.day)
        FactoryBot.create(:post_hashtag, post: @post_1, hashtag: @hashtag)
        FactoryBot.create(:post_circle, post: @post_1, circle: @circle)

        @user_post_1 = FactoryBot.create(:post, user: @user, created_at: Time.zone.now - 1.day)
        FactoryBot.create(:post_hashtag, post: @user_post_1, hashtag: @hashtag)
        FactoryBot.create(:post_circle, post: @user_post_1, circle: @circle)

        @user_post_2 = FactoryBot.create(:post, user: @user, created_at: Time.zone.now - 3.day)
        FactoryBot.create(:post_hashtag, post: @user_post_2, hashtag: @hashtag)
        FactoryBot.create(:post_circle, post: @user_post_2, circle: @circle)

        request.headers['X-Circle-HashId'] = @circle.hashid
        get :overview
        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(response_body['data']).not_to be_empty
        expect(response_body['data']['todays_stats']['posts_count']).to eq(2)
        expect(response_body['data']['todays_stats']['owner_posts_count']).to eq(1)
        expect(response_body['data']['todays_stats']['owner_posts_views_count']).to eq(10)

        # TODO: Should use time freeze to create older entries
        expect(response_body['data']['last_7_days_stats']['posts_count']).to eq(3)
        expect(response_body['data']['last_7_days_stats']['owner_posts_count']).to eq(2)
        expect(response_body['data']['last_7_days_stats']['owner_posts_views_count']).to eq(20)
      end
    end
  end

  describe '#update_circle' do
    before :each do
      @user = FactoryBot.create(:user)
      User.find_by_phone(Constants.praja_account_phone)
      @circle = FactoryBot.create(:circle)
      token, = @user.generate_web_app_jwt_token
      @permission_group = PermissionGroup.find(Constants.owner_permission_group_id)
      FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)

      request.headers['Authorization'] = "Bearer #{token}"
      request.headers['X-Circle-HashId'] = @circle.hashid
      allow($redis).to receive(:sismember).and_return(true)
    end

    context 'should return success' do
      it 'when photo is uploaded' do
        photo = fixture_file_upload("app/assets/images/logo.png", "image/png")
        @photo = FactoryBot.create(:photo)
        allow(Photo).to receive(:upload).with(instance_of(ActionDispatch::Http::UploadedFile), Constants.praja_account_user_id).and_return(@photo)
        put :update_circle, params: { photo: photo }
        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(response_body['photo']['id']).to eq(@photo.id)
      end
    end

    context 'should return bad request' do
      it 'when photo is not uploaded succesfully' do
        photo = fixture_file_upload("app/assets/images/logo.png", "image/png")
        @photo = FactoryBot.create(:photo)
        allow(Photo).to receive(:upload).with(instance_of(ActionDispatch::Http::UploadedFile), Constants.praja_account_user_id).and_return(nil)
        put :update_circle, params: { photo: photo }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body['message']).to eq('ఫోటో అప్డేట్ చెయ్యడం సాధ్యం కాలేదు, కాసేపు ఆగి ప్రయత్నించండి.')
      end

      it 'when circle is not saved' do
        photo = fixture_file_upload("app/assets/images/logo.png", "image/png")
        @photo = FactoryBot.create(:photo)
        allow(Photo).to receive(:upload).with(instance_of(ActionDispatch::Http::UploadedFile), Constants.praja_account_user_id).and_return(@photo)
        allow_any_instance_of(Circle).to receive(:save).and_return(false)
        put :update_circle, params: { photo: photo }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body['message']).to eq('అప్డేట్ చెయ్యడం సాధ్యం కాలేదు, కాసేపు ఆగి ప్రయత్నించండి.')
      end
    end
  end
end
