require 'rails_helper'

RSpec.describe ToastsController, type: :controller do
  describe "#index" do
    before do
      @toast = FactoryBot.create(:toast)
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end
    context "when the toast is found" do
      it "renders a json success message" do
        get :index, params: { id: @toast.id }
        expect(response).to have_http_status(:success)
      end
    end
    context "when the toast is not found" do
      it "renders a json error message" do
        get :index
        expect(response).to have_http_status(:not_found)
        expect(response.body).to eq({ success: false, message: 'గ్రూపు కనుగొనబడలేదు' }.to_json)
      end
    end
  end

  describe "#close" do
    context "when the toast is found" do
      before do
        @toast = FactoryBot.create(:toast)
        @user = FactoryBot.create(:user)
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2302.28.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a json success message" do
        put :close, params: { toast_id: @toast.id }
        expect(response).to have_http_status(:success)
        expect(response.body).to eq({ success: true, message: 'Marked as closed!' }.to_json)
      end
    end
  end
end
