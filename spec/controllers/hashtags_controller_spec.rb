require 'rails_helper'

RSpec.describe HashtagsController, type: :controller do
  describe "#index" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end
    context "when hashtag is found" do
      it "returns http success" do
        get :index, params: { id: @hashtag.id }
        expect(response).to have_http_status(:success)

        get :index, params: { id: @hashtag.hashid }
        expect(response).to have_http_status(:success)

        get :index, params: { hashtag_id: @hashtag.hashid }
        expect(response).to have_http_status(:success)

        get :index, params: { hashtag_id: @hashtag.id }
        expect(response).to have_http_status(:success)

        get :index, params: { hashid: @hashtag.hashid }
        expect(response).to have_http_status(:success)
      end
    end
    context "when hashtag is not found" do
      it "returns http not found" do
        get :index, params: { id: nil }
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#show" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end
    it "returns http success" do
      get :show, params: { id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body)["id"]).to eq(@hashtag.id)
    end
  end

  describe "#get_public_hashtag" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
    end
    it "returns http success" do
      get :get_public_hashtag, params: { "param" => :hashid, "hashid" => @hashtag.hashid }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body)["id"]).to eq(@hashtag.id)
    end

    it "dynamic link being empty" do
      allow($firebase_dl_client).to receive(:shorten_parameters).and_return({ link: nil })
      get :get_public_hashtag, params: { "param" => :hashid, "hashid" => @hashtag.hashid }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body)["dynamic_link"]).to eq('')
    end
  end

  describe "#search" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @hashtag2 = FactoryBot.create(:hashtag, name: "MyString2", identifier: "mystring2")
      @hashtag3 = FactoryBot.create(:hashtag, name: "MyString3", identifier: "mystring3")
      @hashtag4 = FactoryBot.create(:hashtag, name: "MyString4", identifier: "mystring4")
      @hashtag5 = FactoryBot.create(:hashtag, name: "MyString5", identifier: "mystring5")
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "matched hashtags as result" do
      allow(ES_CLIENT).to receive(:search).and_return(
        {
          "hits" => {
            "hits" => [
              { "fields" => { "name" => ["MyString"], "id" => [@hashtag.id] } },
              { "fields" => { "name" => ["MyString2"], "id" => [@hashtag2.id] } },
              { "fields" => { "name" => ["MyString3"], "id" => [@hashtag3.id] } },
              { "fields" => { "name" => ["MyString4"], "id" => [@hashtag4.id] } },
              { "fields" => { "name" => ["MyString5"], "id" => [@hashtag5.id] } }
            ]
          }
        }
      )

      get :search, params: { name: "MyString" }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body).length).to eq(5)
    end
  end

  describe "#get_posts" do
    before do
      @hashtag = FactoryBot.create(:hashtag)

      @user = FactoryBot.create(:user)
      @user2 = FactoryBot.create(:user)
      @user3 = FactoryBot.create(:user)

      @post = FactoryBot.create(:post, user: @user, hashtags: [@hashtag])
      @post2 = FactoryBot.create(:post, user: @user, hashtags: [@hashtag])
      @post3 = FactoryBot.create(:post, user: @user2, hashtags: [@hashtag])
      @post4 = FactoryBot.create(:post, user: @user2, hashtags: [@hashtag])
      @post5 = FactoryBot.create(:post, user: @user2, hashtags: [@hashtag])
      @post6 = FactoryBot.create(:post, user: @user, hashtags: [@hashtag])
      @post7 = FactoryBot.create(:post, user: @user3, hashtags: [@hashtag])
      @post8 = FactoryBot.create(:post, user: @user3, hashtags: [@hashtag])
      @post9 = FactoryBot.create(:post, user: @user3, hashtags: [@hashtag])
      @post10 = FactoryBot.create(:post, user: @user3, hashtags: [@hashtag])
      @post11 = FactoryBot.create(:post, user: @user3, hashtags: [@hashtag])
    end

    it "returns http success" do
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      @post_hashtag = FactoryBot.create(:post_hashtag, post: @post, hashtag: @hashtag)
      @post_hashtag2 = FactoryBot.create(:post_hashtag, post: @post2, hashtag: @hashtag)
      @post_hashtag3 = FactoryBot.create(:post_hashtag, post: @post3, hashtag: @hashtag)
      @post_hashtag4 = FactoryBot.create(:post_hashtag, post: @post4, hashtag: @hashtag)
      @post_hashtag5 = FactoryBot.create(:post_hashtag, post: @post5, hashtag: @hashtag)
      @post_hashtag6 = FactoryBot.create(:post_hashtag, post: @post6, hashtag: @hashtag)
      @post_hashtag7 = FactoryBot.create(:post_hashtag, post: @post7, hashtag: @hashtag)
      @post_hashtag8 = FactoryBot.create(:post_hashtag, post: @post8, hashtag: @hashtag)
      @post_hashtag9 = FactoryBot.create(:post_hashtag, post: @post9, hashtag: @hashtag)
      @post_hashtag10 = FactoryBot.create(:post_hashtag, post: @post10, hashtag: @hashtag)
      @post_hashtag11 = FactoryBot.create(:post_hashtag, post: @post11, hashtag: @hashtag)

      allow(ES_CLIENT).to receive(:search).and_return(
        {
          "hits" => {
            "hits" => [
              { "fields" => { "id" => [@post.id], "user_id" => [@post.user_id], "likes_count" => [1], "comments_count" => [0], "whatsapp_count" => [4], "opinions_count" => [0] } },
              { "fields" => { "id" => [@post3.id], "user_id" => [@post3.user_id], "likes_count" => [0], "comments_count" => [0], "whatsapp_count" => [0], "opinions_count" => [0] } },
              { "fields" => { "id" => [@post7.id], "user_id" => [@post7.user_id], "likes_count" => [4], "comments_count" => [0], "whatsapp_count" => [0], "opinions_count" => [11] } },
            ]
          }
        }
      )
      get :get_posts, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body).length).to eq(10)
    end

    it "returns posts from db if es posts are less than count" do
      AppVersionSupport.new('2305.04.01')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      @post_hashtag = FactoryBot.create(:post_hashtag, post: @post, hashtag: @hashtag)
      @post_hashtag2 = FactoryBot.create(:post_hashtag, post: @post2, hashtag: @hashtag)
      @post_hashtag3 = FactoryBot.create(:post_hashtag, post: @post3, hashtag: @hashtag)
      @post_hashtag4 = FactoryBot.create(:post_hashtag, post: @post4, hashtag: @hashtag)
      @post_hashtag5 = FactoryBot.create(:post_hashtag, post: @post5, hashtag: @hashtag)
      @post_hashtag6 = FactoryBot.create(:post_hashtag, post: @post6, hashtag: @hashtag)
      @post_hashtag7 = FactoryBot.create(:post_hashtag, post: @post7, hashtag: @hashtag)
      @post_hashtag8 = FactoryBot.create(:post_hashtag, post: @post8, hashtag: @hashtag)
      @post_hashtag9 = FactoryBot.create(:post_hashtag, post: @post9, hashtag: @hashtag)
      @post_hashtag10 = FactoryBot.create(:post_hashtag, post: @post10, hashtag: @hashtag)
      @post_hashtag11 = FactoryBot.create(:post_hashtag, post: @post11, hashtag: @hashtag)

      allow(ES_CLIENT).to receive(:search).and_return(
        {
          "hits" => {
            "hits" => [
              { "fields" => { "id" => [@post.id], "user_id" => [@post.user_id], "likes_count" => [1], "comments_count" => [0], "whatsapp_count" => [4], "opinions_count" => [0] } },
              { "fields" => { "id" => [@post3.id], "user_id" => [@post3.user_id], "likes_count" => [0], "comments_count" => [0], "whatsapp_count" => [0], "opinions_count" => [0] } },
            ]
          }
        }
      )

      get :get_posts, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body).length).to eq(2)
      expect(JSON.parse(response.body)['posts'].length).to eq(10)
    end

    it "returns unique user posts from elastic search query" do
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2302.28.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      allow(ES_CLIENT).to receive(:search).and_return(
        {
          "hits" => {
            "hits" => [
              { "fields" => { "id" => [@post.id], "user_id" => [@post.user_id], "likes_count" => [1], "comments_count" => [0], "whatsapp_count" => [4], "opinions_count" => [0] } },
              { "fields" => { "id" => [@post3.id], "user_id" => [@post3.user_id], "likes_count" => [0], "comments_count" => [0], "whatsapp_count" => [0], "opinions_count" => [0] } },
              { "fields" => { "id" => [@post7.id], "user_id" => [@post7.user_id], "likes_count" => [4], "comments_count" => [0], "whatsapp_count" => [0], "opinions_count" => [11] } },
            ]
          }
        }
      )
      get :get_posts, params: { hashtag_id: @hashtag.id, count: 3 }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body).length).to eq(3)
    end
  end

  describe "#like" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "creates a like for a hashtag and return success" do
      @user_group = FactoryBot.create(:user_group, user: @user)
      @user_group_member = FactoryBot.create(:user_group_member, user_group: @user_group, user: @user)
      @user_group2 = FactoryBot.create(:user_group, user: @user)
      @user_group_member2 = FactoryBot.create(:user_group_member, user_group: @user_group2, user: @user)

      post :like, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(@hashtag.likes.count).to eq(3)
    end

    it "if fails to create like returns error" do
      allow(HashtagLike).to receive(:create!).and_raise(ActiveRecord::RecordNotUnique)

      post :like, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:unprocessable_entity)
      expect(JSON.parse(response.body)['message']).to eq("ఇంతక ముందే ట్రెండ్ చేసారు!")
      expect(@hashtag.likes.count).to eq(0)
    end
  end

  describe "#unlike" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "deletes a like for a hashtag and return success" do
      @hashtag_like = FactoryBot.create(:hashtag_like, hashtag: @hashtag, user: @user)

      post :unlike, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(@hashtag.likes.count).to eq(0)
    end

    it "if fails to delete like returns error" do
      allow(HashtagLike).to receive(:find_by!).and_raise(ActiveRecord::RecordNotFound)

      post :unlike, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:unprocessable_entity)
      expect(JSON.parse(response.body)['message']).to eq("ఇంతక ముందే ట్రెండ్ తీసేసారు!")
    end
  end

  describe "#liked_users" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
      @user2 = FactoryBot.create(:user)

      @hashtag_like = FactoryBot.create(:hashtag_like, hashtag: @hashtag, user: @user)
      @hashtag_like2 = FactoryBot.create(:hashtag_like, hashtag: @hashtag, user: @user2)
      @hashtag_like3 = FactoryBot.create(:hashtag_like, hashtag: @hashtag, user: @user)
    end

    it "returns the users who liked the hashtag latest version" do
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :liked_users, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body).length).to eq(3)
    end

    it "returns the users who liked the hashtag old version" do
      AppVersionSupport.new('1.15.2')
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '1.15.2'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :liked_users, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body).length).to eq(3)
    end
  end

  describe "#share_to_whatsapp" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "creates a whatsapp share for a hashtag and return success" do
      get :share_to_whatsapp, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe "#get_share_text" do
    before do
      @hashtag = FactoryBot.create(:hashtag)
      @user = FactoryBot.create(:user)
    end

    it "returns the share text for a hashtag for version > 1.11.3" do
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.04.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_share_text, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
    end

    it "returns the share text for a hashtag for version < 1.11.3" do
      AppVersionSupport.new('1.11.2')
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '1.11.2'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_share_text, params: { hashtag_id: @hashtag.id }
      expect(response).to have_http_status(:success)
    end
  end
end
