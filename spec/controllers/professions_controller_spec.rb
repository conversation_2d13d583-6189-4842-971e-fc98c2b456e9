require 'rails_helper'

RSpec.describe ProfessionsController, type: :controller do
  describe 'GET #index' do
    context 'when user token is not present' do
      it 'returns unauthorized' do
        get :index

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when user token is present' do
      it 'returns all professions' do
        user = create(:user)
        request.headers['Authorization'] = "Bearer #{user.generate_jwt_token}"

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        political_leader = FactoryBot.create(:profession, name: 'Political Leader', name_en: 'Political Leader', admin_medium: admin_medium, ordinal: 1)
        press = FactoryBot.create(:profession, name: 'Press', name_en: 'Press', admin_medium: admin_medium)
        sub_profession_1 = FactoryBot.create(:sub_profession, name: 'Elected Official', name_en: 'Elected Official', profession: political_leader)
        sub_profession_2 = FactoryBot.create(:sub_profession, name: 'Party Leader', name_en: 'Party Leader', profession: political_leader)

        get :index

        expect(response).to have_http_status(:ok)
        items = JSON.parse(response.body)
        expect(items.count).to eq(2)
        expect(items[0]['display_name']).to eq('Press')
        expect(items[0]['sub_professions'].count).to eq(0)
        expect(items[1]['display_name']).to eq('Political Leader')
        expect(items[1]['sub_professions'].count).to eq(2)
        expect(items[1]['sub_professions'][0]['display_name']).to eq('Elected Official')
        expect(items[1]['sub_professions'][1]['display_name']).to eq('Party Leader')
      end
    end
  end

  describe 'POST #submit' do
    before(:each) do
      @user = create(:user)
      request.headers['Authorization'] = "Bearer #{@user.generate_jwt_token}"
    end

    context 'when profession id does not exist' do
      it 'returns unprocessable entity' do
        post :submit, params: { profession_id: 1 }

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'when sub profession id does not exist' do
      it 'returns unprocessable entity' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession = create(:profession, admin_medium: admin_medium)

        post :submit, params: { profession_id: profession.id, sub_profession_id: 1 }

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'when sub profession id is not present for profession with sub professions' do
      it 'returns bad request' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession = create(:profession, admin_medium: admin_medium)
        create(:sub_profession, profession: profession)

        post :submit, params: { profession_id: profession.id }

        expect(response).to have_http_status(:bad_request)
      end
    end

    context 'when sub profession does not belong to profession' do
      it 'returns bad request' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession_1 = create(:profession, admin_medium: admin_medium)
        profession_2 = create(:profession, admin_medium: admin_medium)
        sub_profession = create(:sub_profession, profession: profession_2)

        post :submit, params: { profession_id: profession_1.id, sub_profession_id: sub_profession.id }

        expect(response).to have_http_status(:bad_request)
      end
    end

    context 'when profession is submitted' do
      it 'returns ok' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession = create(:profession, admin_medium: admin_medium)

        post :submit, params: { profession_id: profession.id }

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when profession with sub profession is submitted' do
      it 'returns ok' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession = create(:profession, admin_medium: admin_medium)
        sub_profession = create(:sub_profession, profession: profession)

        post :submit, params: { profession_id: profession.id, sub_profession_id: sub_profession.id }

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when profession is submitted twice' do
      it 'returns ok' do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        profession_1 = create(:profession, admin_medium: admin_medium)
        profession_2 = create(:profession, admin_medium: admin_medium)

        post :submit, params: { profession_id: profession_1.id }
        post :submit, params: { profession_id: profession_2.id }

        expect(UserProfession.find_by(user: @user).profession_id).to eq(profession_2.id)

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when user selects a profession' do
      before(:each) do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @profession_1 = create(:profession, admin_medium: admin_medium)
        @profession_2 = create(:profession, admin_medium: admin_medium)

        allow(Constants).to receive(:leader_profession_ids).and_return([@profession_1.id])
      end

      it 'returns next_page_deeplink if leader profession is selected' do
        post :submit, params: { profession_id: @profession_1.id }

        expect(JSON.parse(response.body)).to include("next_page_deeplink" => "/party-suggestion")
      end

      it 'returns nil for next_page_deeplink if user has already joined a party circle' do
        circle = FactoryBot.create(:circle, level: :political_party, circle_type: :interest)
        UserCircle.create(user: @user, circle: circle)

        post :submit, params: { profession_id: @profession_1.id }
        expect(JSON.parse(response.body)).to include("next_page_deeplink" => nil)
      end

      it 'returns nil for next_page_deeplink if non leader profession is selected' do
        post :submit, params: { profession_id: @profession_2.id }
        expect(JSON.parse(response.body)).to include("next_page_deeplink" => nil)
      end
    end

    context 'when user selects a leader profession' do

      before(:each) do
        allow_any_instance_of(User).to receive(:verify_eligibility_rules_for_premium_pitch?).and_return(true)
        allow_any_instance_of(User).to receive(:leader_profession?).and_return(true)
        allow_any_instance_of(User).to receive(:high_end_device_user?).and_return(false)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)

        @profession = FactoryBot.create(:profession, name: 'Political Leader', name_en: 'Political Leader',
                                        admin_medium: admin_medium, ordinal: 1)
        @sub_profession = create(:sub_profession, profession: @profession, name: 'Party Leader',
                                 name_en: 'Party Leader')
        FactoryBot.create(:user_profession, user: @user, profession: @profession)
      end

      it 'creates a premium pitch for the leader profession user' do
        post :submit, params: { profession_id: @profession.id, sub_profession_id: @sub_profession.id }

        expect(response).to have_http_status(:ok)
        expect(ProfessionSelectionLead.jobs.first['args']).to eq([@user.id, "LEADER_PROFESSION"])
      end
    end

    context "when user has a high-end device but not a leader profession" do
      before(:each) do
        allow_any_instance_of(User).to receive(:verify_eligibility_rules_for_premium_pitch?).and_return(true)
        allow_any_instance_of(User).to receive(:leader_profession?).and_return(false)
        allow_any_instance_of(User).to receive(:high_end_device_user?).and_return(true)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)

        @profession = FactoryBot.create(:profession, name: 'Political Leader', name_en: 'Political Leader',
                                        admin_medium: admin_medium, ordinal: 1)
        @sub_profession = create(:sub_profession, profession: @profession, name: 'Party Leader',
                                 name_en: 'Party Leader')
        FactoryBot.create(:user_profession, user: @user, profession: @profession)
      end

      it 'creates a premium pitch for the high-end device user' do
        post :submit, params: { profession_id: @profession.id, sub_profession_id: @sub_profession.id }

        expect(response).to have_http_status(:ok)
        expect(ProfessionSelectionLead.jobs.first['args']).to eq([@user.id, "HIGH_END_DEVICE_NON_LEADER"])
      end
    end

    context "when user has a both leader profession and high-end device too" do
      before(:each) do
        allow_any_instance_of(User).to receive(:verify_eligibility_rules_for_premium_pitch?).and_return(true)
        allow_any_instance_of(User).to receive(:leader_profession?).and_return(true)
        allow_any_instance_of(User).to receive(:high_end_device_user?).and_return(true)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: image_630x940)

        @profession = FactoryBot.create(:profession, name: 'Political Leader', name_en: 'Political Leader',
                                        admin_medium: admin_medium, ordinal: 1)
        @sub_profession = create(:sub_profession, profession: @profession, name: 'Party Leader',
                                 name_en: 'Party Leader')
        FactoryBot.create(:user_profession, user: @user, profession: @profession)
      end

      it 'creates a premium pitch for the leader profession user' do
        post :submit, params: { profession_id: @profession.id, sub_profession_id: @sub_profession.id }

        expect(response).to have_http_status(:ok)
        expect(ProfessionSelectionLead.jobs.first['args']).to eq([@user.id, "LEADER_PROFESSION"])
      end
    end
  end

  describe 'POST #skip' do
    context 'when the user is high end device user' do
      before :each do
        @user = create(:user)
        request.headers['Authorization'] = "Bearer #{@user.generate_jwt_token}"
        allow_any_instance_of(User).to receive(:high_end_device_user?).and_return(true)
      end

      it 'when the user is high end device user' do
        post :skip, params: { user_id: @user.id }
        expect(response).to have_http_status(:ok)
        expect(ProfessionSelectionLead.jobs.first['args']).to eq([@user.id, "HIGH_END_DEVICE"])
      end
    end

    context 'when the user is not a high end device user' do
      before :each do
        @user = create(:user)
        request.headers['Authorization'] = "Bearer #{@user.generate_jwt_token}"
        allow_any_instance_of(User).to receive(:high_end_device_user?).and_return(false)
      end

      it 'when the user is not a high end device user' do
        post :skip, params: { user_id: @user.id }
        expect(response).to have_http_status(:ok)
        expect(ProfessionSelectionLead.jobs.size).to eq(0)
      end
    end
  end
end
