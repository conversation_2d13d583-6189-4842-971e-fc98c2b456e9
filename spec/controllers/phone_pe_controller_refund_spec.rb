require 'rails_helper'

RSpec.describe PhonePeController, type: :controller do
  describe '#subscription_callback for refund events' do
    let(:user) { create(:user) }
    let(:plan) { create(:plan) }
    let(:subscription) { create(:subscription, user: user, plan: plan, payment_gateway: :phonepe, pg_id: 'sub_123') }
    let(:subscription_charge) { create(:subscription_charge, subscription: subscription, user: user, amount: 299, status: :success, pg_id: 'charge_123') }
    let(:refund) { create(:subscription_charge_refund, subscription_charge: subscription_charge, user: user, amount: 100, status: :initiated) }

    before do
      allow(Rails.logger).to receive(:warn)
      allow(Rails.logger).to receive(:error)
      allow(Honeybadger).to receive(:notify)
    end

    context 'when refund.completed event is received' do
      let(:refund_completed_params) do
        {
          event: 'refund.completed',
          payload: {
            merchantRefundId: "refund-#{refund.id}-#{subscription_charge.pg_id}",
            originalMerchantOrderId: subscription_charge.pg_id,
            state: 'COMPLETED',
            refundId: 'OMR12345',
            amount: 10000 # 100 rupees in paise
          }
        }
      end

      it 'marks the refund as successful' do
        expect(refund.status).to eq('initiated')

        post :subscription_callback, params: refund_completed_params

        refund.reload
        expect(refund.status).to eq('success')
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ 'status' => 'OK' })
      end

      it 'updates the refund pg_json with callback payload' do
        post :subscription_callback, params: refund_completed_params

        refund.reload
        expect(refund.pg_json).to include(
          'merchantRefundId' => "refund-#{refund.id}-#{subscription_charge.pg_id}",
          'state' => 'COMPLETED',
          'refundId' => 'OMR12345'
        )
      end

      it 'logs the successful refund completion' do
        expect(Rails.logger).to receive(:warn).with(/PhonePe refund callback received/)
        expect(Rails.logger).to receive(:warn).with(/PhonePe refund marked as success for refund ID: #{refund.id}/)

        post :subscription_callback, params: refund_completed_params
      end

      context 'when refund is not in initiated state' do
        before do
          refund.update(status: :success)
        end

        it 'notifies Honeybadger about invalid state' do
          expect(Honeybadger).to receive(:notify).with(
            'Invalid state for refund in PhonePe refund completed',
            hash_including(context: hash_including(refund_id: refund.id, current_state: 'success'))
          )

          post :subscription_callback, params: refund_completed_params
        end
      end
    end

    context 'when refund.failed event is received' do
      let(:refund_failed_params) do
        {
          event: 'refund.failed',
          payload: {
            merchantRefundId: "refund-#{refund.id}-#{subscription_charge.pg_id}",
            originalMerchantOrderId: subscription_charge.pg_id,
            state: 'FAILED',
            refundId: 'OMR12345',
            amount: 10000 # 100 rupees in paise
          }
        }
      end

      it 'marks the refund as failed' do
        expect(refund.status).to eq('initiated')

        post :subscription_callback, params: refund_failed_params

        refund.reload
        expect(refund.status).to eq('failed')
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ 'status' => 'OK' })
      end

      it 'logs the failed refund' do
        expect(Rails.logger).to receive(:warn).with(/PhonePe refund callback received/)
        expect(Rails.logger).to receive(:warn).with(/PhonePe refund marked as failed for refund ID: #{refund.id}/)

        post :subscription_callback, params: refund_failed_params
      end
    end

    context 'when refund is not found' do
      let(:invalid_refund_params) do
        {
          event: 'refund.completed',
          payload: {
            merchantRefundId: "refund-99999-#{subscription_charge.pg_id}",
            originalMerchantOrderId: subscription_charge.pg_id,
            state: 'COMPLETED'
          }
        }
      end

      it 'logs error and notifies Honeybadger' do
        expect(Rails.logger).to receive(:error).with(/PhonePe refund not found for refund_id: 99999/)
        expect(Honeybadger).to receive(:notify).with(
          'PhonePe refund not found for callback',
          hash_including(context: hash_including(refund_id: '99999'))
        )

        post :subscription_callback, params: invalid_refund_params

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when merchant_refund_id format is invalid' do
      let(:invalid_format_params) do
        {
          event: 'refund.completed',
          payload: {
            merchantRefundId: "invalid-format",
            originalMerchantOrderId: subscription_charge.pg_id,
            state: 'COMPLETED'
          }
        }
      end

      it 'logs error and notifies Honeybadger' do
        expect(Rails.logger).to receive(:error).with(/Invalid merchant_refund_id format: invalid-format/)
        expect(Honeybadger).to receive(:notify).with(
          'Invalid merchant_refund_id in PhonePe refund callback',
          hash_including(context: hash_including(merchant_refund_id: 'invalid-format'))
        )

        post :subscription_callback, params: invalid_format_params

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when merchant_refund_id is missing' do
      let(:missing_refund_id_params) do
        {
          event: 'refund.completed',
          payload: {
            originalMerchantOrderId: subscription_charge.pg_id,
            state: 'COMPLETED'
          }
        }
      end

      it 'logs error and notifies Honeybadger' do
        expect(Rails.logger).to receive(:error).with(/Invalid merchant_refund_id format:/)
        expect(Honeybadger).to receive(:notify).with(
          'Invalid merchant_refund_id in PhonePe refund callback',
          hash_including(context: hash_including(merchant_refund_id: nil))
        )

        post :subscription_callback, params: missing_refund_id_params

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when unhandled refund state is received' do
      let(:unhandled_state_params) do
        {
          event: 'refund.completed',
          payload: {
            merchantRefundId: "refund-#{refund.id}-#{subscription_charge.pg_id}",
            originalMerchantOrderId: subscription_charge.pg_id,
            state: 'UNKNOWN_STATE'
          }
        }
      end

      it 'logs warning about unhandled state' do
        expect(Rails.logger).to receive(:warn).with(/Unhandled PhonePe refund state: UNKNOWN_STATE for event: refund.completed/)

        post :subscription_callback, params: unhandled_state_params

        expect(response).to have_http_status(:ok)
      end
    end
  end
end
