require 'rails_helper'
# Sidekiq::Testing.fake!

RSpec.describe Floww<PERSON>ontroller, type: :controller do
  describe "POST #send_to_mixpanel" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = Base64.encode64("#{Rails.application.credentials[:floww][:auth_username]}:#{Rails.application.credentials[:floww][:auth_secret]}")
      allow(EventTracker).to receive(:perform_async)
    end
    context "with valid params" do
      it "returns a success response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :send_to_mixpanel, params: { user_id: @user.id, event_name: 'event_name', floww: { user_id: @user.id, event_name: 'event_name', key: 'value' } }
        expect(response).to have_http_status(:ok)
        expect(EventTracker).to have_received(:perform_async).with(@user.id, 'event_name', { "key" => 'value' })
      end
    end

    context "with invalid params" do
      it "returns a bad_request response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :send_to_mixpanel
        expect(response).to have_http_status(:bad_request)
        expect(EventTracker).to_not receive(:perform_async)
      end
    end
  end

  describe "POST #rm_assigned" do
    before do
      @user = FactoryBot.create(:user)
      @valid_rm_id = "fjwh-23786-239sd-23"
      @invalid_rm_id = "3789983239"
      @rm_user = FactoryBot.create(:admin_user, floww_user_id: @valid_rm_id)
      @token = Base64.encode64("#{Rails.application.credentials[:floww][:auth_username]}:#{Rails.application.credentials[:floww][:auth_secret]}")
    end

    context "when rm_user is not found" do
      it "should return not found status" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :rm_assigned, params: { rm_id: @invalid_rm_id, user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['error']).to eq("AdminUser with #{@invalid_rm_id} not found")
      end
    end

    context "when rm_user is successfully assigned" do
      it "should return success status and updates the user" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :rm_assigned, params: { rm_id: @valid_rm_id, user_id: @user.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ 'success' => true })
        @user.reload
      end
    end
  end

  describe "POST #layout_setup_callback" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = Base64.encode64("#{Rails.application.credentials[:floww][:auth_username]}:#{Rails.application.credentials[:floww][:auth_secret]}")
      allow(Floww::AssignOe).to receive(:perform_async)
    end

    context "with valid params" do
      it "returns a success response and calls AssignOe worker" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :layout_setup_callback, params: { user_id: @user.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
        expect(Floww::AssignOe).to have_received(:perform_async).with(@user.id)
      end
    end

    context "with missing user_id param" do
      it "returns a bad_request response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :layout_setup_callback
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('User ID not present')
        expect(Floww::AssignOe).not_to have_received(:perform_async)
      end
    end

    context "when user is not found" do
      it "returns a not_found response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :layout_setup_callback, params: { user_id: -1 }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['message']).to eq('User not found')
        expect(Floww::AssignOe).not_to have_received(:perform_async)
      end
    end
  end

  describe "POST #stage_updated" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = Base64.encode64("#{Rails.application.credentials[:floww][:auth_username]}:#{Rails.application.credentials[:floww][:auth_secret]}")
      allow(EventTracker).to receive(:perform_async)
    end

    context "with valid params" do
      it "returns a success response and updates the user stage" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :stage_updated, params: { user_id: @user.id, to_stage: :LEADER_POSTER_SHARE,
                                       floww: { user_id: @user.id, event_name: 'event_name', key: 'value' } }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
        expect(EventTracker).to have_received(:perform_async).with(@user.id,
                                                                   "crm_new_stage_update_backend",
                                                                   { "key" => 'value' })
      end
    end

    context "with missing to_stage param" do
      it "returns a bad_request response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :stage_updated, params: { user_id: @user.id, floww: { user_id: @user.id, event_name: 'event_name',
                                                                   key: 'value' } }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('To stage not present')
        expect(EventTracker).to_not receive(:perform_async)
      end
    end

    context "with missing user_id param" do
      it "returns a bad_request response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :stage_updated, params: { to_stage: 'new_stage', floww: { user_id: @user.id, event_name: 'event_name',
                                                                       key: 'value' } }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('User ID not present')
        expect(EventTracker).to_not receive(:perform_async)
      end
    end

    context "when user is not found" do
      it "returns a not_found response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :stage_updated, params: { user_id: -1, to_stage: 'new_stage', floww: { user_id: @user.id,
                                                                                    event_name: 'event_name',
                                                                                    key: 'value' } }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['message']).to eq('User not found')
        expect(EventTracker).to_not receive(:perform_async)
      end
    end
  end
end
