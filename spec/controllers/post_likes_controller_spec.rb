require 'rails_helper'

RSpec.describe PostLikesController, type: :controller do

  describe 'Create Post like' do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('1.17.3')
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "1.17.3"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end
    it 'should create a post like' do
      put :like, params: { post_id: @post.id }
      expect(response).to have_http_status(:success)
      expect(response.body).to eq({ success: true }.to_json)
      expect(PostLike.where(post: @post, user: @user).exists?).to eq(true)
    end

    it 'should not create a post like if already liked' do

      put :like, params: { post_id: @post.id }
      put :like, params: { post_id: @post.id }
      expect(PostLike.where(post: @post, user: @user).count).to eq(1)
      expect(response).to have_http_status(:unprocessable_entity)
      expect(response.body).to eq({ success: false, message: "ట్రెండ్ చేయలేకపోయాము!" }.to_json)
    end

    it  "throw active record not unique error" do
      allow(PostLike).to receive(:create!).and_raise(ActiveRecord::RecordNotUnique)
      put :like, params: { post_id: @post.id }
      expect(response).to have_http_status(:ok)
      expect(response.body).to eq({ success: true }.to_json)
    end
  end

  describe 'Delete Post like' do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('1.17.3')
      @token = @user.generate_login_token(nil, nil)
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "1.17.3"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it 'should delete a post like' do
      # to like the post
      put :like, params: { post_id: @post.id }
      # to unlike the post
      put :unlike, params: { post_id: @post.id }

      expect(response).to have_http_status(:success)
      expect(response.body).to eq({ success: true }.to_json)
      expect(PostLike.where(post: @post, user: @user).exists?).to eq(false)
      expect($redis.sismember("user_like_for_post_#{@post.id}", @user.id.to_s)).to eq(false)
    end

    it 'should not delete a post like if already unliked' do
      allow(PostLike).to receive(:where).and_raise(StandardError.new("Something went wrong"))

      put :unlike, params: { post_id: @post.id }
      
      expect(response).to have_http_status(:unprocessable_entity)
      expect(response.body).to eq({ success: false, message: "ఇంతక ముందే ట్రెండ్ తీసేసారు!" }.to_json)
    end
  end
end
