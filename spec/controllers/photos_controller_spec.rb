require 'rails_helper'

RSpec.describe PhotosController, type: :controller do
  describe 'PhotosController' do
    context 'upload photo' do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it 'with valid data should upload photo' do
        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        post :upload, params: { data: image }, format: :json
        expect(response).to have_http_status(:ok)
      end

      it 'with invalid data should not upload photo' do
        # stubbing the upload method to return nil as sending data nil is returning errors
        allow(Photo).to receive(:upload).and_return(nil)

        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        post :upload, params: { data: image }, format: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
  describe '#photo_params' do
    it 'should permit data' do
      permitted_params = [:data]
      # Simulate a request with additional, non-permitted parameters
      params = ActionController::Parameters.new(
        photo: {
          data: 'data',
          extra_param: 'Extra value'
        }
      )

      # Assign params to the controller
      controller.params = params

      filtered_params = controller.send(:photo_params)
      # Check that only permitted parameters are present
      expect(filtered_params.keys).to contain_exactly(*permitted_params.map(&:to_s))
      
    end
  end
end
