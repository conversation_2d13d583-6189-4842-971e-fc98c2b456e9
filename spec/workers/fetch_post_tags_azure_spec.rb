require 'rails_helper'
require 'webmock/rspec'

RSpec.describe FetchPostTagsAzure, type: :worker do
  describe "#perform" do
    context "when post_id is blank" do
      it "returns nil" do
        expect(FetchPostTagsAzure.new.perform(nil)).to eq(nil)
      end
    end

    context "when post_id is not blank" do
      before :each do
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        @tag = FactoryBot.create(:tag, id: 1, identifier: "poster", tag_type: :post)
      end

      it "creates tagging for the post with approved status and creates metadatum with poster photo key" do
        array =
          [
            {
              "text"=>"Praja App",
              "boundingPolygon"=>[{"x"=>43, "y"=>212}, {"x"=>206, "y"=>56}, {"x"=>232, "y"=>83}, {"x"=>67, "y"=>239}],
              "words"=>
                [
                  {"text"=>"Praja", "boundingPolygon"=>[{"x"=>45, "y"=>209}, {"x"=>125, "y"=>134}, {"x"=>148, "y"=>162}, {"x"=>68, "y"=>237}], "confidence"=>0.995},
                  {"text"=>"App", "boundingPolygon"=>[{"x"=>143, "y"=>117}, {"x"=>198, "y"=>64}, {"x"=>224, "y"=>92}, {"x"=>166, "y"=>145}], "confidence"=>0.993}
                ]
            },
            {
              "text"=>"Top",
              "boundingPolygon"=>[{"x"=>194, "y"=>528}, {"x"=>866, "y"=>535}, {"x"=>864, "y"=>644}, {"x"=>192, "y"=>643}],
              "words"=>
                [
                  {"text"=>"Top", "boundingPolygon"=>[{"x"=>194, "y"=>529}, {"x"=>228, "y"=>529}, {"x"=>227, "y"=>644}, {"x"=>193, "y"=>644}], "confidence"=>0.624}
                ]
            }
          ]

        response_body = {
          "readResult"=> {
            "blocks"=> [
              "lines"=> array
            ]
          }
        }

        stub_request(:post,
                     'https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze?api-version=2023-10-01&features=read')
          .to_return(status: 200, body: response_body.to_json, headers: {})

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)

        FetchPostTagsAzure.new.perform(@post.id)

        expect(@post.taggings.count).to eq(1)
        expect(@post.taggings.first.status.to_sym).to eq(:approved)
        expect(@post.metadatum.count).to eq(1)
        expect(@post.metadatum.first.key).to eq(Constants.get_is_poster_photo_key_for_post)
        expect(@post.metadatum.first.value).to eq("true")
      end

      it "does not create tagging for the post if tag is not present in cloud_vision_tags" do
        stub_request(:post,
                     'https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze?api-version=2023-10-01&features=read')
          .to_return(status: 200, body: {}.to_json, headers: {})

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)

        FetchPostTagsAzure.new.perform(@post.id)

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)
      end

      it "create tagging and metadatum for the post if photo is acceptable format" do
        @post.post_photos.first.photo.url = "https://cdn.thecircleapp.in/1.webp"
        @post.post_photos.first.photo.save!
        array =
          [
            {
              "text"=>"Praja App",
              "boundingPolygon"=>[{"x"=>43, "y"=>212}, {"x"=>206, "y"=>56}, {"x"=>232, "y"=>83}, {"x"=>67, "y"=>239}],
              "words"=>
                [
                  {"text"=>"Praja", "boundingPolygon"=>[{"x"=>45, "y"=>209}, {"x"=>125, "y"=>134}, {"x"=>148, "y"=>162}, {"x"=>68, "y"=>237}], "confidence"=>0.995},
                  {"text"=>"App", "boundingPolygon"=>[{"x"=>143, "y"=>117}, {"x"=>198, "y"=>64}, {"x"=>224, "y"=>92}, {"x"=>166, "y"=>145}], "confidence"=>0.993}
                ]
            },
            {
              "text"=>"Top",
              "boundingPolygon"=>[{"x"=>194, "y"=>528}, {"x"=>866, "y"=>535}, {"x"=>864, "y"=>644}, {"x"=>192, "y"=>643}],
              "words"=>
                [
                  {"text"=>"Top", "boundingPolygon"=>[{"x"=>194, "y"=>529}, {"x"=>228, "y"=>529}, {"x"=>227, "y"=>644}, {"x"=>193, "y"=>644}], "confidence"=>0.624}
                ]
            }
          ]

        response_body = {
          "readResult"=> {
            "blocks"=> [
              "lines"=> array
            ]
          }
        }

        stub_request(:post,
                     'https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze?api-version=2023-10-01&features=read')
          .to_return(status: 200, body: response_body.to_json, headers: {})

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)

        FetchPostTagsAzure.new.perform(@post.id)

        expect(@post.taggings.count).to eq(1)
        expect(@post.metadatum.count).to eq(1)
      end

      it "does not create tagging and metadatum for the post if text is not praja app" do
        array =
          [
            {
              "text"=>"Top",
              "boundingPolygon"=>[{"x"=>194, "y"=>528}, {"x"=>866, "y"=>535}, {"x"=>864, "y"=>644}, {"x"=>192, "y"=>643}],
              "words"=>
                [
                  {"text"=>"Top", "boundingPolygon"=>[{"x"=>194, "y"=>529}, {"x"=>228, "y"=>529}, {"x"=>227, "y"=>644}, {"x"=>193, "y"=>644}], "confidence"=>0.624}
                ]
            }
          ]

        response_body = {
          "readResult"=> {
            "blocks"=> [
              "lines"=> array
            ]
          }
        }
        stub_request(:post,
                     'https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze?api-version=2023-10-01&features=read')
          .to_return(status: 200, body: response_body.to_json, headers: {})

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)

        FetchPostTagsAzure.new.perform(@post.id)

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)
      end

      it "raises Honeybadger error if error occurs while creating tagging or metadatum" do
        array =
          [
            {
              "text"=>"Praja App",
              "boundingPolygon"=>[{"x"=>43, "y"=>212}, {"x"=>206, "y"=>56}, {"x"=>232, "y"=>83}, {"x"=>67, "y"=>239}],
              "words"=>
                [
                  {"text"=>"Praja", "boundingPolygon"=>[{"x"=>45, "y"=>209}, {"x"=>125, "y"=>134}, {"x"=>148, "y"=>162}, {"x"=>68, "y"=>237}], "confidence"=>0.995},
                  {"text"=>"App", "boundingPolygon"=>[{"x"=>143, "y"=>117}, {"x"=>198, "y"=>64}, {"x"=>224, "y"=>92}, {"x"=>166, "y"=>145}], "confidence"=>0.993}
                ]
            },
            {
              "text"=>"Top",
              "boundingPolygon"=>[{"x"=>194, "y"=>528}, {"x"=>866, "y"=>535}, {"x"=>864, "y"=>644}, {"x"=>192, "y"=>643}],
              "words"=>
                [
                  {"text"=>"Top", "boundingPolygon"=>[{"x"=>194, "y"=>529}, {"x"=>228, "y"=>529}, {"x"=>227, "y"=>644}, {"x"=>193, "y"=>644}], "confidence"=>0.624}
                ]
            }
          ]

        response_body = {
          "readResult"=> {
            "blocks"=> [
              "lines"=> array
            ]
          }
        }

        stub_request(:post,
                     'https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze?api-version=2023-10-01&features=read')
          .to_return(status: 200, body: response_body.to_json, headers: {})

        allow_any_instance_of(Post).to receive(:save!).and_raise(StandardError)
        allow(Honeybadger).to receive(:notify)
        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)

        FetchPostTagsAzure.new.perform(@post.id)

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)
        expect(Honeybadger).to have_received(:notify)
      end

      it 'raises Honeybadger error if error occurs while fetching tags' do
        stub_request(:post,
                     'https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze?api-version=2023-10-01&features=read')
          .and_raise(StandardError)
        allow(Honeybadger).to receive(:notify)

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)

        FetchPostTagsAzure.new.perform(@post.id)

        expect(@post.taggings.count).to eq(0)
        expect(@post.metadatum.count).to eq(0)
        expect(Honeybadger).to have_received(:notify)
      end
    end
  end
end
