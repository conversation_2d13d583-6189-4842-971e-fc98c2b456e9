require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe IndexCreativesForPostersFeed, type: :worker do
  let(:event) { instance_double(Event, id: 1, priority: 'high', start_time: Time.now, end_time: Time.now + 1.day, created_at: Time.now, active: true) }
  let(:creative) { instance_double(PosterCreative, id: 1, start_time: Time.now, end_time: Time.now + 1.day, created_at: Time.now, active: true, primary: true) }
  let(:poster_feed) { instance_double(PosterFeed) }

  before do
    allow(Event).to receive(:find).and_return(event)
    allow(event).to receive_message_chain(:poster_creatives, :where, :select).and_return([creative])
    allow(event).to receive_message_chain(:event_circles, :pluck).and_return([1, 2, 3])
    allow(PosterCreative).to receive(:find).and_return(creative)
    allow(creative).to receive_message_chain(:poster_creative_circles, :pluck).and_return([1, 2, 3])
    allow(PosterCreativeView).to receive(:where).and_return(double(count: 10))
    allow(PosterShare).to receive(:where).and_return(double(count: 5))
    allow(PosterFeed).to receive(:new).and_return(poster_feed)
    allow(poster_feed).to receive(:reindex)
  end

  describe '#perform' do
    context 'when id is event_xxx' do
      it 'processes the event and indexes the creatives' do
        subject.perform('event_1')
        expect(PosterFeed).to have_received(:new).with(hash_including(id: 'event_1', primary_creative_id: 1, creative_ids: [1], circle_ids: [1, 2, 3], active: true))
        expect(poster_feed).to have_received(:reindex)
      end
    end

    context 'when id is creative_xxx' do
      it 'processes the creative and indexes it' do
        subject.perform('creative_1')
        expect(PosterFeed).to have_received(:new).with(hash_including(id: 'creative_1', primary_creative_id: 1, creative_ids: [1], circle_ids: [1, 2, 3], active: true))
        expect(poster_feed).to have_received(:reindex)
      end
    end
  end
end
