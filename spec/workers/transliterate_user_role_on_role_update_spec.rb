require 'rails_helper'

RSpec.describe TransliterateUserRoleOnRoleUpdate, type: :worker do

  context 'sidekiq options as' do
    it { is_expected.to be_processed_in :critical }
  end

  context 'calls' do
    it 'enqueues transliteration jobs for each UserRole' do

      @user = FactoryBot.create(:user)
      @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      @location_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @role = FactoryBot.create(:role, name: 'Member', has_purview: true, purview_level: :state,
                                display_name_order: "purview,role")
      @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                     purview_circle_id: @location_circle.id)

      described_class.perform_async(@role.id)

      expect(TransliterateUserRoleOnRoleUpdate.jobs.size).to eq(1)
      expect(TransliterateEntityName).to have_enqueued_sidekiq_job("UserRole", @user_role.id)
    end

    it 'does nothing if no UserRoles exist for the role' do
      @role = FactoryBot.create(:role, name: 'Member', has_purview: true, purview_level: :state,
                                display_name_order: "purview,role")

      described_class.perform_async(@role.id)

      expect(TransliterateEntityName).not_to have_enqueued_sidekiq_job
    end
    
  end

end
