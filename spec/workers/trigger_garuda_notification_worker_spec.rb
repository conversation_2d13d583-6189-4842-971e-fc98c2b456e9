# frozen_string_literal: true
require 'rails_helper'
require 'webmock/rspec'

RSpec.describe TriggerGarudaNotification, type: :worker do
  describe 'perform' do
    context 'performs in notifications queue' do
      it { is_expected.to be_processed_in :notifications }
    end

    context 'No retries for the job' do
      it { is_expected.to be_retryable 0 }
    end

    context 'when garuda_notification_id is nil' do
      it 'return nil' do
        expect(described_class.new.perform(nil)).to be_nil
      end
    end

    context 'when garuda_notification is not present' do
      it 'return nil' do
        expect(described_class.new.perform(-1)).to be_nil
      end
    end

    context 'when garuda_notification is present' do
      before :each do
        @garuda_notification = FactoryBot.create(:garuda_notification)
      end

      it 'sends a request to send notification' do
        url = URI("#{Constants.get_garuda_base_url}/users/all/dispatch")
        response = { success: true, notification_id: Nanoid.generate }.to_json

        stub_request(:post, url)
          .to_return(status: 200, body: response, headers: {})
        allow(JSON).to receive(:parse).and_return(response)

        described_class.new.perform(@garuda_notification.id)

        expect(WebMock).to have_requested(:post, url)
        expect(JSON).to have_received(:parse).with(response)
      end
    end
  end
end
