require "rails_helper"

RSpec.describe BulkUpdateUserInvitesSent, type: :worker do
  # context "perform" do
  #   it { is_expected.to be_processed_in :default }
  #
  #   let(:user) { FactoryBot.create(:user) }
  #
  #   phones_batch = []
  #   5.times do
  #     user_circle = FactoryBot.create(:user_invite)
  #     phones_batch << user_circle.phone
  #   end
  #
  #   it 'calls delete_phone_records_in_user_invites_index' do
  #     expect_any_instance_of(described_class).to receive(:delete_phone_records_in_user_invites_index)
  #     described_class.new.perform(phones_batch)
  #   end
  #
  #   it "updates user invites sent" do
  #     allow(ES_CLIENT).to receive(:method_missing).with(:perform_request, 'POST', "#{EsUtil.get_user_invite_phones_index_v2}/_delete_by_query", {}, {
  #       "query": {
  #         "terms": {
  #           "id": phones_batch
  #         }
  #       }
  #     })
  #
  #     described_class.new.perform(phones_batch)
  #
  #     expect(ES_CLIENT).to have_received(:method_missing).with(:perform_request, 'POST', "#{EsUtil.get_user_invite_phones_index_v2}/_delete_by_query", {}, {
  #       "query": {
  #         "terms": {
  #           "id": phones_batch
  #         }
  #       }
  #     })
  #
  #     expect(UserInvite.where(phone: phones_batch, sent: true).count).to eq(5)
  #   end
  # end
end
