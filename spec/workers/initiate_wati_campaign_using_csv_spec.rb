require 'rails_helper'
require 'webmock/rspec'

RSpec.describe InitiateWatiCampaignUsingCsv, type: :worker do
  describe "#perform" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:campaign_name) { "some_campaign" }
    let(:metadata_key) { "some_key" }

    before do
      allow(worker).to receive(:initialize_variables)
      allow(worker).to receive(:populate_creatives_master_data)
      allow(worker).to receive(:set_user_data)
      allow(worker).to receive(:set_event_data)
      allow(worker).to receive(:decide_and_activate_offer_per_user)
      allow(worker).to receive(:set_template)
      allow(worker).to receive(:set_cta_link)
      allow(worker).to receive(:send_bulk_api_per_template)
      allow(worker).to receive(:send_mixpanel_events)
      allow(worker).to receive(:update_trigger_counts)
      allow(worker).to receive(:import_data_in_user_wati_campaigns)
      allow(worker).to receive(:cleanup_data)
    end

    context "when users_master_data is empty" do
      before do
        allow(worker).to receive(:fetch_and_populate_umd_from_user_metadata) do
          worker.instance_variable_set(:@users_master_data, {})
        end
      end
      it "should not execute further steps and return early" do
        expect(worker).not_to receive(:populate_creatives_master_data)

        worker.perform(campaign_name, metadata_key)
      end
    end

    context "when users_master_data is present" do
      before do
        allow(worker).to receive(:fetch_and_populate_umd_from_user_metadata) do
          users_data = { 1 => "user_data_1", 2 => "user_data_2" }
          worker.instance_variable_set(:@users_master_data, users_data)
          worker.instance_variable_set(:@user_ids, users_data.keys)
        end
      end

      it "should execute all steps in order" do
        expect(worker).to receive(:fetch_and_populate_umd_from_user_metadata).ordered
        expect(worker).to receive(:populate_creatives_master_data).ordered
        expect(worker).to receive(:set_user_data).ordered
        expect(worker).to receive(:set_event_data).ordered
        expect(worker).to receive(:decide_and_activate_offer_per_user).ordered
        expect(worker).to receive(:set_template).ordered
        expect(worker).to receive(:set_cta_link).ordered
        expect(worker).to receive(:send_bulk_api_per_template).ordered
        expect(worker).to receive(:send_mixpanel_events).ordered
        expect(worker).to receive(:update_trigger_counts).ordered
        expect(worker).to receive(:import_data_in_user_wati_campaigns).ordered
        expect(worker).to receive(:cleanup_data).ordered

        worker.perform(campaign_name, metadata_key)
      end
    end
  end

  describe "#initialize_variables" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:current_time) { Time.zone.now }
    let(:campaign_name) { "wati_re_activation_campaign_#{current_time.strftime("%Y_%m_%d")}" }
    let(:metadata_key) { "fake_key" }

    before do
      allow(Time.zone).to receive(:now).and_return(current_time)
      worker.send(:initialize_variables, campaign_name, metadata_key)
    end

    context "when new variables are present" do
      it "should initialize all variables correctly" do
        expect(worker.instance_variable_get(:@current_time)).to eq(current_time)
        expected_campaign_name = campaign_name
        expect(worker.instance_variable_get(:@campaign_name)).to eq(expected_campaign_name)
        expected_key = "wati_re_activation_campaign_#{current_time.strftime("%Y_%m_%d")}_creative_id"
        expect(worker.instance_variable_get(:@creative_id_metadata_key)).to eq(expected_key)
        expect(worker.instance_variable_get(:@user_ids)).to eq([])
        expect(worker.instance_variable_get(:@users_master_data)).to eq({})
        expect(worker.instance_variable_get(:@creatives_master_data)).to eq({})
      end
    end

    context "when trail activation is triggered" do
      let(:metadata_key) { Constants.trial_activation_wati_campaign_count }
      it "should initialize related variables" do
        expect(worker.instance_variable_get(:@mixpanel_event_name)).to eq("wati_trial_activation_campaign_triggered_backend")
        expect(worker.instance_variable_get(:@template_key)).to eq("trial_activation")
        expect(worker.instance_variable_get(:@import_campaign_type)).to eq(UserWatiCampaign.campaign_types[:trial_activation])
      end
    end

    context "when premium reactivation is triggered" do
      let(:metadata_key) { Constants.premium_reactivation_wati_campaign_count }
      it "should initialize related variables" do
        expect(worker.instance_variable_get(:@mixpanel_event_name)).to eq("wati_reactivation_campaign_triggered_backend")
        expect(worker.instance_variable_get(:@template_key)).to eq("premium_reactivation")
        expect(worker.instance_variable_get(:@import_campaign_type)).to eq(UserWatiCampaign.campaign_types[:subscription_reactivation])
      end
    end
  end

  describe "#fetch_and_populate_umd_from_user_metadata" do
    let(:user) { create(:user) }
    let(:user_id) { user.id }

    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:campaign_name) { 'fake_campaign_name' }
    let(:creative_id_metadata_key) { 'fake_creative_id_key' }
    let(:poster_image_url_key) { Constants.poster_image_url(campaign_name) }
    let(:trigger_count_key) { Constants.premium_reactivation_wati_campaign_count }

    before do
      worker.instance_variable_set(:@creative_id_metadata_key, creative_id_metadata_key)
      worker.instance_variable_set(:@campaign_name, campaign_name)
      worker.instance_variable_set(:@metadata_key, trigger_count_key)
      worker.instance_variable_set(:@users_master_data, {})
    end

    context "when all required user metadata exists" do
      before do
        create(:user_metadatum, user: user, key: creative_id_metadata_key, value: 4321)
        create(:user_metadatum, user: user, key: poster_image_url_key, value: "https://example.com/image1.jpg")
        create(:user_metadatum, user: user, key: trigger_count_key, value: 2)
        worker.send(:fetch_and_populate_umd_from_user_metadata)
      end

      it "should populate users_master_data correctly" do
        expect(worker.instance_variable_get(:@users_master_data)).to eq({
                                                                          user_id => {
                                                                            user_id: user_id,
                                                                            creative_id: 4321,
                                                                            poster_image_url: "https://example.com/image1.jpg",
                                                                            trigger_count: 2
                                                                          }
                                                                        })
      end
    end

    context "when creative_id is missing" do
      before do
        create(:user_metadatum, user: user, key: poster_image_url_key, value: "https://example.com/image1.jpg")
        create(:user_metadatum, user: user, key: trigger_count_key, value: 3)
        worker.send(:fetch_and_populate_umd_from_user_metadata)
      end

      it "should remove the user from users_master_data" do
        expect(worker.instance_variable_get(:@users_master_data)).not_to have_key(user_id)
      end
    end

    context "when poster_image_url is missing" do
      before do
        create(:user_metadatum, user: user, key: creative_id_metadata_key, value: 4321)
        create(:user_metadatum, user: user, key: trigger_count_key, value: 3)
        worker.send(:fetch_and_populate_umd_from_user_metadata)
      end

      it "should remove the user from users_master_data" do
        expect(worker.instance_variable_get(:@users_master_data)).not_to have_key(user_id)
      end
    end

    context "when trigger_count is missing" do
      before do
        create(:user_metadatum, user: user, key: creative_id_metadata_key, value: 4321)
        create(:user_metadatum, user: user, key: poster_image_url_key, value: "https://example.com/image1.jpg")
        worker.send(:fetch_and_populate_umd_from_user_metadata)
      end

      it "should remove the user from users_master_data" do
        expect(worker.instance_variable_get(:@users_master_data)).not_to have_key(user_id)
      end
    end

    context "when multiple users exist" do
      let(:user2) { create(:user) }
      let(:user2_id) { user2.id }

      before do
        create(:user_metadatum, user: user, key: creative_id_metadata_key, value: 1111)
        create(:user_metadatum, user: user, key: poster_image_url_key, value: "https://example.com/image2.jpg")
        create(:user_metadatum, user: user, key: trigger_count_key, value: 5)

        create(:user_metadatum, user: user2, key: creative_id_metadata_key, value: 2222)
        create(:user_metadatum, user: user2, key: poster_image_url_key, value: "https://example.com/image3.jpg")
        create(:user_metadatum, user: user2, key: trigger_count_key, value: 7)
        worker.send(:fetch_and_populate_umd_from_user_metadata)
      end

      it "should populate data for both users correctly" do
        expect(worker.instance_variable_get(:@users_master_data)).to eq({
                                                                          user_id => {
                                                                            user_id: user_id,
                                                                            creative_id: 1111,
                                                                            poster_image_url: "https://example.com/image2.jpg",
                                                                            trigger_count: 5
                                                                          },
                                                                          user2_id => {
                                                                            user_id: user2_id,
                                                                            creative_id: 2222,
                                                                            poster_image_url: "https://example.com/image3.jpg",
                                                                            trigger_count: 7
                                                                          }
                                                                        })
      end
    end
  end

  describe "#set_user_data" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:user) { create(:user, name: "Shashi", phone: "1234567890") }
    let(:user2) { create(:user, name: "Vardhan", phone: "9808032832") }
    let(:plan) { create(:user_plan, user: user, amount: 399) }
    let(:plan2) { create(:user_plan, user: user2, amount: 299) }

    before do
      user.update(user_plan: plan)
      user2.update(user_plan: plan2)
      worker.instance_variable_set(:@user_ids, [user.id, user2.id])
      worker.instance_variable_set(:@users_master_data, Hash.new { |h, k| h[k] = {} })
      worker.send(:set_user_data)
    end

    subject { worker.instance_variable_get(:@users_master_data) }

    context "when user data is set" do
      it "should assign correct data fo each user" do
        expect(subject[user.id][:name]).to eq("Shashi")
        expect(subject[user.id][:phone]).to eq(1234567890)
        expect(subject[user.id][:amount]).to eq(399)
        expect(subject[user.id][:plan_amount]).to eq(399)

        expect(subject[user2.id][:name]).to eq("Vardhan")
        expect(subject[user2.id][:phone]).to eq(9808032832)
        expect(subject[user2.id][:amount]).to eq(299)
        expect(subject[user2.id][:plan_amount]).to eq(299)
      end
    end

    context "when user_ids list is empty" do
      before do
        worker.instance_variable_set(:@users_master_data, {})
        worker.instance_variable_set(:@user_ids, [])
        worker.send(:set_user_data)
      end

      it "should not modify users_master_data" do
        expect(worker.instance_variable_get(:@users_master_data)).to be_empty
      end
    end

    context "when there are more than 100 users" do
      let(:users) { create_list(:user, 150) }
      let(:user_ids) { users.map(&:id) }

      before do
        worker.instance_variable_set(:@user_ids, user_ids)
      end

      it "should processes users in batches of 100" do
        expect(User).to receive(:joins).twice.and_call_original
        worker.send(:set_user_data)
      end
    end
  end

  describe "#decide_and_activate_offer_per_user" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:current_time) { Time.new(2023, 10, 15, 14, 30, 0) }
    let(:expected_expire_time) { current_time.end_of_day.advance(days: 100).to_i }
    let(:users_master_data) { Hash.new { |hash, key| hash[key] = {} } }
    let(:redis) { instance_double("redis") }
    let(:redis_pipeline) { instance_double("redis_pipeline") }
    let(:user_ids) { [1, 2, 3, 4, 5] }

    before do
      worker.instance_variable_set(:@current_time, current_time)
      worker.instance_variable_set(:@user_ids, user_ids)
      worker.instance_variable_set(:@users_master_data, users_master_data)
      worker.instance_variable_set(:@redis, redis)
      allow(SubscriptionCharge).to receive(:where).and_return(SubscriptionCharge)
      allow(SubscriptionCharge).to receive(:pluck).and_return([3])
      allow(redis).to receive(:pipelined).and_yield(redis_pipeline)
      allow(redis_pipeline).to receive(:zadd)
    end

    context "when processing users in batches" do
      it "should iterate over user_ids in batches of 100" do
        expect(user_ids.each_slice(100).count).to eq(1)
        worker.send(:decide_and_activate_offer_per_user)
      end
    end

    context "when fetching users with successful 1-rupee subscriptions" do
      it "should query SubscriptionCharge with the correct conditions" do
        expect(SubscriptionCharge).to receive(:where).with(charge_amount: 1, status: :success, user_id: user_ids).and_return(SubscriptionCharge)
        expect(SubscriptionCharge).to receive(:where).with("success_at > ?", 6.months.ago.beginning_of_day).and_return(SubscriptionCharge)
        expect(SubscriptionCharge).to receive(:pluck).with(:user_id).and_return([3])
        worker.send(:decide_and_activate_offer_per_user)
      end
    end

    context "when updating users_master_data" do
      it "should set the amount to 1 for users without a successful subscription" do
        worker.send(:decide_and_activate_offer_per_user)
        expect(users_master_data[1][:amount]).to eq(1)
        expect(users_master_data[3]).to eq({})
      end
    end
  end

  describe "#populate_creatives_master_data" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:event) { create(:event, name: "Test Event", priority: "high") }
    let(:creative) { create(:poster_creative, primary: true, event: event) }
    let(:user_data) { { user: { creative_id: creative.id } } }

    before do
      allow(worker).to receive(:get_event_type).with(event).and_return("some_type")
      allow(worker).to receive(:get_deep_link_for_event_and_creative).with(event.id, creative.id).and_return("deep_link_1")

      worker.instance_variable_set(:@users_master_data, user_data)
      worker.instance_variable_set(:@creatives_master_data, {})
    end

    context "when extracting creative_ids" do
      it "should extract creative_id from users_master_data" do
        worker.send(:populate_creatives_master_data)
        creative_ids = worker.instance_variable_get(:@users_master_data).values.map { |c| c[:creative_id] }
        expect(creative_ids).to contain_exactly(creative.id)
      end
    end

    context "when fetching poster_creative records" do
      it "should retrieve the correct poster_creative with event" do
        worker.send(:populate_creatives_master_data)
        fetched_creatives = PosterCreative.includes(:event).where(id: [creative.id])
        expect(fetched_creatives).to include(creative)
      end
    end

    context "when populating creatives_master_data" do
      it "should store the correct event details for the creative" do
        worker.send(:populate_creatives_master_data)
        expect(worker.instance_variable_get(:@creatives_master_data)[creative.id]).to eq({
                                                                                           event_id: event.id,
                                                                                           event_name: event.name,
                                                                                           event_type: 'some_type',
                                                                                           deep_link: 'deep_link_1',
                                                                                           priority_of_event: 'high'
                                                                                         })
      end
    end

    context "when users_master_data is empty" do
      before do
        worker.instance_variable_set(:@users_master_data, {})
        worker.instance_variable_set(:@creatives_master_data, {})
      end

      it "should not populate @creatives_master_data" do
        worker.send(:populate_creatives_master_data)
        expect(worker.instance_variable_get(:@creatives_master_data)).to be_empty
      end
    end

    context "when creative_id does not exist" do
      before do
        worker.instance_variable_set(:@users_master_data, { user: { creative_id: 9999 } })
        worker.instance_variable_set(:@creatives_master_data, {})
      end

      it "should not add non-existent creative to creatives_master_data" do
        worker.send(:populate_creatives_master_data)
        expect(worker.instance_variable_get(:@creatives_master_data)).to be_empty
      end
    end
  end

  describe "#set_event_data" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:user) { create(:user) }
    let(:event) { create(:event, priority: "high") }
    let(:creative) { create(:poster_creative, event: event, primary: true) }
    before do
      @users_master_data = {
        user.id => { creative_id: creative.id }
      }
      @creatives_master_data = {
        creative.id => { event_id: event.id, event_name: event.name, event_type: "tomorrow" }
      }

      worker.instance_variable_set(:@users_master_data, @users_master_data)
      worker.instance_variable_set(:@creatives_master_data, @creatives_master_data)

      worker.send(:set_event_data)
    end
    context "when event data is set correctly" do
      it "should set the event_id" do
        expect(@users_master_data[user.id][:event_id]).to eq(event.id)
      end

      it "should set the event_name" do
        expect(@users_master_data[user.id][:event_name]).to eq(event.name)
      end

      it "should set the event_type" do
        expect(@users_master_data[user.id][:event_type]).to eq("tomorrow")
      end
    end

    context "when handling missing data" do
      it "should raise an error for missing creative_id in creatives_master_data" do
        @users_master_data[3] = { creative_id: 30 }

        expect { worker.set_event_data }.to raise_error(NoMethodError)
      end
    end
  end

  describe "#set_template" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:users_master_data) { { "user_1" => { event_type: 'event_1', amount: 1, template: nil } } }
    before do
      allow(worker).to receive(:get_template_name).and_return("template_name")
      worker.instance_variable_set(:@users_master_data, users_master_data)
      worker.send(:set_template)
    end

    context "when amount is 1" do
      it "should set the template for the user with amount 1" do
        expect(users_master_data["user_1"][:template]).to eq("template_name")
      end
    end

    context "when get_template_name is called" do
      it "should call get_template_name with the correct arguments" do
        expect(worker).to have_received(:get_template_name).with("event_1", true)
      end
    end
  end

  describe "#set_cta_link" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:creative_id) { 1 }
    let(:deep_link) { "https://prajaapp.sng.link/A3x5b/some-path" }
    let(:expected_link) { "some-path" }

    before do
      worker.instance_variable_set(:@creatives_master_data, {
        creative_id => { deep_link: deep_link }
      })
    end

    context "when event_type is today" do
      let(:user_data) { { event_type: "today", creative_id: creative_id } }

      before do
        worker.instance_variable_set(:@users_master_data, { user1: user_data })
        worker.send(:set_cta_link)
      end

      it "should set cta_link to the creative deep link without the base URL" do
        expect(worker.instance_variable_get(:@users_master_data)[:user1][:cta_link]).to eq(expected_link)
      end
    end

    context "when event_type is not today" do
      let(:premium_link) { "https://prajaapp.sng.link/A3x5b/premium-screen" }
      let(:expected_premium_link) { "premium-screen" }
      let(:user_data) { { event_type: "tomorrow", creative_id: creative_id } }

      before do
        allow(worker).to receive(:get_premium_experience_screen_deep_link).and_return(premium_link)
        worker.instance_variable_set(:@users_master_data, { user2: user_data })
        worker.send(:set_cta_link)
      end

      it "should set cta_link to the premium experience deep link without the base URL" do
        expect(worker.instance_variable_get(:@users_master_data)[:user2][:cta_link]).to eq(expected_premium_link)
      end
    end

    context "when deep link does not contain the base URL" do
      let(:deep_link) { "https://example.com/some-path" }
      let(:expected_link) { "https://example.com/some-path" }
      let(:user_data) { { event_type: "today", creative_id: creative_id } }

      before do
        worker.instance_variable_set(:@users_master_data, { user3: user_data })
        worker.send(:set_cta_link)
      end

      it "should not modify cta_link if base URL is not present" do
        expect(worker.instance_variable_get(:@users_master_data)[:user3][:cta_link]).to eq(expected_link)
      end
    end
  end

  describe "#organise_data_based_on_template" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    before do
      worker.instance_variable_set(:@users_master_data, users_master_data)
    end

    subject { worker.send(:organise_data_based_on_template) }
    context "when users_master_data has one user and one template" do
      let(:users_master_data) { { 1 => { template: "template_1", name: "User1" } } }

      it "should organizes data correctly" do
        expect(subject).to eq({ "template_1" => [{ template: "template_1", name: "User1" }] })
      end
    end

    context "when users_master_data has no users" do
      let(:users_master_data) { {} }

      it "returns an empty hash" do
        expect(subject).to eq({})
      end
    end

    context "when users_master_data has multiple users in the same template" do
      let(:users_master_data) {
        {
          1 => { template: "template_1", name: "User1" },
          2 => { template: "template_1", name: "User2" }
        }
      }

      it "should group users under the same template" do
        expect(subject).to eq({
                                "template_1" => [
                                  { template: "template_1", name: "User1" },
                                  { template: "template_1", name: "User2" }
                                ]
                              })
      end
    end

    context "when users_master_data has users with different templates" do
      let(:users_master_data) {
        {
          1 => { template: "template_1", name: "User1" },
          2 => { template: "template_2", name: "User2" },
          3 => { template: "template_1", name: "User3" },
          4 => { template: "template_2", name: "User4" }
        }
      }

      it "should group users based on their respective templates" do
        expect(subject).to eq({
                                'template_1' => [
                                  { template: 'template_1', name: 'User1' },
                                  { template: 'template_1', name: 'User3' }
                                ],
                                'template_2' => [
                                  { template: 'template_2', name: 'User2' },
                                  { template: 'template_2', name: 'User4' }
                                ]
                              })
      end
    end
  end

  describe "#send_bulk_api_per_template" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:current_time) { Time.zone.parse('2025-04-28 12:00') }
    let(:template_name) { "fake_template" }
    let(:metadata_key) { Constants.wati_campaigns_redis_key }

    before do
      allow(Time).to receive(:now).and_return(current_time)
      worker.instance_variable_set(:@current_time, current_time)
      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow($redis).to receive(:hset)
    end

    context "when there is a template with users_data" do
      let(:users_data) do
        { template_name => [
          { phone: "9999999999", name: "john", poster_image_url: "fake_image_url", event_name: "Event",
            cta_link: "fake_cta_link", amount: "100" }
        ]
        }
      end

      before do
        allow(worker).to receive(:organise_data_based_on_template).and_return(users_data)
      end

      it "should store data in redis using hset in json" do
        worker.send(:send_bulk_api_per_template)
        broadcast_name = "#{template_name}_2025_04_28_1"
        expect($redis).to have_received(:hset) do |redis_key, broadcast_name, json_data|
          expect(redis_key).to eq(metadata_key)
          expect(broadcast_name).to eq(broadcast_name)
          user_data = JSON.parse(json_data)
          expect(user_data).to include(
                                 "919999999999" => a_hash_including(
                                   "name" => "john",
                                   "poster_image_url" => "fake_image_url",
                                   "event_name" => "Event",
                                   "poster_link" => "fake_cta_link",
                                   "amount" => ""
                                 )
                               )
        end
        expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).with(
          template_name,
          broadcast_name
        )
      end
    end

    context "when there are multiple templates" do
      let(:users_data) do
        { "fake_template_1" => [
          { phone: "9999999999", name: "Alice", poster_image_url: "fake_image_url_1", event_name: "Event1",
            cta_link: "fake_cta_link_1", amount: "100" }],
          "fake_template_2" => [
            { phone: "8888888888", name: "Bob", poster_image_url: "fake_image_url_2", event_name: "Event2",
              cta_link: "fake_cta_link_2", amount: "200" }]
        }
      end
      let(:redis_store_count) { [] }

      before do
        allow(worker).to receive(:organise_data_based_on_template).and_return(users_data)
        allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
        allow($redis).to receive(:hset) do |redis_key, b_name, json_data|
          redis_store_count << [redis_key, b_name, json_data]
        end
      end

      it "should separate the data per template" do
        worker.send(:send_bulk_api_per_template)
        expect($redis).to have_received(:hset).twice

        users_data.each do |template, users|
          broadcast_name = "#{template}_2025_04_28_1"
          redis_key, broadcast_name, json_data = redis_store_count.find { |_, b_name, _| b_name == broadcast_name }

          expect(redis_key).to eq(metadata_key)
          expect(broadcast_name).to eq(broadcast_name)

          user_data = JSON.parse(json_data)

          users.each do |user|
            formatted_phone = "91#{user[:phone]}"
            expect(user_data).to include(
                                   formatted_phone => a_hash_including(
                                     "name" => user[:name],
                                     "poster_image_url" => user[:poster_image_url],
                                     "event_name" => user[:event_name],
                                     "poster_link" => user[:cta_link],
                                     "amount" => ""
                                   )
                                 )
          end

          expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).with(
            template,
            broadcast_name
          )
        end
      end
    end

    context "when there is no users data" do
      before do
        allow(worker).to receive(:organise_data_based_on_template).and_return({})
        allow($redis).to receive(:hset)
        allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      end
      it "should not store anything in redis" do
        worker.send(:send_bulk_api_per_template)
        expect($redis).not_to have_received(:hset)
        expect(SendWhatsappMessagesUsingBulkApi).not_to have_received(:perform_async)
      end
    end
  end

  describe "#send_mixpanel_events" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    before do
      allow(EventTracker).to receive(:perform_async)
      worker.instance_variable_set(:@users_master_data, users_master_data)
      worker.instance_variable_set(:@mixpanel_event_name, mixpanel_event_name)
      worker.send(:send_mixpanel_events)
    end

    context "When user_master_data is empty" do
      let(:users_master_data) { {} }
      let(:mixpanel_event_name) { "wati_reactivation_campaign_triggered_backend" }
      it "should not send any events" do
        expect(EventTracker).not_to have_received(:perform_async)
      end
    end

    context "When user_master_data has one user for reactivation" do
      let(:mixpanel_event_name) { "wati_reactivation_campaign_triggered_backend" }
      let(:users_master_data) { {1 => { event_id: "E1", creative_id: "C1", amount: 100, plan_amount: 399, trigger_count: 2, broadcast_name: "fake_broadcast",
                                        priority_of_event: "high" }} }
      it "should send one event with plus one trigger_count" do
        expect(EventTracker).to have_received(:perform_async).with(1, "wati_reactivation_campaign_triggered_backend", {
          event_id: "E1",
          priority_of_event: "high",
          creative_id: "C1",
          broadcast_name: "fake_broadcast",
          amount: 100,
          plan_amount: 399,
          trigger_count: 3
        })
      end
    end

    context "When user_master_data has one user for trial activation" do
      let(:mixpanel_event_name) { "wati_trial_activation_campaign_triggered_backend" }
      let(:users_master_data) { { 1 => { event_id: "E1", creative_id: "C1", amount: 0, plan_amount: 0, trigger_count: 0,  broadcast_name: "fake_broadcast",
                                         priority_of_event: "high"} } }
      it "should send one trial activation event without amount fields" do
        expect(EventTracker).to have_received(:perform_async).with(1, "wati_trial_activation_campaign_triggered_backend", {
          event_id: "E1",
          priority_of_event: "high",
          creative_id: "C1",
          broadcast_name: "fake_broadcast",
          trigger_count: 1
        })
      end
    end

    context "When trigger_count is zero" do
      let(:mixpanel_event_name) { "wati_reactivation_campaign_triggered_backend" }
      let(:users_master_data) { {1 => { event_id: "E1", creative_id: "C1", amount: 100, plan_amount: 299, trigger_count: 0, broadcast_name: "fake_broadcast",
                                        priority_of_event: "high"}} }
      it "should send by adding one to trigger_count" do
        expect(EventTracker).to have_received(:perform_async).with(1, "wati_reactivation_campaign_triggered_backend", {
          event_id: "E1",
          priority_of_event: "high",
          creative_id: "C1",
          broadcast_name: "fake_broadcast",
          amount: 100,
          plan_amount: 299,
          trigger_count: 1
        })
      end
    end

    context "When multiple users has user_master_data" do
      let(:mixpanel_event_name) { "wati_reactivation_campaign_triggered_backend" }
      let(:users_master_data) { {
        1 => { event_id: "E1", creative_id: "C1", amount: 100, plan_amount: 399, trigger_count: 2, broadcast_name: "fake_broadcast_one" , priority_of_event: "high"},
        2 => { event_id: "E2", creative_id: "C2", amount: 200, plan_amount: 499, trigger_count: 5, broadcast_name: "fake_broadcast_two", priority_of_event: "high" }
      } }
      it "should send all the events to mixpanel" do
        expect(EventTracker).to have_received(:perform_async).with(1, "wati_reactivation_campaign_triggered_backend", {
          event_id: "E1",
          priority_of_event: "high",
          creative_id: "C1",
          broadcast_name: "fake_broadcast_one",
          amount: 100,
          plan_amount: 399,
          trigger_count: 3
        })
        expect(EventTracker).to have_received(:perform_async).with(2, "wati_reactivation_campaign_triggered_backend", {
          event_id: "E2",
          priority_of_event: "high",
          creative_id: "C2",
          broadcast_name: "fake_broadcast_two",
          amount: 200,
          plan_amount: 499,
          trigger_count: 6
        })
      end
    end
  end

  describe "#update_trigger_counts" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:user1) { create(:user) }
    let(:user2) { create(:user) }
    let(:user_ids) { [user1.id, user2.id] }
    let(:metadata_key) { Constants.premium_reactivation_wati_campaign_count }

    before do
      create(:user_metadatum, user_id: user1.id, key: metadata_key, value: 2)
      create(:user_metadatum, user_id: user2.id, key: metadata_key, value: 0)
      worker.instance_variable_set(:@user_ids, user_ids)
      worker.instance_variable_set(:@metadata_key, metadata_key)
    end

    context "when user metadata exists with a valid key" do
      it "should add the existing value by 1" do
        expect {
          worker.send(:update_trigger_counts) }.to change {
          UserMetadatum.find_by(user_id: user1.id, key: metadata_key).value.to_i
        }.from(2).to(3)
      end
    end

    context "when user metadata exists but starts at zero" do
      it "should add the value from 0 to 1" do
        expect {
          worker.send(:update_trigger_counts) }.to change {
          UserMetadatum.find_by(user_id: user2.id, key: metadata_key).value.to_i
        }.from(0).to(1)
      end
    end
  end

  describe "#cleanup_data" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:user_ids) { create_list(:user, 3).map(&:id) }
    let(:creative_id_metadata_key) { 'creative_id' }
    let(:campaign_name) { 'test_campaign' }
    let(:poster_image_url_key) { Constants.poster_image_url(campaign_name) }

    before do
      worker.instance_variable_set(:@user_ids, user_ids)
      worker.instance_variable_set(:@creative_id_metadata_key, creative_id_metadata_key)
      worker.instance_variable_set(:@campaign_name, campaign_name)
    end

    context "when metadata exists for given user_ids" do
      before do
        user_ids.each do |user_id|
          create(:user_metadatum, user_id: user_id, key: creative_id_metadata_key)
          create(:user_metadatum, user_id: user_id, key: poster_image_url_key)
        end
      end
      it "should remove the relevant metadata entries" do
        expect { worker.send(:cleanup_data) }.to change { UserMetadatum.where(user_id: user_ids).count }.by(-6)
      end
    end

    context "when some users have relevant data" do
      before do
        create(:user_metadatum, user_id: user_ids.first, key: creative_id_metadata_key)
        create(:user_metadatum, user_id: user_ids.last, key: poster_image_url_key)
      end
      it "should remove only existing data" do
        expect { worker.send(:cleanup_data) }.to change { UserMetadatum.where(user_id: user_ids).count }.by(-2)
      end
    end

    context "when no relevant metadata exists" do
      it "should not delete any records" do
        expect { worker.send(:cleanup_data) }.not_to change { UserMetadatum.count }
      end
    end
  end

  describe "#get_event_type" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:current_time) { Time.zone.now }
    let(:event) { create(:event, start_time: event_start_time) }
    before do
      worker.instance_variable_set(:@current_time, current_time)
    end

    context "when event starts today" do
      let(:event_start_time) { current_time }
      it "should return today" do
        expect(worker.send(:get_event_type, event)).to eq("today")
      end
    end

    context "when event starts tomorrow" do
      let(:event_start_time) { (current_time + 1.day).change(hour: 10) }
      it "should return tomorrow" do
        expect(worker.send(:get_event_type, event)).to eq("tomorrow")
      end
    end

    context "when event is beyond tomorrow" do
      let(:event_start_time) { (current_time + 2.days).change(hour: 10) }
      it "should return upcoming" do
        expect(worker.send(:get_event_type, event)).to eq("upcoming")
      end
    end
  end

  describe "#get_template_name" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:template_key) { :reactivation_templates }
    let(:master_templates) { { template_key => {
      today_template: "today_high_event_for_reactivation",
      today_template_rs_1: "today_high_event_campaign_reactivation_with_rs_1",
      tomorrow_template: "tomorrow_high_event_for_re_activation",
      tomorrow_template_rs_1: "tomorrow_high_event_campaign_reactivation_with_rs_1",
      upcoming_template: "upcoming_high_event_for_re_activation",
      upcoming_template_rs_1: "upcoming_high_event_campaign_reactivation_with_rs_1"
    } } }

    before do
      worker.instance_variable_set(:@template_key, template_key)
      worker.instance_variable_set(:@master_templates, master_templates)
    end

    context "when event_type is today" do
      context "when is_rs_1_offer is 1" do
        it "should return today_high_event_for_reactivation_with_rs_1" do
          expect(worker.send(:get_template_name, "today", 1)).to eq("today_high_event_campaign_reactivation_with_rs_1")
        end
      end

      context "when is_rs_1_offer is 0" do
        it "should return today_high_event_for_reactivation" do
          expect(worker.send(:get_template_name, "today", 0)).to eq('today_high_event_for_reactivation')
        end
      end
    end

    context "when event_type is tomorrow" do
      context "when is_rs_1_offer is 1" do
        it "should return tomorrow_high_event_for_re_activation_with_rs_1" do
          expect(worker.send(:get_template_name, "tomorrow", 1)).to eq("tomorrow_high_event_campaign_reactivation_with_rs_1")
        end
      end

      context "when is_rs_1_offer is 0" do
        it "should return tomorrow_high_event_for_re_activation" do
          expect(worker.send(:get_template_name, "tomorrow", 0)).to eq("tomorrow_high_event_for_re_activation")
        end
      end
    end

    context "when event_type is upcoming" do
      context "when is_rs_1_offer is 1" do
        it "should return upcoming_high_event_for_re_activation_with_rs_1" do
          expect(worker.send(:get_template_name, "upcoming", 1)).to eq("upcoming_high_event_campaign_reactivation_with_rs_1")
        end
      end

      context "when is_rs_1_offer is 0" do
        it "should return upcoming_high_event_for_re_activation" do
          expect(worker.send(:get_template_name, "upcoming", 0)).to eq("upcoming_high_event_for_re_activation")
        end
      end
    end

    context "when event_type is invalid" do
      it "should return nil" do
        expect(worker.send(:get_template_name, "invalid", 0)).to be_nil
      end
    end
  end

  describe "#get_deep_link_for_event_and_creative" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:event) { create(:event, active: true, priority: "high") }
    let(:free_creative) { create(:poster_creative, event: event, active: true, primary: true, paid: false) }
    let(:primary_creative) { create(:poster_creative, event: event, active: true, primary: true, paid: true) }
    let(:paid_creative) { create(:poster_creative, event: event, active: true, primary: false, paid: true) }

    before do
      free_creative
      primary_creative
      paid_creative
    end

    context "when the creative is primary and paid" do
      it "should generates a deep link" do
        expect(Singular).to receive(:shorten_link).and_return('https://short.link')
        link = worker.send(:get_deep_link_for_event_and_creative, event.id, primary_creative.id)
        expect(link).to eq('https://short.link')
      end
    end

    context "when the creative is non primary paid" do
      it "should generates a deep link" do
        expect(Singular).to receive(:shorten_link).and_return('https://short.link')
        link = worker.send(:get_deep_link_for_event_and_creative, event.id, paid_creative.id)
        expect(link).to eq('https://short.link')
      end
    end

    context "when verifying URI construction" do
      it "should construct the deeplink_uri and link_uri correctly" do
        allow(URI).to receive(:parse).and_call_original
        allow(URI).to receive(:encode_www_form).and_call_original
        expect(Singular).to receive(:shorten_link) do |uri_string|
          uri = URI.parse(uri_string)
          query_params = URI.decode_www_form(uri.query).to_h
          expect(uri.to_s).to match(/^https:\/\/prajaapp\.sng\.link\/A3x5b\/l0ft\?/)
          expect(query_params).to have_key("_dl")
          expect(query_params).to have_key("_ddl")
          expected_deeplink = "praja://buzz.praja.app/posters/layout?id=#{event.id}&creative_id=#{primary_creative.id}"
          expect(query_params["_dl"]).to eq(expected_deeplink)
          expect(query_params["_ddl"]).to eq(expected_deeplink)
          "https://short.link"
        end
        link = worker.send(:get_deep_link_for_event_and_creative, event.id, primary_creative.id)
        expect(link).to eq('https://short.link')
        expect(URI).to have_received(:parse).with("praja://buzz.praja.app/posters/layout?id=#{event.id}&creative_id=#{primary_creative.id}")
        expect(URI).to have_received(:parse).with('https://prajaapp.sng.link/A3x5b/l0ft')
      end
    end

    context "when checking variable assignment" do
      it "should assign link variable before shortening" do
        deeplink_uri_mock = instance_double(URI::Generic, to_s: "mock_deeplink_uri")
        link_uri_mock = instance_double(URI::Generic, to_s: "mock_link_uri")
        allow(URI).to receive(:parse).with(/praja:/).and_return(deeplink_uri_mock)
        allow(URI).to receive(:parse).with(/https:/).and_return(link_uri_mock)
        allow(link_uri_mock).to receive(:query=)
        expect(Singular).to receive(:shorten_link).with("mock_link_uri").and_return("https://short.link")
        link = worker.send(:get_deep_link_for_event_and_creative, event.id, primary_creative.id)
        expect(link).to eq("https://short.link")
      end
    end
  end

  describe "#get_premium_experience_screen_deep_link" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:deeplink) { "praja://buzz.praja.app/premium-experience?payment-sheet=true" }
    let(:link) { "https://prajaapp.sng.link/A3x5b/l0ft" }

    context "when generating deeplink URI" do
      it "should parse the deeplink" do
        deeplink_uri = URI.parse(deeplink)
        expect(deeplink_uri.scheme).to eq("praja")
        expect(deeplink_uri.host).to eq("buzz.praja.app")
        expect(deeplink_uri.path).to eq("/premium-experience")
        expect(deeplink_uri.query).to eq("payment-sheet=true")
      end
    end

    context "when generating link URI" do
      it "should parse the base link" do
        link_uri = URI.parse(link)
        expect(link_uri.scheme).to eq("https")
        expect(link_uri.host).to eq("prajaapp.sng.link")
        expect(link_uri.path).to eq("/A3x5b/l0ft")
      end
    end

    context "when appending query parameters" do
      it "should encode the deeplink correctly in the link URI" do
        link_uri = URI.parse(link)
        link_uri.query = URI.encode_www_form({ _dl: deeplink, _ddl: deeplink })
        expect(link_uri.query).to include("_dl=#{CGI.escape(deeplink)}")
        expect(link_uri.query).to include("_ddl=#{CGI.escape(deeplink)}")
      end
    end

    context "when generating final deep link" do
      it "should return a shortened link" do
        allow(Singular).to receive(:shorten_link).and_return("shortened_link")
        result = worker.send(:get_premium_experience_screen_deep_link)
        expect(result).to eq("shortened_link")
      end
    end

    context "when Singular API is called" do
      it "should call singular shorten_link with correct parameters" do
        full_link = "#{link}?_dl=#{CGI.escape(deeplink)}&_ddl=#{CGI.escape(deeplink)}"
        expect(Singular).to receive(:shorten_link).with(full_link, 1.month).and_return("shortened_link")
        worker.send(:get_premium_experience_screen_deep_link)
      end
    end
  end

  describe "#upload_csv_to_s3" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    let(:csv) { "name,phone\nshashi,7349834532" }
    let(:template_name) { "sample_template" }
    let(:batch_index) { 3 }
    let(:current_time) { Time.zone.parse("2025-04-03 14:30:45") }
    let(:s3_bucket) { "test-bucket" }
    let(:s3_object) { instance_double(Aws::S3::Object) }
    let(:s3_bucket) { instance_double(Aws::S3::Bucket, object: s3_object) }
    let(:s3_resource) { instance_double(Aws::S3::Resource, bucket: s3_bucket) }
    let(:s3_path) do
      date = worker.instance_variable_get(:@current_time).strftime("%Y-%m-%d")
      timestamp = worker.instance_variable_get(:@current_time).strftime("%Y-%m-%d_%H:%m:%s")
      "test/wati//#{template_name}/#{date}/campaign-#{batch_index}-at-#{timestamp}.csv"
    end

    before do
      worker.instance_variable_set(:@current_time, current_time)
      allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
      allow(s3_object).to receive(:put).and_return(true)
      allow(Constants).to receive(:aws_s3_bucket_name_for_csvs).and_return(s3_bucket)
      allow(Rails).to receive_message_chain(:application, :credentials).and_return({
                                                                                     aws_access_key_id: "test_key",
                                                                                     aws_secret_access_key: "test_secret"
                                                                                   })
      allow(Rails).to receive(:env).and_return("test")
    end

    context "when uploading a valid CSV file" do
      it "should initialize s3 resource" do
        expect(Aws::S3::Resource).to receive(:new).with(
          region: 'ap-south-1',
          credentials: an_instance_of(Aws::Credentials)
        )
        worker.send(:upload_csv_to_s3, csv, template_name, batch_index)
      end

      it "should create correct file name" do
        expect(s3_bucket).to receive(:object).with(s3_path)
        worker.send(:upload_csv_to_s3, csv, template_name, batch_index)
      end

      it "should calculate MD5 checksum" do
        expected_checksum = Digest::MD5.base64digest(csv)
        expect(s3_object).to receive(:put).with(body: csv, content_md5: expected_checksum)
        worker.send(:upload_csv_to_s3, csv, template_name, batch_index)
      end

      it "should return s3 object path" do
        result = worker.send(:upload_csv_to_s3, csv, template_name, batch_index)
        expect(result).to eq(s3_path)
      end
    end

    context "with different CSV content" do
      it "handles empty CSV content" do
        empty_csv = ""
        empty_checksum = Digest::MD5.base64digest(empty_csv)

        expect(s3_object).to receive(:put).with(body: empty_csv, content_md5: empty_checksum)

        worker.send(:upload_csv_to_s3, empty_csv, template_name, batch_index)
      end

      it "handles large CSV content" do
        large_csv = "name,phone\n" + ("shashi,8974353210\n" * 23)
        large_checksum = Digest::MD5.base64digest(large_csv)

        expect(s3_object).to receive(:put).with(body: large_csv, content_md5: large_checksum)

        worker.send(:upload_csv_to_s3, large_csv, template_name, batch_index)
      end
    end
  end

  describe "#import_data_in_user_wati_campaigns" do
    let(:worker) { InitiateWatiCampaignUsingCsv.new }
    context "when users_master_data is empty" do
      before do
        worker.instance_variable_set(:@users_master_data, {})
        worker.instance_variable_set(:@import_campaign_type, "trial_activation")
        worker.send(:import_data_in_user_wati_campaigns)
      end

      it "should not create any user_wati_campaign records" do
        expect(UserWatiCampaign).not_to receive(:import)
      end
    end

    context "when users_master_data has valid entries" do
      let(:user_id) { 3268 }
      let(:user_data) {
        {
          creative_id: 1,
          poster_image_url: "https://example.com/poster.jpg",
          trigger_count: 1,
          name: "John",
          phone: 304958023,
          amount: 19,
          plan_amount: 29,
          event_id: 2,
          event_name: "Sample Event",
          template: "trial_template",
          cta_link: "d32/r_3i2uec23",
          broadcast_name: "trial_template_2025_04_06",
          priority_of_event: "high"
        }
      }

      before do
        worker.instance_variable_set(:@users_master_data, { user_id => user_data })
        worker.instance_variable_set(:@import_campaign_type, "trial_activation")
      end

      it "should create and import user_wati_campaign records" do
        expect(UserWatiCampaign).to receive(:import) do |records, options|
          expect(records.length).to eq(1)
          campaign = records.first

          expect(campaign.user_id).to eq(user_id)
          expect(campaign.broadcast_name).to eq("trial_template_2025_04_06")
          expect(campaign.campaign_type).to eq("trial_activation")
          expect(campaign.status).to eq("triggered")
          expect(campaign.template_name).to eq("trial_template")

          expect(campaign.campaign_data.symbolize_keys).to eq(
                                              creative_id: 1,
                                              event_id: 2,
                                              priority_of_event: "high",
                                              event_name: "Sample Event",
                                              poster_image_url: "https://example.com/poster.jpg",
                                              trigger_count: 1,
                                              amount: 19,
                                              plan_amount: 29,
                                              cta_link: "d32/r_3i2uec23"
                                                           )

          expect(options[:batch_size]).to eq(100)
        end
        worker.send(:import_data_in_user_wati_campaigns)
      end
    end
  end
end
