# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SubCircleCreationUsersJoinHandling, type: :worker do
  context "sub circles creation post logic handling" do
    before :each do
      circle = FactoryBot.create(:circle)
      @sub_circle = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: circle)
    end

    it { is_expected.to be_processed_in :default }

    it { is_expected.to be_retryable 0 }

    it "returns if sub_circle_id is nil" do
      described_class.new.perform(nil)
      expect(SubCircle).not_to receive(:find_by).with(id: nil)
    end

    it "returns if sub_circle does not exist" do
      expect(SubCircle).to receive(:find_by).with(id: -1).and_return(nil)
      described_class.new.perform(-1)
    end

    it "returns if filters are blank" do
      allow(SubCircle).to receive(:find_by).and_return(@sub_circle)
      allow(SubCircleFilter).to receive(:where).and_return(SubCircleFilter.none)

      expect(UserCircle).not_to receive(:import)
      described_class.new.perform(@sub_circle.id)
    end

    it "associates users with the sub_circle" do
      allow(SubCircle).to receive(:find_by).and_return(@sub_circle)
      allow(SubCircleFilter).to receive(:where).and_return(SubCircleFilter.none)

      expect(UserCircle).not_to receive(:import)
      described_class.new.perform(@sub_circle.id)
    end

    it "should return users with filter_role_ids and filter_location_circle_ids as filters" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                      badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role)
      role2 = FactoryBot.create(:role)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id,)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role2.id.to_s)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id.to_s)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle.id)
      expect(SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling).to receive(:perform_async).exactly(2).times
      described_class.new.perform(sub_circle1.id)
    end

    it "should return users with filter_role_ids as filters" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                      badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role)
      role2 = FactoryBot.create(:role)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id,)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role2.id.to_s)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id.to_s)
      expect(UserCircle).to receive(:import)
      described_class.new.perform(sub_circle1.id)
    end

    it "should return users with filter_location_circle_ids as filters" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      filters = FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle.id)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, district_id: district_circle.id, mandal_id: mandal_circle.id, state_id: state_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id, district_id: district_circle.id, mandal_id: mandal_circle.id, state_id: state_circle.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user2, circle: political_circle)
      user_circle4 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      expect(UserCircle).to receive(:import)
      described_class.new.perform(sub_circle1.id)
    end

    it "should return users with filter_grade_level_ids as filters with grade level 5 from role" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role, grade_level: :grade_5)
      role2 = FactoryBot.create(:role, grade_level: :grade_5)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id,)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      expect(UserCircle).to receive(:import)
      described_class.new.perform(sub_circle1.id)
    end

    it "should return users with filter_grade_levels as filters with grade level 5 from user role grade level" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role, grade_level: :grade_4)
      role2 = FactoryBot.create(:role, grade_level: :grade_4)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id,)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      expect(UserCircle).to receive(:import)
      described_class.new.perform(sub_circle1.id)
    end


    it "should return users with filter_grade_levels and roles as filters with grade level 5 from user role grade level and role" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role, grade_level: :grade_4)
      role2 = FactoryBot.create(:role, grade_level: :grade_4)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id,)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role2.id.to_s)
      expect(UserCircle).to receive(:import)
      described_class.new.perform(sub_circle1.id)
    end

    it "should return users with filter_grade_levels and filter_locations as filters with grade level 5 from user role without purview grade level and location" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role, grade_level: :grade_4)
      role2 = FactoryBot.create(:role, grade_level: :grade_5)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "4")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle.id.to_s)
      expect(SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling).to receive(:perform_async).exactly(2).times
      described_class.new.perform(sub_circle1.id)
      end
    it "should return users with filter_grade_levels and filter_locations as filters with grade level 5 from user role with purview grade level and location" do
      state_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
      district_circle = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state_circle)
      mandal_circle = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district_circle)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal_circle)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user2, circle: village_circle)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "4")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle2.id.to_s)
      expect(SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling).to receive(:perform_async).exactly(2).times
      described_class.new.perform(sub_circle1.id)
    end
  end
end
