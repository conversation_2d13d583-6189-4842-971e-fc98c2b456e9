require 'rails_helper'

RSpec.describe TrendPost, type: :worker do
  describe '#perform' do
    context 'sidekiq_options' do
      it { is_expected.to be_retryable 0 }
      it { is_expected.to be_processed_in :critical }
    end

    context 'when args is empty' do
      it 'returns' do
        expect(described_class.new.perform).to be_nil
      end
    end

    context 'when args is not empty' do
      let(:post) { create(:post) }
      let(:user) { create(:user) }
      let(:trend_post) { described_class.new }

      it 'calls do_like' do
        allow(User).to receive(:find).with(user.id).and_return(user)
        allow_any_instance_of(Post).to receive(:do_like).with(user)

        expect_any_instance_of(Post).to receive(:do_like).with(user)
        trend_post.perform(post.id, [user.id])
      end

      it 'returns when user is not found' do
        allow(User).to receive(:find).with(nil).and_raise(ActiveRecord::RecordNotFound)

        expect_any_instance_of(Post).not_to receive(:do_like)
        trend_post.perform(post.id, [nil])
      end
    end
  end
end
