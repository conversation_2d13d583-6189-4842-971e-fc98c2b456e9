require 'rails_helper'

RSpec.describe CreateRenewalOrderForUser, type: :worker do
  context "Create a renewal order for a user" do
    before :each do
      @user = FactoryBot.create(:user)
      @order = FactoryBot.create(:order, :with_order_items, user: @user)
    end

    it { is_expected.to be_processed_in :default }

    it { is_expected.to be_retryable 0 }

    it "returns if user_id is nil" do
      described_class.new.perform(nil)
      expect(@user.orders.count).to eq(1)
    end

    it "returns if user is nil" do
      described_class.new.perform(0)
      expect(@user.orders.count).to eq(1)
    end

    it "creates a new order for the user with the help of old successful order" do
      @order.status = :successful
      @order.save!
      described_class.new.perform(@user.id)
      expect(@order.id).not_to eq(@user.orders.last.id)
      expect(@order.order_items.count).to eq(@user.orders.last.order_items.count)
      expect(@user.orders.count).to eq(2)
      expect(@user.orders.open.count).to eq(1)
      expect(@order.order_items.pluck(:item_id)).to eq(@user.orders.last.order_items.pluck(:item_id))
      expect(@order.order_items.pluck(:parent_order_item_id).uniq.compact).to_not eq(@user.orders.last.order_items
                                                                                          .pluck(:parent_order_item_id)
                                                                                          .uniq.compact)

      product_item_price = ItemPrice.where(item_type: "Product", item_id: Constants.get_poster_product_id,
                                           active: true, duration_in_months: 12).last
      final_price = product_item_price.price + product_item_price.maintenance_price
      # expect @order has product order item
      expect(@order.order_items.where(item_type: 'Product').count).to eq(1)
      expect(@user.orders.last.status).to eq("opened")
      expect(@user.orders.last.order_items.pluck(:order_id).uniq.last).to eq(@user.orders.last.id)
      expect(@user.orders.last.order_items.pluck(:order_id).uniq.last).to_not eq(@order.id)
      # check status frame parent_order_item_id
      frame_ids = @order.order_items.where(item_type: 'Frame').pluck(:item_id)
      status_frame = Frame.where(id: frame_ids, frame_type: 'status').first
      premium_frame_ids = frame_ids - [status_frame.id]
      premium_frame_ids.each do |frame_id|
        item_price = ItemPrice.where(item_type: 'Frame', item_id: frame_id, active: true,
                                     duration_in_months: 12).last
        expect(@user.orders.last.order_items.where(item_id: frame_id).first.item_price_id).to eq(nil)
      end
      status_frame_item_price = ItemPrice.where(item_type: 'Frame', item_id: status_frame.id, active: true,
                                                duration_in_months: 12).last
      final_price += status_frame_item_price.price + status_frame_item_price.maintenance_price
      expect(@user.orders.last.order_items.where(item_id: status_frame.id).first.parent_order_item_id).to eq(nil)
      expect(@user.orders.last.order_items.where(item_id: status_frame.id).first.item_price_id)
        .to eq(status_frame_item_price.id)
      expect(@order.payable_amount).to_not eq(@user.orders.last.payable_amount)
      expect(@user.orders.last.payable_amount).to eq(final_price)
    end

    it "returns if error occurs in begin block" do
      allow(Honeybadger).to receive(:notify).and_return(true)
      allow(Order).to receive(:new).and_raise(ActiveRecord::RecordNotUnique)

      described_class.new.perform(@user.id)
      expect(Honeybadger).to have_received(:notify)
      expect(@user.orders.count).to eq(1)
    end
  end
end
