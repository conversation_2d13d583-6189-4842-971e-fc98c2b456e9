require 'rails_helper'

RSpec.describe SendFollowNotification, type: :worker do
  context "garuda will send follow notification to user" do
    let(:time) { (1.minute) }
    let(:scheduled_job) { described_class.perform_at(time) }
    before :each do
      @user = FactoryBot.create(:user)
      @request_user = FactoryBot.create(:user)
      @user_follower = FactoryBot.create(:user_follower, user_id: @user.id, follower_id: @request_user.id)
    end

    it { is_expected.to be_processed_in :notifications }

    it { is_expected.to be_retryable 0 }

    it "check for the same argument in job been called" do
      described_class.perform_async(@user_follower.id)
      expect(described_class.jobs[0]['args']).to eq([@user_follower.id])
    end

    it "triggers a send garuda to send notification to user" do
      allow(GarudaNotification).to receive(:send_user_notification)
      described_class.new.perform(@user_follower.id)
      expect(GarudaNotification).to have_received(:send_user_notification)
    end
  end
end
