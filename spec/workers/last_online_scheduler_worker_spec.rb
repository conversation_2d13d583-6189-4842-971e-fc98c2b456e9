# frozen_string_literal: true
require 'rails_helper'

RSpec.describe LastOnlineSchedulerWorker do
  describe '#perform' do
    let(:user_id) { create(:user).id }
    let(:last_online_time) { Time.zone.now }
    let(:user_token_usage) { double('UserTokenUsage', created_at: last_online_time) }

    before do
      allow(UserTokenUsage).to receive(:where).with(user_id: user_id).and_return([user_token_usage])
      allow(UpdateLeadScore).to receive(:perform_async)
      allow($redis).to receive(:zadd)
    end

    context 'when perform is called' do
      it 'schedules UpdateLeadScore job' do
        subject.perform(user_id)
        expect(UpdateLeadScore).to have_received(:perform_async).with(user_id)
      end

      it 'updates Redis with the next run time' do
        subject.perform(user_id)
        expect($redis).to have_received(:zadd).with(Constants.update_floww_lead_score_based_on_last_online_key, (last_online_time + 7.days + 1.hour).to_i, user_id)
      end
    end
  end
end
