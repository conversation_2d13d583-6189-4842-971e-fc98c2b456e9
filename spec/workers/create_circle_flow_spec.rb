# frozen_string_literal: true
require 'rails_helper'

RSpec.describe CreateCircleFlow, type: :worker do

  it { is_expected.to be_processed_in :default }

  it { is_expected.to be_retryable 0 }

  before do
    state = FactoryBot.create(:circle, circle_type: :location, level: :state)
    district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
    mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
    village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
    @admin_user = FactoryBot.create(:admin_user)
    @photo = fixture_file_upload('app/assets/images/praja-full-logo.png', 'image/png')
    @user = FactoryBot.create(:user,
                              name: 'Test User',
                              village_id: village.id,
                              mandal_id: mandal.id,
                              district_id: district.id,
                              state_id: state.id,
                              poster_photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                              poster_photo_with_background: FactoryBot.create(:admin_medium, blob_data: @photo))

    allow(Transliteration::Text).to receive(:new).and_return(Transliteration::Text.new(@user.name))
    allow(Transliteration::Text.new(@user.name)).to receive(:transliterate).with('te').and_return('టెస్ట్ యూజర్')
    allow(Transliteration::Text.new(@user.name)).to receive(:transliterate).with('en').and_return('Test User')

    allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
  end

  context 'success' do
    it 'creates circle with required models' do
      @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                              h1_count: 1,
                                              h2_count: 1,
                                              user_leader_photos: [
                                                FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                  header_type: :header_1, priority: 1),
                                                FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                  header_type: :header_2, priority: 1)])

      expect do
        described_class.new.perform(@user.id, @admin_user.id)
      end.to change { Metadatum.where(entity_type: 'Circle', key: Constants.user_with_circle_key, value: @user.id).count }.by(1)
                                                                                                                          .and change { Circle.count }.by(1)
                                                                                                                                                      .and change { CirclePhoto.count }.by(1)
                                                                                                                                                                                       .and change { UserCirclePermissionGroup.count }.by(1)
                                                                                                                                                                                                                                      .and change { CirclesRelation.count }.by(1)
                                                                                                                                                                                                                                                                           .and change { UserPosterLayout.count }.by(1)
                                                                                                                                                                                                                                                                                                                 .and change { UserLeaderPhoto.count }.by(3)

      circle = Metadatum.where(entity_type: 'Circle', key: Constants.user_with_circle_key, value: @user.id).first.entity

      expect(circle.circle_type.to_sym).to eq(:interest)
      expect(circle.level.to_sym).to eq(:political_leader)
      expect(circle.name).to eq('టెస్ట్ యూజర్')
      expect(circle.name_en).to eq('Test User')
      expect(circle.photo.url).to eq(@user.poster_photo_with_background.url)
      expect(circle.circle_photos.first.photo.url).to eq(@user.poster_photo.url)
      expect(circle.conversation_type).to eq('channel')
      expect(circle.get_owner_id).to eq(@user.id)
      expect(Circle.has_active_layout?(circle.id)).to eq(true)

      circle_layout = UserPosterLayout.where(entity: circle, active: true).first
      expect(circle_layout.h1_count).to eq(1)
      expect(circle_layout.h2_count).to eq(2)
      expect(circle_layout.user_leader_photos.count).to eq(3)
      expect(circle_layout.user_leader_photos.where(header_type: :header_1).count).to eq(1)
      expect(circle_layout.user_leader_photos.where(header_type: :header_2).count).to eq(2)
    end
  end

  context 'failure' do
    it 'on circle create failure raise Honeybadger' do
      allow_any_instance_of(Circle).to receive(:save!).and_raise(ActiveRecord::RecordInvalid)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@user.id, @admin_user.id)
      expect(Honeybadger).to have_received(:notify)
    end

    it 'on circle user circle permission create failure raise Honeybadger' do
      allow(UserCirclePermissionGroup).to receive(:create).and_raise(ActiveRecord::RecordInvalid)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@user.id, @admin_user.id)
      expect(Honeybadger).to have_received(:notify)
    end

    it 'on circle relation create failure raise Honeybadger' do
      allow(CirclesRelation).to receive(:create).and_raise(ActiveRecord::RecordInvalid)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@user.id, @admin_user.id)
      expect(Honeybadger).to have_received(:notify)
    end

    it 'if active user poster layout is blank it sends a mail to admin user and raise Honeybadger' do
      # user_poster_layout = UserPosterLayout.where(entity: user).live.first
      # mock this line below
      allow(UserPosterLayout).to receive_message_chain(:where, :first).and_return(nil)
      allow(UserMailer).to receive(:send_email)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@user.id, @admin_user.id)
      expect(UserMailer).to have_received(:send_email)
      expect(Honeybadger).to have_received(:notify)
    end

    it 'if layout type is invalid it sends a mail to admin user and raise Honeybadger' do
      @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                              h1_count: 1,
                                              h2_count: 1,
                                              user_leader_photos: [
                                                FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo), header_type: :header_1, priority: 1),
                                                FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo), header_type: :header_2, priority: 1)])

      allow(UserPosterLayout::LAYOUTS).to receive(:keys).and_return([:layout_0_1])
      allow(UserMailer).to receive(:send_email)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@user.id, @admin_user.id)
      expect(UserMailer).to have_received(:send_email)
      expect(Honeybadger).to have_received(:notify)
    end

    it 'of circle layout create failure raise Honeybadger' do
      allow_any_instance_of(UserPosterLayout).to receive(:save!).and_raise(ActiveRecord::RecordInvalid)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@user.id, @admin_user.id)
      expect(Honeybadger).to have_received(:notify)
    end
  end
end
