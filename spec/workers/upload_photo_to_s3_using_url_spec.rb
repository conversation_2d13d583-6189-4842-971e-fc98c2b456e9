require "rails_helper"

RSpec.describe UploadPhotoToS3UsingUrl, type: :worker do
  describe "perform" do
    it { is_expected.to be_processed_in :default }

    it { is_expected.to be_retryable 0 }

    before :each do
      @photo = FactoryBot.create(:photo)
      @user = FactoryBot.create(:user, photo_id: @photo.id)
    end

    it "returns nil if user is nil" do
      expect(UploadPhotoToS3UsingUrl.new.perform(1, Faker::Internet.url, -1)).to eq(nil)
    end

    it "returns nil if user photo_id is not equal to image_id" do
      expect(UploadPhotoToS3UsingUrl.new.perform(2, Faker::Internet.url, @user.id)).to eq(nil)
    end

    context "if user photo_id is equal to image_id" do
      let(:s3_resource_mock) { instance_double(Aws::S3::Resource) }
      let(:s3_bucket_mock) { instance_double(Aws::S3::Bucket) }
      let(:s3_object_mock) { instance_double(Aws::S3::Object) }
      let(:url) { Faker::Internet.url }
      let(:uri) { URI.parse(url) }
      let(:response) { double("response") }
      let(:photo) { Photo.new(url: "https://cdn.thecircleapp.in/#{Rails.env}/photos/#{@user.id}/test-md5-hash.jpg", user: @user, service: :aws) }

      it "if couldn't able to read the image updates user photo id to nil" do
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test-bucket")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test-access-key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test-secret-access-key")

        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)

        allow(URI).to receive(:parse).and_return(uri)
        allow(Net::HTTP).to receive(:get_response).with(uri).and_return(response)
        allow(response).to receive(:code).and_return(400)

        expect(UploadPhotoToS3UsingUrl.new.perform(@photo.id, url, @user.id)).to eq(true)
        expect(@user.reload.photo_id).to eq(nil)
      end

      it "if couldn't able to upload the image updates user photo id to nil" do
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test-bucket")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test-access-key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test-secret-access-key")

        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)

        allow(URI).to receive(:parse).and_return(uri)
        allow(Net::HTTP).to receive(:get_response).with(uri).and_return(response)
        allow(response).to receive(:code).and_return(200)

        allow_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        allow(IO).to receive(:copy_stream).and_return(true)
        allow(Digest::MD5).to receive(:hexdigest).and_return("test-md5-hash.jpg")

        allow(s3_resource_mock).to receive(:bucket).and_return(s3_bucket_mock)
        allow(s3_bucket_mock).to receive(:object).and_return(s3_object_mock)
        allow(s3_object_mock).to receive(:upload_file).and_return(true)
        allow(Photo).to receive(:new).and_return(photo)
        allow(photo).to receive(:save).and_return(false)
        allow(@user).to receive(:save!).and_return(true)

        expect_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        expect(UploadPhotoToS3UsingUrl.new.perform(@photo.id, url, @user.id)).to eq(true)
        expect(@user.reload.photo_id).to eq(nil)
      end

      it "if uploads the image save the new photo url on to user photo" do
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test-bucket")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test-access-key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test-secret-access-key")

        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)

        allow(URI).to receive(:parse).and_return(uri)
        allow(Net::HTTP).to receive(:get_response).with(uri).and_return(response)
        allow(response).to receive(:code).and_return(200)

        allow_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        allow(IO).to receive(:copy_stream).and_return(true)
        allow(Digest::MD5).to receive(:hexdigest).and_return("test-md5-hash.jpg")

        allow(s3_resource_mock).to receive(:bucket).and_return(s3_bucket_mock)
        allow(s3_bucket_mock).to receive(:object).and_return(s3_object_mock)
        allow(s3_object_mock).to receive(:upload_file).and_return(true)
        allow(Photo).to receive(:new).and_return(photo)
        allow(photo).to receive(:save).and_return(true)
        allow(@user).to receive(:save!).and_return(true)

        expect_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        expect(UploadPhotoToS3UsingUrl.new.perform(@photo.id, url, @user.id)).not_to eq(nil)

        @user.reload

        expect(@user.photo_id).not_to eq(@photo.id)
      end

      it "if upload raises an exception notifies in honetbadger" do
        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test-bucket")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test-access-key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test-secret-access-key")

        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)

        allow(URI).to receive(:parse).and_return(uri)
        allow(Net::HTTP).to receive(:get_response).with(uri).and_return(response)
        allow(response).to receive(:code).and_return(200)

        allow_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        allow(IO).to receive(:copy_stream).and_return(true)
        allow(Digest::MD5).to receive(:hexdigest).and_return("test-md5-hash.jpg")
        allow(s3_resource_mock).to receive(:bucket).and_raise(Exception)

        expect_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        expect { UploadPhotoToS3UsingUrl.new.perform(@photo.id, url, @user.id) }.to raise_error(Exception)
        expect(@user.photo_id).to eq(@photo.id)
      end
    end
  end
end
