require 'rails_helper'

RSpec.describe ProcessSignedUpContact, type: :worker do
  it { is_expected.to be_processed_in :critical }

  it { is_expected.to be_retryable 1 }

  context 'create a UserContactSuggestion' do
    let(:user) { FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}".to_i) }
    let(:invited_user) { FactoryBot.create(:user) }
    let(:user_invite) { FactoryBot.create(:user_invite, phone: user.phone, user: invited_user) }

    it 'if user is not internal' do
      allow(UserInvite).to receive(:where).and_return([user_invite])
      allow(UserContactSuggestion).to receive(:create)

      described_class.new.perform(user.id)
      expect(UserContactSuggestion).to have_received(:create)
    end
  end

  context 'does not create a UserContactSuggestion' do
    let(:user) { FactoryBot.create(:user, phone: "20#{Faker::Number.unique.number(digits: 8)}".to_i) }
    let(:invited_user) { FactoryBot.create(:user) }
    let(:user_invite) { FactoryBot.create(:user_invite, phone: user.phone, user: invited_user) }

    it 'if user is internal' do
      allow(UserInvite).to receive(:where).and_return([user_invite])
      allow(UserContactSuggestion).to receive(:create)

      described_class.new.perform(user.id)
      expect(UserContactSuggestion).not_to have_received(:create)
    end
  end

  context 'does not create a UserContactSuggestion' do
    let(:user) { FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}".to_i) }
    let(:invited_user) { FactoryBot.create(:user) }
    let(:user_invite) { FactoryBot.create(:user_invite, phone: user.phone, user: invited_user) }

    it 'if UserContactSuggestion already exists' do
      allow(UserInvite).to receive(:where).and_return([user_invite])
      allow(UserContactSuggestion).to receive(:create).and_raise(ActiveRecord::RecordNotUnique)

      described_class.new.perform(user.id)
      expect(UserContactSuggestion).to have_received(:create)
    end
  end
end
