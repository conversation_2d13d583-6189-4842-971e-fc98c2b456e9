require "rails_helper"

RSpec.describe QueueInvalidContact, type: :worker do
  context "perform" do
    it { is_expected.to be_processed_in :low }

    it { is_expected.to be_retryable 1 }

    before :each do
      @user = FactoryBot.create(:user, phone: "20#{Faker::Number.unique.number(digits: 8)}".to_i)
      @invalid_contact = FactoryBot.create(:user, phone: Faker::PhoneNumber.cell_phone_in_e164)
    end

    it 'returns if user is internal' do
      allow(JSON).to receive(:parse)

      described_class.new.perform({ "name" => @invalid_contact.name, "phone" => @invalid_contact.phone, "phone_user_id" => @invalid_contact.id }.to_json, @user.id)

      expect(JSON).not_to have_received(:parse)
    end
  end
end
