require 'rails_helper'

RSpec.describe RefundSubscriptionCharge, type: :worker do
  describe '#perform' do
    let(:cashfree) { :cashfree }
    let(:juspay) { :juspay }
    let!(:subscription) { create(:subscription, payment_gateway: cashfree) }
    let!(:subscription_charge) { create(:subscription_charge, subscription: subscription, user: subscription.user, amount: 100, status: :success) }
    let!(:refund) { create(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 50, status: :initiated) }

    context "with Cashfree payment gateway" do
      before do
        subscription.update(payment_gateway: cashfree)
      end

      it "processes the refund with Cashfree API" do
        allow(CashfreePaymentUtils).to receive(:cashfree_post_v1).and_return('status' => 'OK')

        described_class.new.perform(refund.id)

        expect(CashfreePaymentUtils).to have_received(:cashfree_post_v1).with(
          "/subscriptions/create-refund",
          hash_including(
            "merchantTxnId": subscription_charge.pg_id,
            "refundAmount": 50,
            "merchantRefundId": "refund-#{refund.id}-#{subscription_charge.pg_id}"
          )
        )
      end

      it "updates the refund's pg_json with the API response" do
        response = { 'status' => 'OK' }
        allow(CashfreePaymentUtils).to receive(:cashfree_post_v1).and_return(response)

        described_class.new.perform(refund.id)

        refund.reload
        expect(refund.pg_json).to eq(response)
      end
    end

    context "with Juspay payment gateway" do
      before do
        subscription.update(payment_gateway: juspay)
      end

      it "processes the refund with Juspay API" do
        allow(JuspayPaymentUtils).to receive(:post).and_return('status' => 'PENDING')

        described_class.new.perform(refund.id)

        expect(JuspayPaymentUtils).to have_received(:post).with(
          "/orders/#{subscription_charge.pg_id}/refunds",
          hash_including(
            unique_request_id: "R-#{refund.id}-#{subscription_charge.pg_id}",
            amount: 50
          )
        )
      end

      it "updates the refund's pg_json with the API response" do
        response = { 'status' => 'PENDING' }
        allow(JuspayPaymentUtils).to receive(:post).and_return(response)

        described_class.new.perform(refund.id)

        refund.reload
        expect(refund.pg_json).to eq(response)
      end
    end

    it "logs a payment event in mixpanel" do
      # Create a real subscription charge and refund
      real_subscription = create(:subscription, payment_gateway: :cashfree)
      real_subscription_charge = create(:subscription_charge,
                                       subscription: real_subscription,
                                       user: real_subscription.user,
                                       amount: 100,
                                       charge_amount: 100,
                                       status: :success)

      real_refund = create(:subscription_charge_refund,
                          subscription_charge: real_subscription_charge,
                          amount: 50,
                          status: :initiated)

      # Mock the API call
      allow(CashfreePaymentUtils).to receive(:cashfree_post_v1).and_return('status' => 'OK')

      # Mock the mixpanel event logging
      allow(real_subscription_charge).to receive(:log_payment_mixpanel_event)
      allow(real_subscription_charge).to receive(:is_trial_charge?).and_return(false)

      # Allow the worker to find our real refund
      allow(SubscriptionChargeRefund).to receive(:find_by).with(id: real_refund.id).and_return(real_refund)

      # Perform the worker action
      described_class.new.perform(real_refund.id)

      # Verify the mixpanel event was logged
      expect(real_subscription_charge).to have_received(:log_payment_mixpanel_event).with(
        event_name: 'payment_refund_initiated_backend',
        extra_params: { refund_amount: 50 }
      )
    end

    it "does nothing if the refund is not in initiated state" do
      refund.update(status: :success)
      allow(CashfreePaymentUtils).to receive(:cashfree_post_v1)

      described_class.new.perform(refund.id)

      expect(CashfreePaymentUtils).not_to have_received(:cashfree_post_v1)
    end

    it "does nothing if the refund doesn't exist" do
      allow(SubscriptionChargeRefund).to receive(:find_by).and_return(nil)
      allow(CashfreePaymentUtils).to receive(:cashfree_post_v1)

      expect {
        described_class.new.perform(999)
      }.not_to raise_error

      expect(CashfreePaymentUtils).not_to have_received(:cashfree_post_v1)
    end

    it "does nothing if the subscription charge doesn't exist" do
      non_existent_refund = create(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 10, status: :initiated)
      allow(non_existent_refund).to receive(:subscription_charge).and_return(nil)
      allow(SubscriptionChargeRefund).to receive(:find_by).and_return(non_existent_refund)
      allow(CashfreePaymentUtils).to receive(:cashfree_post_v1)

      expect {
        described_class.new.perform(non_existent_refund.id)
      }.not_to raise_error

      expect(CashfreePaymentUtils).not_to have_received(:cashfree_post_v1)
    end

    context "with PhonePe payment gateway" do
      let(:phonepe) { :phonepe }

      before do
        subscription.update(payment_gateway: phonepe)
      end

      it "processes the refund with PhonePe API" do
        allow(PhonepePaymentUtils).to receive(:post).and_return('refundId' => 'OMR12345', 'state' => 'PENDING')

        described_class.new.perform(refund.id)

        expect(PhonepePaymentUtils).to have_received(:post).with(
          "/payments/v2/refund",
          hash_including(
            merchantRefundId: "refund-#{refund.id}-#{subscription_charge.pg_id}",
            originalMerchantOrderId: subscription_charge.pg_id,
            amount: 5000 # 50 rupees in paise
          )
        )
      end

      it "updates the refund's pg_json with the API response" do
        response = { 'refundId' => 'OMR12345', 'state' => 'PENDING' }
        allow(PhonepePaymentUtils).to receive(:post).and_return(response)

        described_class.new.perform(refund.id)

        refund.reload
        expect(refund.pg_json).to eq(response)
      end

      it "logs the mixpanel event for non-trial charges" do
        # Create a real subscription charge and refund to test mixpanel logging
        real_subscription = create(:subscription, payment_gateway: phonepe)
        real_subscription_charge = create(:subscription_charge, subscription: real_subscription, user: real_subscription.user, amount: 100, status: :success)
        real_refund = create(:subscription_charge_refund, subscription_charge: real_subscription_charge, amount: 50, status: :initiated)

        # Mock the API call
        allow(PhonepePaymentUtils).to receive(:post).and_return('refundId' => 'OMR12345', 'state' => 'PENDING')

        # Mock the mixpanel event logging
        allow(real_subscription_charge).to receive(:log_payment_mixpanel_event)
        allow(real_subscription_charge).to receive(:is_trial_charge?).and_return(false)

        # Allow the worker to find our real refund
        allow(SubscriptionChargeRefund).to receive(:find_by).with(id: real_refund.id).and_return(real_refund)

        # Perform the worker action
        described_class.new.perform(real_refund.id)

        # Verify the mixpanel event was logged
        expect(real_subscription_charge).to have_received(:log_payment_mixpanel_event).with(
          event_name: 'payment_refund_initiated_backend',
          extra_params: { refund_amount: 50 }
        )
      end
    end

    it "notifies Honeybadger if an error occurs" do
      allow(CashfreePaymentUtils).to receive(:cashfree_post_v1).and_raise(StandardError.new("API Error"))
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(refund.id)

      expect(Honeybadger).to have_received(:notify).with(
        instance_of(StandardError),
        hash_including(
          context: hash_including(
            subscription_charge_id: subscription_charge.id,
            refund_id: refund.id,
            amount: 50
          )
        )
      )
    end
  end
end
