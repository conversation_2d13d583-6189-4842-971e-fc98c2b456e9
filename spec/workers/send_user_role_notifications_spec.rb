require 'rails_helper'

RSpec.describe SendUserRoleNotifications, type: :worker do
  describe "send user role notifications" do
    context "send notification for badge assigned user" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @role = FactoryBot.create(:role)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
      end

      it { is_expected.to be_processed_in :notifications }

      it "check for the same argument in job been called" do
        described_class.perform_async(@user_role.id)
        expect(described_class.jobs[0]['args']).to eq([@user_role.id])
      end

      it "calls send_notification_for_badge_assigned_user method" do
        expect_any_instance_of(UserRole).to receive(:send_notification_for_badge_assigned_user).once
        SendUserRoleNotifications.new.perform(@user_role.id)
      end

      # Disabling internal notifications
      # it "calls send_internal_notification method" do
      #   expect_any_instance_of(UserRole).to receive(:send_internal_notification).once
      #   SendUserRoleNotifications.new.perform(@user_role.id)
      # end
    end
  end
end
