# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateUserUnreadNotificationsCount, type: :worker do
  describe 'perform' do
    context 'performs in low queue' do
      it { is_expected.to be_processed_in :low }
    end

    context 'No retries for the job' do
      it { is_expected.to be_retryable 0 }
    end

    context 'when user_id is nil' do
      it 'return nil' do
        expect(described_class.new.perform(nil)).to be_nil
      end
    end

    context 'when user is not present' do
      it 'return nil' do
        expect(described_class.new.perform(-1)).to be_nil
      end
    end

    context 'when user is present' do
      before :each do
        @user = FactoryBot.create(:user)
      end

      it 'updates the user unread notifications count' do
        FactoryBot.create(:notification, user: @user)
        described_class.new.perform(@user.id)

        expect(@user.reload.unread_notifications_count).to eq(1)
      end
    end
  end
end
