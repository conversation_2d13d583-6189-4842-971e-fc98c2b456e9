# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MovePremiumPitchToLayoutCompleted, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe "#perform" do
    context "When user is not present" do
      it "should return without performing any actions" do
        allow(User).to receive(:find).with(1).and_return(nil)
        expect_any_instance_of(User).not_to receive(:premium_pitch)
      end
    end
    context "When user is present and premium pitch is not present" do
      let(:user) { FactoryBot.create(:user) }
      it "should not call worker" do
        allow(User).to receive(:find).with(user.id).and_return(user)
        expect(user).not_to receive(:premium_pitch)
      end
    end
    context "When user is present and premium pitch is present" do
      let(:user) { FactoryBot.create(:user) }
      let(:premium_pitch) { FactoryBot.create(:premium_pitch, user: user) }
      it "should call worker" do
        allow(User).to receive(:find).with(user.id).and_return(user)
        premium_pitch.enabled_trial!
        expect(premium_pitch.status).to eq("trail_enabled")
      end
    end
  end
end
