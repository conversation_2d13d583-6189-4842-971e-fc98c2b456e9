require 'rails_helper'

RSpec.describe SendPosterCampaignWati, type: :worker do
  describe 'perform' do
    context 'when user_id or campaign_name is missing' do
      subject { described_class.new }

      it 'returns early' do
        expect(subject.perform(nil, nil, nil)).to be_nil
      end
    end

    context 'when user is not found' do
      before :each do
        allow(User).to receive(:find_by_id).and_return(nil)
      end

      subject { described_class.new }

      it 'returns early' do
        expect(subject.perform(1, 'test_campaign', 'test_template')).to be_nil
      end
    end

    context 'when poster_url is missing' do
      let(:user) { FactoryBot.create(:user) }
      before :each do
        allow(User).to receive(:find_by_id).and_return(user)
      end

      subject { described_class.new }

      it 'returns early' do
        expect(subject.perform(user.id, 'test_campaign', 'test_template')).to be_nil
      end
    end

    context 'when poster_url is present' do
      let(:user) { FactoryBot.create(:user) }
      let(:user_metadata) { FactoryBot.create(:user_metadatum, user_id: user.id, key: 'poster_image_url_test_campaign', value: 'https://example.com/poster.jpg') }
      before :each do
        allow(User).to receive(:find_by_id).and_return(user)
        allow(UserMetadatum).to receive(:where).and_return([user_metadata])
      end

      subject { described_class.new }

      it 'sends the poster image to the user via Wati' do
        expect(WatiIntegration).to receive(:perform_async).with('test_template', [{ 'number' => user.phone.to_s, 'name' => user.name, 'poster_url' => 'https://example.com/poster.jpg' }])
        subject.perform(user.id, 'test_campaign', 'test_template')
      end
    end
  end
end
