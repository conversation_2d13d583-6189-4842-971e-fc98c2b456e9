require "rails_helper"

RSpec.describe ImportCircleOwnerFollow, type: :worker do
  context "perform" do
    it { is_expected.to be_processed_in :low }

    it { is_expected.to be_retryable 0 }

    before :each do
      @circle = FactoryBot.create(:circle)
      @owner = FactoryBot.create(:user)
      @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, circle: @circle, user: @owner, permission_group_id: Constants.owner_permission_group_id)
    end

    it "imports circle members to owner as followers" do
      5.times do |i|
        FactoryBot.create(:user_circle, circle: @circle)
      end

      described_class.new.perform(@circle.id, @owner.id, Time.zone.now)
      expect(UserFollower.where(user_id: @owner.id).count).to eq(5)
    end

    it "notify error if raises exception" do
      3.times do |i|
        FactoryBot.create(:user_circle, circle: @circle)
      end

      allow(UserFollower).to receive(:import).and_raise(Exception)
      allow(Honeybadger).to receive(:notify)

      described_class.new.perform(@circle.id, @owner.id, Time.zone.now)

      expect(Honeybadger).to have_received(:notify)
    end
  end
end
