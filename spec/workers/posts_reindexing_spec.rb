require "rails_helper"

RSpec.describe PostsReindexing, type: :worker do
  context "perform" do
    it "re indexes the last 3 days posts" do
      @post = FactoryBot.create(:post, created_at: Time.zone.today - 2.days)
      @post2 = FactoryBot.create(:post, created_at: Time.zone.today - 1.day)
      @post3 = FactoryBot.create(:post, created_at: Time.zone.today)

      allow(Post).to receive_message_chain(:where, :find_in_batches).and_yield([@post, @post2, @post3])

      allow(IndexPostNewV2).to receive(:perform_async)

      described_class.new.perform

      expect(IndexPostNewV2).to have_received(:perform_async).thrice
    end
  end
end
