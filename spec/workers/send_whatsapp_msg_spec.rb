require 'rails_helper'
require 'webmock/rspec'

RSpec.describe SendWhatsappMsg, type: :worker do
  let(:wati_end_point) { 'https://live-server-6274.wati.io/api/v1/sendTemplateMessage' }
  let(:template_name) { 'sample_template' }
  let(:broadcast_name) { 'sample_broadcast' }
  let(:hash_variable) { { "number" => "1234567890", "name" => "John Doe" } }
  let(:url) { "#{wati_end_point}?whatsappNumber=911234567890" }
  let(:api_key) { 'fake_api_key' }

  before do
    allow(Constants).to receive(:get_send_message_wati_url).and_return(wati_end_point)
    allow(Rails.application.credentials).to receive(:[]).with(:wati_api_key).and_return(api_key)
  end

  it 'successfully sends a WhatsApp message' do
    stub_request(:post, url).to_return(status: 200, body: { success: true }.to_json,
                                       headers: { 'Content-Type' => 'application/json' })

    expect {
      SendWhatsappMsg.new.perform(template_name, broadcast_name, hash_variable)
    }.not_to raise_error

    expect(WebMock).to have_requested(:post, url).with(
      body: {
        broadcast_name: broadcast_name,
        template_name: template_name,
        parameters: [
          { name: "number", value: "1234567890" },
          { name: "name", value: "John Doe" }
        ]
      }.to_json,
      headers: {
        'Content-Type' => 'application/json-patch+json',
        'Authorization' => api_key
      }
    )
  end

  it 'raises an error if the phone number is not 10 digits' do
    hash_variable["number"] = "123456789"

    expect {
      SendWhatsappMsg.new.perform(template_name, broadcast_name, hash_variable)
    }.to raise_error("Number is not 10 digits")
  end

  it 'raises an error if the URL cannot be formed' do
    allow(Constants).to receive(:get_send_message_wati_url).and_return(nil)

    expect {
      SendWhatsappMsg.new.perform(template_name, broadcast_name, hash_variable)
    }.to raise_error("Unable to form URL")
  end

  it 'notifies Honeybadger if the response is not 200' do
    stub_request(:post, url).to_return(status: 401, body: { success: false }.to_json, headers: { 'Content-Type' => 'application/json' })
    allow(Honeybadger).to receive(:notify)
    SendWhatsappMsg.new.perform(template_name, broadcast_name, hash_variable)
    expect(Honeybadger).to have_received(:notify).with(
      "WATI API call failed:",
      context: { response_body: { 'success' => false }, response_code: "401" }
    )
  end

  it 'notifies Honeybadger if the response is 200 and body return false and valid whatsapp should be true' do
    stub_request(:post, url).to_return(status: 200, body: { success: false, validWhatsAppNumber: true }.to_json, headers: { 'Content-Type' => 'application/json' })
    allow(Honeybadger).to receive(:notify)
    SendWhatsappMsg.new.perform(template_name, broadcast_name, hash_variable)
    expect(Honeybadger).to have_received(:notify).with(
      "WATI API call failed:",
      context: { response_body: { 'success' => false, 'validWhatsAppNumber' => true }, response_code: "200" }
    )
  end
end
