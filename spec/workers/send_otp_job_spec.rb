require 'rails_helper'

RSpec.describe SendOtpJob, type: :worker do
  let(:instance) { described_class.new }
  context "send otp" do
    it "is processed in critical queue" do
      expect(described_class.sidekiq_options_hash["queue"]).to eq(:critical)
    end

    it "has retry count of 1" do
      expect(described_class.sidekiq_options_hash["retry"]).to eq(1)
    end
  end

  context "perform" do
    it "sends otp via text if count is 1" do
      user = FactoryBot.create(:user)
      phone = user.phone
      code = 1234
      otp_count = 1
      app_hash = 'app_hash'

      allow(Rails.env).to receive(:production?).and_return(true)
      expect(instance).to receive(:send_2factor_otp).and_call_original
      allow(Net::HTTP).to receive(:post_form).with(any_args).and_return(true)
      instance.perform(user.id, phone, code, otp_count, app_hash)
    end

    it "sends otp via 2factor if count is 2" do
      user = FactoryBot.create(:user)
      phone = user.phone
      code = 1234
      otp_count = 2
      app_hash = 'app_hash'

      allow(Rails.env).to receive(:production?).and_return(true)
      expect(instance).to receive(:send_2factor_otp).and_call_original
      allow(Net::HTTP).to receive(:post_form).with(any_args).and_return(true)
      instance.perform(user.id, phone, code, otp_count, app_hash)
    end

    it "sends otp via voice if count is 3" do
      user = FactoryBot.create(:user)
      phone = user.phone
      code = 1234
      otp_count = 3
      app_hash = 'app_hash'

      allow(Rails.env).to receive(:production?).and_return(true)
      expect(instance).to receive(:send_voice_otp).and_call_original
      otp = instance_double(String)
      client = instance_double(Twilio::REST::Client)
      allow(CGI).to receive(:escape).with(any_args).and_return(otp)
      allow(Twilio::REST::Client).to receive(:new).with(any_args).and_return(client)
      allow(client).to receive_message_chain(:calls, :create).with(any_args).and_return(true)
      instance.perform(user.id, phone, code, otp_count, app_hash)
    end

    it "sends otp via text by a post api request" do
      user = FactoryBot.create(:user)
      phone = user.phone
      code = 1234
      otp_count = 1
      app_hash = 'app_hash'

      allow(Net::HTTP).to receive(:post_form).with(any_args).and_return(true)
      instance.perform(user.id, phone, code, otp_count, app_hash)
    end

    it "sends otp via 2factor by a post api request" do
      user = FactoryBot.create(:user)
      phone = user.phone
      code = 1234
      otp_count = 2
      app_hash = 'app_hash'

      allow(Net::HTTP).to receive(:post_form).with(any_args).and_return(true)
      instance.perform(user.id, phone, code, otp_count, app_hash)
    end

    it "sends otp via voice by a post api request" do
      user = FactoryBot.create(:user)
      phone = user.phone
      code = 1234
      otp_count = 3
      app_hash = 'app_hash'

      otp = instance_double(String)
      client = instance_double(Twilio::REST::Client)
      allow(CGI).to receive(:escape).with(any_args).and_return(otp)
      allow(Twilio::REST::Client).to receive(:new).with(any_args).and_return(client)
      allow(client).to receive_message_chain(:calls, :create).with(any_args).and_return(true)
      instance.perform(user.id, phone, code, otp_count, app_hash)
    end
  end
end
