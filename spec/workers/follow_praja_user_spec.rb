require 'rails_helper'

RSpec.describe FollowPrajaUser, type: :worker do
  describe '#perform' do
    context 'with sidekiq options' do
      it { is_expected.to be_retryable 1 }
      it { is_expected.to be_processed_in :default }
    end

    context 'does not create a UserFollower record' do
      let(:user) { FactoryBot.create(:user) }
      let(:user2) { FactoryBot.create(:user, status: :pre_signup) }

      it 'with invalid user_id' do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
        allow(UserFollower).to receive(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto)

        described_class.new.perform(nil)
        expect(UserFollower).not_to have_received(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto)
      end

      it 'with environment other than production' do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('development'))
        allow(UserFollower).to receive(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto)

        described_class.new.perform(user.id)
        expect(UserFollower).not_to have_received(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto)
      end

      it 'with user not signed up' do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
        allow(UserFollower).to receive(:create).with(user_id: 41, follower_id: user2.id, source_of_follow: :auto)

        described_class.new.perform(user2.id)
        expect(UserFollower).not_to have_received(:create).with(user_id: 41, follower_id: user2.id, source_of_follow: :auto)
      end

      it 'with user already following praja' do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
        allow(UserFollower).to receive(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto).and_raise{ActiveRecord::RecordNotUnique}

        expect{ described_class.new.perform(user.id) }.to raise_error
      end
    end

    context 'creates a UserFollower record' do
      let(:user) { FactoryBot.create(:user) }

      it 'with valid user_id' do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
        allow(UserFollower).to receive(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto).and_return(true)

        described_class.new.perform(user.id)
        expect(UserFollower).to have_received(:create).with(user_id: 41, follower_id: user.id, source_of_follow: :auto)
      end
    end
  end
end
