# frozen_string_literal: true
require 'rails_helper'

RSpec.describe UpdateLeadScore, type: :worker do
  it { is_expected.to be_processed_in :update_lead_score }

  describe '#perform' do
    context 'when user is not found' do
      it 'does not trigger SyncMixpanelUser or UpdateLead' do
        user = FactoryBot.create(:user)
        allow(User).to receive(:find).and_return(nil)
        expect(SyncMixpanelUser).to_not receive(:perform_async)
        expect(Floww::UpdateLead).to_not receive(:perform_async)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is found and is poster subscribed with premium layout' do
      it 'does not trigger SyncMixpanelUser or UpdateLead' do
        user = FactoryBot.create(:user)
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:has_premium_layout?).and_return(true)
        expect(SyncMixpanelUser).to_not receive(:perform_async)
        expect(Floww::UpdateLead).to_not receive(:perform_async)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is found and is not poster subscribed or has no premium layout' do
      it 'triggers SyncMixpanelUser and UpdateLead with calculated lead score' do
        user = FactoryBot.create(:user)
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:has_premium_layout?).and_return(false)
        allow(user).to receive(:calculate_lead_score).and_return(10)
        expect(SyncMixpanelUser).to receive(:perform_async).with(user.id, 10)
        expect(Floww::UpdateLead).to receive(:perform_async).with(user.id, 10)
        described_class.new.perform(user.id)
      end
    end
  end
end
