# frozen_string_literal: true

require 'rails_helper'
RSpec.describe LastTrialAttemptSchedulerWorker do
  describe '#perform' do
    let(:user) { create(:user) }
    let(:user_id) { user.id }
    let!(:subscription_charge) { create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :failed) }

    before do
      allow(UpdateLeadScore).to receive(:perform_async)
      allow($redis).to receive(:zadd)
    end

    context 'when the user has never subscribed' do
      it 'updates the lead of the user in Flow' do
        subject.perform(user_id)
        expect(UpdateLeadScore).to have_received(:perform_async).with(user_id)
      end

      it 'calculates the next run time and updates Redis' do
        subject.perform(user_id)
        expect($redis).to have_received(:zadd).with(Constants.update_floww_lead_score_based_on_last_trial_attempt_key, (user.user_last_trial_setup_attempt_time + 7.days + 1.hour).to_i, user_id)
      end
    end

    context 'when the user has ever subscribed' do
      before do
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).and_return(true)
      end

      it 'does not calculate the next run time and update Redis' do
        subject.perform(user_id)
        expect($redis).not_to have_received(:zadd)
      end

      it 'does not update the lead of the user in Flow' do
        subject.perform(user_id)
        expect(UpdateLeadScore).to have_received(:perform_async).with(user_id)
      end
    end
  end
end
