# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueueFrameViews, type: :worker do
  context "when worker runs" do
    it { is_expected.to be_processed_in :default }
    it { is_expected.to be_retryable 3 }
  end

  context "frame views enqueued" do
    it "enqueues frame views" do
      user_id = rand(10000)
      frame_id = rand(10000)
      timestamp = Time.zone.now.to_i
      user_date_frame_views_queue = Constants.user_date_frame_views_queue_redis_key(user_id)

      $redis.hdel(Constants.frame_views_redis_key, frame_id.to_s)
      $redis.srem(Constants.frame_views_queue_redis_key, { frame_id: frame_id, user_id: user_id }.to_json)
      $redis.srem(user_date_frame_views_queue, frame_id.to_s)

      described_class.new.perform(user_id, { frame_id => timestamp })

      Rails.logger.debug "frame_views:#{frame_id}"

      expect($redis.exists?(Constants.frame_views_redis_key)).to eq(true)
      expect($redis.hexists(Constants.frame_views_redis_key, frame_id.to_s)).to eq(true)
      expect($redis.exists?(user_date_frame_views_queue)).to eq(true)
      expect($redis.sismember(user_date_frame_views_queue, frame_id.to_s)).to eq(true)
      expect($redis.exists?(Constants.frame_views_queue_redis_key)).to eq(true)
      expect($redis.sismember(Constants.frame_views_queue_redis_key, { frame_id: frame_id, user_id: user_id, viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)).to eq(true)

      $redis.hdel(Constants.frame_views_redis_key, frame_id.to_s)
      $redis.srem(Constants.frame_views_queue_redis_key, { frame_id: frame_id, user_id: user_id, viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)
      $redis.srem(user_date_frame_views_queue, frame_id.to_s)
    end

    it "add premium pitch frame views to redis key" do
      user_id = rand(10000)
      frame_id = 0
      timestamp = Time.zone.now.to_i

      described_class.new.perform(user_id, { frame_id => timestamp })

      expect(IncreasePremiumPitchCountWorker).to have_enqueued_sidekiq_job(user_id)
    end
  end
end
