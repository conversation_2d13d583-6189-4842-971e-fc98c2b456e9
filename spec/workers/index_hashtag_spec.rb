require 'rails_helper'

RSpec.describe IndexHashtag, type: :worker do
  describe "perform" do
    it { is_expected.to be_processed_in :hashtags_indexing }

    it { is_expected.to be_retryable 1 }

    it "returns nil if hashtag is nil" do
      expect(IndexHashtag.new.perform(nil)).to eq(nil)
    end

    it "returns nil if hashtag is not present" do
      expect(IndexHashtag.new.perform("")).to eq(nil)
    end

    context "if hashtag is present" do
      before :each do
        @user = FactoryBot.create(:user)
        @liked_user = FactoryBot.create(:user)

        name = Faker::Lorem.unique.word
        @hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))

        @post = FactoryBot.create(:post, content: "Hello ##{@hashtag.name}", user: @user)
        @opinion_post = FactoryBot.create(:post, content: "Hai ##{@hashtag.name}", parent_post: @post, user: @user)

        @post_hashtag = FactoryBot.create(:post_hashtag, hashtag: @hashtag, post: @post)
        @opinion_post_hashtag = FactoryBot.create(:post_hashtag, hashtag: @hashtag, post: @opinion_post)

        @post_like = FactoryBot.create(:post_like, post: @post, user: @liked_user)
        @opinion_post_like = FactoryBot.create(:post_like, post: @opinion_post, user: @liked_user)

        @hashtag_like = FactoryBot.create(:hashtag_like, hashtag: @hashtag, user: @liked_user)

        hashtag_created_at = @hashtag.created_at.to_i * 1000
        hashtag_like_created_at = @hashtag_like.created_at.to_i * 1000
        post_created_at = @post.created_at.to_i * 1000
        opinion_post_created_at = @opinion_post.created_at.to_i * 1000
        post_like_created_at = @post_like.created_at.to_i * 1000
        opinion_post_like_created_at = @opinion_post_like.created_at.to_i * 1000

        @params = {
          "name": @hashtag.name,
          "trends": [ hashtag_like_created_at ],
          "posts": [ post_created_at, opinion_post_created_at ],
          "posts_trends": [ post_like_created_at, opinion_post_like_created_at ],
          "active": true,
          "likes_count": 5,
          "opinions_count": 2,
          "whatsapp_count": 0,
          "circle_ids": [],
          "post_user_ids": [ @user.id ],
          "post_trends_and_hashtag_position_info": [
            {
              "hashtag_position" => 0,
              "post_id" => @post.id,
              "trends_time_stamps" => [ post_created_at ]
            },
            {
              "hashtag_position" => 0,
              "post_id" => @opinion_post.id,
              "trends_time_stamps" => [ opinion_post_created_at ]
            }
          ]
        }

        @upsert_params = {
          "id": @hashtag.id,
          "name": @hashtag.name,
          "active": true,
          "likes_count": 5,
          "opinions_count": 2,
          "whatsapp_count": 0,
          "trends": [ hashtag_like_created_at ],
          "posts": [ post_created_at, opinion_post_created_at ],
          "posts_trends": [ post_like_created_at, opinion_post_like_created_at ],
          "circle_ids": [],
          "post_user_ids": [ @user.id ],
          "created_at": hashtag_created_at,
          "post_trends_and_hashtag_position_info": [
            {
              "hashtag_position" => 0,
              "post_id" => @post.id,
              "trends_time_stamps" => [ post_created_at ]
            },
            {
              "hashtag_position" => 0,
              "post_id" => @opinion_post.id,
              "trends_time_stamps" => [ opinion_post_created_at ]
            }
          ]
        }
      end

      # note: as source is in quotes moving any line of the code or moving will break the test
      it "perform the index post request" do
        allow(ES_CLIENT)
          .to receive(:method_missing)
          .with(:perform_request,
          "POST",
          "#{EsUtil.get_hashtags_index}/_update/#{@hashtag.id}?retry_on_conflict=1",
          {},
          {
            "script": {
              "source": """
                  ctx._source.name = params.name;
                  ctx._source.likes_count = params.likes_count;
                  ctx._source.opinions_count = params.opinions_count;
                  ctx._source.whatsapp_count = params.whatsapp_count;
                  ctx._source.trends = params.trends;
                  ctx._source.posts = params.posts;
                  ctx._source.posts_trends = params.posts_trends;
                  ctx._source.active = params.active;
                  ctx._source.circle_ids = params.circle_ids;
                  ctx._source.post_user_ids = params.post_user_ids;
                  ctx._source.post_trends_and_hashtag_position_info = params.post_trends_and_hashtag_position_info;
              """,
              "params": @params
            },
            "upsert": @upsert_params
          }
        )

        IndexHashtag.new.perform(@hashtag.id)

        expect(ES_CLIENT)
          .to have_received(:method_missing)
          .with(:perform_request,
          "POST",
          "#{EsUtil.get_hashtags_index}/_update/#{@hashtag.id}?retry_on_conflict=1",
          {},
          {
            "script": {
              "source": """
                  ctx._source.name = params.name;
                  ctx._source.likes_count = params.likes_count;
                  ctx._source.opinions_count = params.opinions_count;
                  ctx._source.whatsapp_count = params.whatsapp_count;
                  ctx._source.trends = params.trends;
                  ctx._source.posts = params.posts;
                  ctx._source.posts_trends = params.posts_trends;
                  ctx._source.active = params.active;
                  ctx._source.circle_ids = params.circle_ids;
                  ctx._source.post_user_ids = params.post_user_ids;
                  ctx._source.post_trends_and_hashtag_position_info = params.post_trends_and_hashtag_position_info;
              """,
              "params": @params
            },
            "upsert": @upsert_params
          }
        )
      end
    end
  end
end
