require 'rails_helper'

RSpec.describe SyncMixpanelUser, type: :worker do
  describe '#perform' do
    context 'sidekiq options as' do
      it { is_expected.to be_processed_in :low }
    end

    context 'calls' do
      let(:mixpanel_tracker_people) { double('mixpanel_tracker_people') }

      it 'set method on mixpanel tracker if user present ' do
        user = FactoryBot.create(:user)
        properties = user.get_mixpanel_hash
        expect($mixpanel_tracker).to receive(:people).and_return(mixpanel_tracker_people)
        expect(mixpanel_tracker_people).to receive(:set).with(user.id, properties)
        described_class.new.perform(user.id)
      end

      it 'does not call set method on mixpanel tracker if user is nil' do
        expect($mixpanel_tracker).not_to receive(:people)
        described_class.new.perform(nil)
      end
    end
  end
end
