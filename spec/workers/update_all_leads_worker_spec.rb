# frozen_string_literal: true

require 'rails_helper'
RSpec.describe UpdateAllLeadsWorker, type: :worker do
  describe '#perform' do
    let(:worker) { described_class.new }

    context 'when there are interested premium pitches' do
      let(:premium_pitches) { create_list(:premium_pitch, 3, status: :interested) }

      before do
        allow(PremiumPitch).to receive(:where).with(status: [:interested, :trail_enabled, :subscribed_no_layout])
                                              .and_return(PremiumPitch)
        allow(PremiumPitch).to receive(:find_in_batches).with(batch_size: 200).and_yield(premium_pitches)
        allow(UpdateLeadScore).to receive(:perform_async)
      end

      it 'triggers UpdateLeadScore for each premium pitch' do
        worker.perform
        premium_pitches.each do |premium_pitch|
          expect(UpdateLeadScore).to have_received(:perform_async).with(premium_pitch.user_id)
        end
      end
    end

    context 'when there are no interested premium pitches' do
      before do
        allow(PremiumPitch).to receive(:where).with(status: [:interested, :trail_enabled, :subscribed_no_layout])
                                              .and_return(PremiumPitch)
        allow(PremiumPitch).to receive(:find_in_batches).with(batch_size: 200).and_yield([])
        allow(UpdateLeadScore).to receive(:perform_async)
      end

      it 'does not trigger UpdateLeadScore' do
        worker.perform
        expect(UpdateLeadScore).not_to have_received(:perform_async)
      end
    end

    context 'when there are more than 200 interested premium pitches' do
      let(:premium_pitches_batch1) { create_list(:premium_pitch, 200, status: :interested) }
      let(:premium_pitches_batch2) { create_list(:premium_pitch, 50, status: :interested) }

      before do
        allow(PremiumPitch).to receive(:where).with(status: [:interested, :trail_enabled, :subscribed_no_layout])
                                              .and_return(PremiumPitch)
        allow(PremiumPitch).to receive(:find_in_batches).with(batch_size: 200).and_yield(premium_pitches_batch1)
                                                        .and_yield(premium_pitches_batch2)
        allow(UpdateLeadScore).to receive(:perform_async)
      end

      it 'triggers UpdateLeadScore for each premium pitch in all batches' do
        worker.perform
        (premium_pitches_batch1 + premium_pitches_batch2).each do |premium_pitch|
          expect(UpdateLeadScore).to have_received(:perform_async).with(premium_pitch.user_id)
        end
      end
    end
  end
end
