require 'rails_helper'

RSpec.describe CancelUserOtherSubscriptions, type: :worker do
  it { is_expected.to be_processed_in :default }
  it { is_expected.to be_retryable 0 }

  context 'when subscription is present' do
    let(:user) { FactoryBot.create(:user) }
    let(:subscription) { FactoryBot.create(:subscription, pg_reference_id: Faker::Lorem.unique.word, user: user, status: :active) }
    let(:another_on_hold_subscription) { FactoryBot.create(:subscription, pg_reference_id: Faker::Lorem.unique.word, user: user, status: :on_hold) }
    let(:another_created_subscription) { FactoryBot.create(:subscription, pg_reference_id: Faker::Lorem.unique.word, user: user, status: :created) }

    it 'cancels other subscriptions' do
      allow_any_instance_of(Subscription).to receive(:cancel).and_return(true)

      another_on_hold_subscription

      expect_any_instance_of(Subscription).to receive(:cancel).with('another subscription activated')

      described_class.new.perform(subscription.id)
      subscription.reload

      expect(subscription.status).to eq('active')
    end

    it 'closes other created subscriptions' do
      allow_any_instance_of(Subscription).to receive(:close!).and_return(true)

      another_created_subscription

      expect_any_instance_of(Subscription).to receive(:close!)

      described_class.new.perform(subscription.id)
      subscription.reload

      expect(subscription.status).to eq('active')
    end
  end
end
