# frozen_string_literal: true
require 'rails_helper'
RSpec.describe UpdateLeadScoreOfContacts do
  describe '#perform' do
    let(:user) { create(:user) }
    let(:user_id) { user.id }
    let(:phone_user) { create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}") }
    let(:phone_user_id) { phone_user.id }
    let!(:user_contact_suggestion) { create(:user_contact_suggestion, user_id: user.id, phone_user_id: phone_user.id,
                                            phone: phone_user.phone) }

    before do
      allow(UserContactSuggestion).to receive(:joins).and_return(UserContactSuggestion)
      allow(UserContactSuggestion).to receive(:select).and_return(UserContactSuggestion)
      allow(UserContactSuggestion).to receive(:where).and_return(UserContactSuggestion)
      allow(UserContactSuggestion).to receive(:find_each).and_yield(user_contact_suggestion)
      allow(UpdateLeadScore).to receive(:perform_async)
    end

    context 'when there are user contact suggestions to process' do
      it 'schedules UpdateLeadScore for each user contact suggestion' do
        subject.perform(user_id)
        expect(UpdateLeadScore).to have_received(:perform_async).with(user_contact_suggestion.phone_user_id)
      end
    end

    context 'when there are no user contact suggestions to process' do
      before do
        allow(UserContactSuggestion).to receive(:find_each).and_yield(nil)
      end

      it 'does not schedule any UpdateLeadScore jobs' do
        subject.perform(user_id)
        expect(UpdateLeadScore).not_to have_received(:perform_async)
      end
    end

    context 'when an exception occurs during processing' do
      before do
        allow(UserContactSuggestion).to receive(:find_each).and_raise(StandardError)
      end

      it 'logs the error' do
        expect { subject.perform(user_id) }.to raise_error(StandardError)
      end
    end
  end
end
