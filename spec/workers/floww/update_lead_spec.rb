# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::UpdateLead, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe '#perform' do
    context 'when user is not a floww contact yet' do
      let(:user) { FactoryBot.create(:user) }

      it 'does not trigger update lead' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return(nil)
        allow(FlowwApi).to receive(:update_lead_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to_not receive(:update_lead_activity)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is a floww contact' do
      let(:user) { FactoryBot.create(:user) }

      it 'triggers update lead with dashboard links' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return('floww_contact_id')
        allow(user).to receive(:active_user_poster_layout).and_return(FactoryBot.create(:user_poster_layout))
        allow(Constants).to receive(:get_admin_host).and_return("http://localhost:3000")
        allow(FlowwApi).to receive(:update_lead_activity)

        expect(user).to receive(:active_user_poster_layout)
        expect(FlowwApi).to receive(:update_lead_activity) do |user_id, fields_hash:|
          expect(fields_hash).to include(:rm_dashboard, :oe_dashboard)
          expect(fields_hash[:rm_dashboard]).to eq("https://jathara.thecircleapp.in/create-layout?user_id=#{user.id}")
          expect(fields_hash[:oe_dashboard]).to eq("http://localhost:3000/admin/users/#{user.hashid}/oe_work_flow")
        end
        described_class.new.perform(user.id)
      end
    end
  end
end
