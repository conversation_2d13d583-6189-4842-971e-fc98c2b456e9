# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::Payment, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe '#perform' do
    let(:user) { FactoryBot.create(:user) }

    context 'when user is not a floww contact yet' do
      it 'does not trigger payment' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return(nil)
        allow(FlowwApi).to receive(:update_mandate_subscribed_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to_not receive(:update_mandate_subscribed_activity)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is a floww contact' do
      it 'triggers payment' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return('floww_contact_id')
        allow(SubscriptionUtils).to receive(:get_subscription_duration_in_months_and_amount).and_return([1, 100])
        allow(FlowwApi).to receive(:update_mandate_subscribed_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(SubscriptionUtils).to receive(:get_subscription_duration_in_months_and_amount)
        expect(FlowwApi).to receive(:update_mandate_subscribed_activity)
        described_class.new.perform(user.id)
      end
    end
  end
end
