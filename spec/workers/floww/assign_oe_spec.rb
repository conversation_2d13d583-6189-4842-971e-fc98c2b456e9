# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::AssignOe, type: :worker do
  describe '#perform' do
    let(:user) { create(:user) }

    before do
      allow_any_instance_of(User).to receive(:get_floww_contact_id).and_return('123')
      allow(FlowwApi).to receive(:update_assign_oe_activity)
    end

    it 'calls update_assign_oe_activity method with correct params' do
      expect(FlowwApi).to receive(:update_assign_oe_activity).with(user.id)

      subject.perform(user.id)
    end

    it 'does not call FlowwApi if user does not exist' do
      expect(FlowwApi).not_to receive(:update_assign_oe_activity)

      subject.perform(-1)
    end

    it 'does not call FlowwApi if user does not have floww_contact_id' do
      allow_any_instance_of(User).to receive(:get_floww_contact_id).and_return(nil)
      expect(FlowwApi).not_to receive(:update_assign_oe_activity)

      subject.perform(user.id)
    end
  end
end
