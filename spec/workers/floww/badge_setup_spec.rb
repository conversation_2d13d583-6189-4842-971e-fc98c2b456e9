# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::BadgeSetup, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe '#perform' do
    let(:user) { FactoryBot.create(:user) }

    context 'when user is not a floww contact yet' do
      it 'does not trigger badge setup' do
        allow(User).to receive(:find_by_id).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return(nil)
        allow(FlowwApi).to receive(:update_badge_setup_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to_not receive(:update_badge_setup_activity)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is a floww contact' do
      it 'triggers badge setup with user_hashid' do
        allow(User).to receive(:find_by_id).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return('floww_contact_id')
        allow(user).to receive(:hashid).and_return('user_hashid')
        allow(FlowwApi).to receive(:update_badge_setup_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to receive(:update_badge_setup_activity).with(user.id, user_hashid: 'user_hashid')
        described_class.new.perform(user.id)
      end
    end
  end
end
