require 'rails_helper'

RSpec.describe GeneratePosterCampaignImage, type: :worker do
  describe '#perform' do
    context 'when user_id or creative_id or campaign_name is missing' do
      subject { described_class.new }

      it 'returns early' do
        expect(subject.perform(nil, nil, nil)).to be_nil
      end
    end

    context 'when user is not found' do
      before :each do
        allow(User).to receive(:find_by_id).and_return(nil)
        allow_any_instance_of(described_class).to receive(:invoke_lambda).and_return(
          double(payload: StringIO.new({ body: '{"poster_url": "https//example.com/poster.jpg"}' }.to_json))
        )
      end

      subject { described_class.new }

      it 'returns early' do
        expect(subject.perform( 1,  1, 'test_campaign')).to be_nil
      end

      it 'return early when user does not have premium layout' do
        user = FactoryBot.create(:user)
        allow(User).to receive(:find_by_id).and_return(user)
        expect(subject.perform(user.id, 1, 'test_campaign')).to be_nil
      end
    end

    context 'poster campaign url cases' do
      let(:mock_lambda_client) { instance_double(Aws::Lambda::Client) }
      let(:user) { FactoryBot.create(:user) }
      before :each do
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @event = FactoryBot.create(:event)
        @poster_creative = FactoryBot.build(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                            photo_v2: @admin_medium_2, primary: true)
        @poster_creative.save!
        @poster_url = @poster_creative.photo_v3.url
        allow(User).to receive(:find_by_id).and_return(user)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
        allow(Aws::Lambda::Client).to receive(:new).and_return(mock_lambda_client)
        allow(mock_lambda_client).to receive(:invoke).and_return(
          double(payload: StringIO.new({ body: { poster_url: @poster_url }.to_json }.to_json))
        )
      end

      subject { described_class.new }

      it "return early if poster creative is not found" do
        allow(PosterCreative).to receive(:find_by).and_return(nil)
        expect(subject.perform(user.id, 1, 'test_campaign')).to be_nil
      end

      it 'poster campaign image URL is present' do
        subject.perform(user.id, @poster_creative.id, 'test_campaign')
        user_metadata = UserMetadatum.find_by(user_id: user.id, key: 'poster_image_url_test_campaign')
        expect(user_metadata&.value).to eq(@poster_url)
      end

      it 'returns early if user metadatum already exists' do
        UserMetadatum.create(user_id: user.id, key: 'poster_image_url_test_campaign',
                             value: 'https://example.com/poster.jpg')
        expect(subject.perform(user.id, @poster_creative.id, 'test_campaign')).to be_nil
      end

      it 'returns early if poster url is missing' do
        allow_any_instance_of(described_class).to receive(:invoke_lambda).and_return(
          double(payload: StringIO.new({ body: { }.to_json }.to_json))
        )
        expect(subject.perform(user.id, @poster_creative.id, 'test_campaign')).to be_nil
      end

      it 'returns early if invoke lambda fails' do
        allow_any_instance_of(described_class).to receive(:invoke_lambda).and_raise(StandardError)
        expect(subject.perform(user.id, @poster_creative.id, 'test_campaign')).to be_nil
      end
    end
  end
end
