require 'rails_helper'
require 'webmock/rspec'

RSpec.describe DmUtil do

  describe '#authorize_user_to_create_conversation' do
    context 'checking user authorization to create one to one conversation for comment blocking sender and other block conditions user' do
      before :each do
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @political_circle = FactoryBot.create(:circle)
        @political_circle_2 = FactoryBot.create(:circle)
        @political_circle_3 = FactoryBot.create(:circle)
        stub_request(:put, Constants.get_dm_url + '/conversations/block-user')
          .to_return(status: 200, body: '', headers: {})
        stub_request(:post, "https://api.mixpanel.com/track").to_return(status: 200, body: "", headers: {})
        @user = FactoryBot.create(:user, village_id: @village_circle.id)
        @recipient1 = FactoryBot.create(:user)
        @recipient2 = FactoryBot.create(:user)
        @recipient3 = FactoryBot.create(:user)
        @recipient4 = FactoryBot.create(:user)
        @recipient5 = FactoryBot.create(:user)
        @recipient6 = FactoryBot.create(:user)
      end

      it 'should be requested as sender is blocked for commenting' do
        allow_any_instance_of(User).to receive(:is_blocked_for_commenting?).and_return(true)
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:requested])
        expect(auth_info[:parameters][:reason]).to eq(DmUtil::AUTH_STATUS[:blocked_for_commenting])
      end

      it "should be banned as sender is blocked by #{OneToOneConvModeration::NUMBER_OF_BLOCKS_TO_CHECK_TO_BAN_NORMAL_USERS} && have #{OneToOneConvModeration::NUMBER_OF_FOLLOWERS_TO_CHECK_FOR_BANNING_WITH_BLOCKS} followers" do
        # making user block other user
        OneToOneConvModeration::NUMBER_OF_BLOCKS_TO_CHECK_TO_BAN_NORMAL_USERS.times do
          FactoryBot.create(:blocked_user, user: FactoryBot.create(:user), blocked_user: @user, reason: "chat_block")
        end

        OneToOneConvModeration::NUMBER_OF_FOLLOWERS_TO_CHECK_FOR_BANNING_WITH_BLOCKS - 1.times do
          FactoryBot.create(:user_follower, user: @user, follower: FactoryBot.create(:user))
        end

        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:banned])
        expect(auth_info[:parameters][:banning_reasons]).to eq(DmUtil::BANNING_REASONS[:is_blocked_by_multiple_users])
      end
    end

    context 'checking user authorization to create one to one conversation for users who are grade 1 and grade 2 in between them' do
      before :each do
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @political_circle = FactoryBot.create(:circle)
        @political_circle_2 = FactoryBot.create(:circle)
        @political_circle_3 = FactoryBot.create(:circle)
        stub_request(:put, Constants.get_dm_url + '/conversations/block-user')
          .to_return(status: 200, body: '', headers: {})
        stub_request(:post, "https://api.mixpanel.com/track").to_return(status: 200, body: "", headers: {})
        @user = FactoryBot.create(:user, village_id: @village_circle.id)
        @recipient1 = FactoryBot.create(:user)
        @recipient2 = FactoryBot.create(:user)
        @recipient3 = FactoryBot.create(:user)
        @recipient4 = FactoryBot.create(:user)
        @recipient5 = FactoryBot.create(:user)
        @recipient6 = FactoryBot.create(:user)
        # making loggedin user role as grade 2
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  active: true)
        @role1 = FactoryBot.create(:role,
                                   name: Faker::Name.unique.name,
                                   has_badge: true,
                                   badge_ring: true,
                                   badge_color: :GOLD,
                                   quota_type: :absolute,
                                   quota_value: 2,
                                   grade_level: :grade_1,
                                   parent_circle_level: :political_party,
                                   active: true)
        @role2 = FactoryBot.create(:role,
                                   name: Faker::Name.unique.name,
                                   has_badge: true,
                                   badge_ring: true,
                                   badge_color: :GOLD,
                                   quota_type: :absolute,
                                   quota_value: 2,
                                   grade_level: :grade_1,
                                   parent_circle_level: :political_party,
                                   active: true)
        @role3 = FactoryBot.create(:role,
                                   name: Faker::Name.unique.name,
                                   has_badge: true,
                                   badge_ring: true,
                                   badge_color: :GOLD,
                                   quota_type: :absolute,
                                   quota_value: 2,
                                   grade_level: :grade_1,
                                   parent_circle_level: :political_party,
                                   active: true)

        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @political_circle.id)
        FactoryBot.create(:user_role, user: @recipient1, role: @role1, parent_circle_id: @political_circle.id)
        FactoryBot.create(:user_role, user: @recipient2, role: @role2, parent_circle_id: @political_circle.id)
        FactoryBot.create(:user_role, user: @recipient3, role: @role3, parent_circle_id: @political_circle.id)
        FactoryBot.create(:user_role, user: @recipient4, role: @role1, parent_circle_id: @political_circle_2.id)
        FactoryBot.create(:user_role, user: @recipient5, role: @role2, parent_circle_id: @political_circle_2.id)
        FactoryBot.create(:user_role, user: @recipient6, role: @role3, parent_circle_id: @political_circle_2.id)

        # making user block other user
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient2, reason: "chat_block")

        # making both users block each other
        FactoryBot.create(:blocked_user, user: @recipient3, blocked_user: @user, reason: "chat_block")
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient3, reason: "chat_block")

        # making user being blocked by other user
        FactoryBot.create(:blocked_user, user: @recipient4, blocked_user: @user, reason: "chat_block_and_report")

        # making recipient follow user
        FactoryBot.create(:user_follower, user: @user, follower: @recipient5)
      end

      it 'user should be allowed as users have common circle' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user should be receiver_blocked as recipient is blocked by user' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient2.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:receiver_blocked])
      end

      it 'user should be both_blocked as both users are blocked by each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient3.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:both_blocked])
      end

      it 'user should be sender_blocked as user is blocked by recipient' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient4.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:sender_blocked])
      end

      it 'sender should be allowed as receiver is following him' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient5.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'sender should be allowed if he is grade 1 or 2 and  if they are not blocked' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient6.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

    end

    context 'checking user authorization to create one to one conversation for users who are grade 1 and grade 2 with normal users' do
      before :each do
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @political_circle = FactoryBot.create(:circle)
        @political_circle_2 = FactoryBot.create(:circle)
        @political_circle_3 = FactoryBot.create(:circle)
        stub_request(:put, Constants.get_dm_url + '/conversations/block-user')
          .to_return(status: 200, body: '', headers: {})
        stub_request(:post, "https://api.mixpanel.com/track").to_return(status: 200, body: "", headers: {})
        @user = FactoryBot.create(:user, village_id: @village_circle.id)
        @recipient1 = FactoryBot.create(:user)
        @recipient2 = FactoryBot.create(:user)
        @recipient3 = FactoryBot.create(:user)
        @recipient4 = FactoryBot.create(:user)
        @recipient5 = FactoryBot.create(:user)
        @recipient6 = FactoryBot.create(:user)
        @recipient1.circles << @political_circle
        # making loggedin user role as grade 2
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  active: true)
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @political_circle.id)

        # making user block other user
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient2, reason: "chat_block_and_report")

        # making both users block each other
        FactoryBot.create(:blocked_user, user: @recipient3, blocked_user: @user, reason: "chat_block_and_report")
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient3, reason: "chat_block_and_report")

        # making user being blocked by other user
        FactoryBot.create(:blocked_user, user: @recipient4, blocked_user: @user, reason: "chat_block_and_report")

        # making recipient follow user
        FactoryBot.create(:user_follower, user: @user, follower: @recipient5)
      end

      it 'sender should be allowed as users have common circle' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user should be receiver_blocked as recipient is blocked by user' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient2.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:receiver_blocked])
      end

      it 'user should be both_blocked as both users are blocked by each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient3.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:both_blocked])
      end

      it 'user should be sender_blocked as user is blocked by recipient' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient4.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:sender_blocked])
      end

      it 'sender should be allowed as sender is higher grade' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient5.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

    end

    context 'checking user authorization to create one to one conversation for users who are normal users with grade 1 and grade 2 users' do
      before :each do
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @political_circle = FactoryBot.create(:circle)
        @political_circle_2 = FactoryBot.create(:circle)
        @political_circle_3 = FactoryBot.create(:circle)
        stub_request(:put, Constants.get_dm_url + '/conversations/block-user')
          .to_return(status: 200, body: '', headers: {})
        stub_request(:post, "https://api.mixpanel.com/track").to_return(status: 200, body: "", headers: {})
        @user = FactoryBot.create(:user, village_id: @village_circle.id)
        @recipient1 = FactoryBot.create(:user)
        @recipient2 = FactoryBot.create(:user)
        @recipient3 = FactoryBot.create(:user)
        @recipient4 = FactoryBot.create(:user)
        @recipient5 = FactoryBot.create(:user)
        @recipient6 = FactoryBot.create(:user)
        @recipient1.circles << @political_circle
        # making loggedin user role as grade 2
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  active: true)
        FactoryBot.create(:user_role, user: @recipient1, role: @role, parent_circle_id: @political_circle.id)

        # making user block other user
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient2, reason: "chat_block_and_report")

        # making both users block each other
        FactoryBot.create(:blocked_user, user: @recipient3, blocked_user: @user, reason: "chat_block_and_report")
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient3, reason: "chat_block_and_report")

        # making user being blocked by other user
        FactoryBot.create(:blocked_user, user: @recipient4, blocked_user: @user, reason: "chat_block_and_report")

        # making recipient follow user
        FactoryBot.create(:user_follower, user: @user, follower: @recipient5)
      end

      it 'sender should be requested as users have common circle but recipient is higher grade user' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:requested])
      end

      it 'user should be receiver_blocked as recipient is blocked by user' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient2.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:receiver_blocked])
      end

      it 'user should be both_blocked as both users are blocked by each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient3.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:both_blocked])
      end

      it 'user should be sender_blocked as user is blocked by recipient' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient4.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:sender_blocked])
      end

      it 'sender should be allowed as receiver is following him' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient5.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user able to request if they are not blocked, have no common circles and not following each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient6.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:requested])
      end

    end

    context 'checking user authorization to create one to one conversation for users who are not grade 1 and grade 2' do
      before :each do
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @village_circle2 = FactoryBot.create(:circle, name: 'village_circle2', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @political_circle = FactoryBot.create(:circle)
        stub_request(:put, Constants.get_dm_url + '/conversations/block-user')
          .to_return(status: 200, body: '', headers: {})
        @user = FactoryBot.create(:user, village_id: @village_circle.id)
        @recipient1 = FactoryBot.create(:user, village_id: @village_circle.id)
        @recipient2 = FactoryBot.create(:user, village_id: @village_circle2.id)
        @recipient3 = FactoryBot.create(:user, village_id: @village_circle2.id)
        @recipient4 = FactoryBot.create(:user)
        @recipient5 = FactoryBot.create(:user)
        @recipient6 = FactoryBot.create(:user)
        @recipient7 = FactoryBot.create(:user)

        fake_number = "91#{Faker::Number.unique.number(digits: 8)}"
        @recipient8 = FactoryBot.create(:user, phone: fake_number.to_i)

        FactoryBot.create(:user_contact_suggestion, user: @recipient8, phone_user_id: @user.id, phone: @recipient8.phone)

        @user.circles << @political_circle
        @recipient2.circles << @political_circle

        # making user block other user
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient4, reason: "chat_block")

        # making both users block each other
        FactoryBot.create(:blocked_user, user: @recipient6, blocked_user: @user, reason: "chat_block_and_report")
        FactoryBot.create(:blocked_user, user: @user, blocked_user: @recipient6, reason: "chat_block_and_report")

        # making user being blocked by other user
        FactoryBot.create(:blocked_user, user: @recipient7, blocked_user: @user, reason: "chat_block_and_report")

        # making recipient follow user
        FactoryBot.create(:user_follower, user: @user, follower: @recipient5)
      end

      it 'user should be allowed as users are in same village' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user should be allowed as users have common circle' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient2.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user should be allowed as users are following each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient5.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user should be allowed as user is from the recipient contacts' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient8.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:allowed])
      end

      it 'user able to request if they are not blocked, have no common circles or village and not following each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient3.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:requested])
      end

      it 'user should be receiver_blocked as recipient is blocked by user' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient4.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:receiver_blocked])
      end

      it 'user should be sender_blocked as user is blocked by recipient' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient7.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:sender_blocked])
      end

      it 'user should be both_blocked as both users are blocked by each other' do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient6.id)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:both_blocked])
      end

      it "user should be banned as he created more than or equal to #{OneToOneConvModeration::NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_BAN_CONV_CREATION} conversations with in 24 hrs" do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED - 1, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED - 1,
                                                                            OneToOneConvModeration::NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_BAN_CONV_CREATION - 1,
                                                                            OneToOneConvModeration::NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_BAN_CONV_CREATION + 1)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:banned])
      end

      it "user should be banned as he created more than or equal to #{OneToOneConvModeration::NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_BAN_CONV_CREATION} conversations with in 7 days" do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED - 1, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED - 1,
                                                                            OneToOneConvModeration::NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_BAN_CONV_CREATION + 1,
                                                                            OneToOneConvModeration::NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_BAN_CONV_CREATION - 1)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:banned])
      end

      it "user should be requested as he created more than or equal to #{OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED} conversations with in 24 hrs" do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED - 1, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED + 1)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:requested])
      end

      it "user should be requested as he created more than or equal to #{OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED} conversations with in 7 days" do
        auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, @recipient1.id, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED + 1, OneToOneConvModeration::NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED - 1)
        expect(auth_info[:status]).to eq(DmUtil::AUTH_STATUS[:requested])
      end
    end

    context 'checking  DmUtil.blockedInfoCallBack function for making blocked data api call to dm service' do

      it 'perform should return nil if request params are nil' do
        result = DmUtil.blocked_info_callback_to_dm('some_url', nil)
        expect(result).to be_nil
      end

      it 'perform should return nil if url is empty' do
        result = DmUtil.blocked_info_callback_to_dm(nil, {})
        expect(result).to be_nil
      end

      it 'perform should return response if request params are valid' do
        url = Constants.get_dm_url + '/conversations/block-user'
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        req_body = { "blocked_data": { "userId": 123, "blockedUserId": 456 } }
        response = DmUtil.blocked_info_callback_to_dm(url, req_body)
        expect(response).not_to be_nil
        expect(response).to eq(true)
      end

      it 'perform should raise error if response code is not 200' do
        url = Constants.get_dm_url + '/conversations/block-user'
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
        req_body = {
          "blocked_user_id": 456,
          "user_id": 123
        }
        expect { DmUtil.blocked_info_callback_to_dm(url, req_body) }.to raise_error(RuntimeError, 'DM Service blocked data HTTP request failed with status code 500')
      end
    end
  end

  describe '#join callback' do
    context 'send user join info to dm service' do
      it 'return nil if circle_id is nil' do
        result = DmUtil.send_user_join_callback_to_dm_service(nil, 1)
        expect(result).to be_nil
      end

      it 'return nil if user_id is nil' do
        result = DmUtil.send_user_join_callback_to_dm_service(1, nil)
        expect(result).to be_nil
      end

      it 'return nil if circle_id and user_id are nil' do
        result = DmUtil.send_user_join_callback_to_dm_service(nil, nil)
        expect(result).to be_nil
      end

      it 'calls put request to dm service if circle id and user id are present' do
        circle = FactoryBot.create(:circle)
        user = FactoryBot.create(:user)
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        result = DmUtil.send_user_join_callback_to_dm_service(circle.id, user.id)

        req_body = { userId: user.id.to_s, circleId: circle.id.to_s, membersCount: circle.members_count }
        req_url = Constants.get_dm_url + "/conversations/join-circle-conversation"
        expect(DmUtil).to have_received(:put_request_to_dm).with(req_url, req_body)
        expect(result).to eq(true)
      end
    end
  end

  describe '#unjoin callback' do
    context 'send user un join info to dm service' do
      it 'return nil if circle_id is nil' do
        result = DmUtil.send_user_unjoin_callback_to_dm_service(nil, 1)
        expect(result).to be_nil
      end

      it 'return nil if user_id is nil' do
        result = DmUtil.send_user_unjoin_callback_to_dm_service(1, nil)
        expect(result).to be_nil
      end

      it 'return nil if circle_id and user_id are nil' do
        result = DmUtil.send_user_unjoin_callback_to_dm_service(nil, nil)
        expect(result).to be_nil
      end

      it 'calls put request to dm service if circle id and user id are present' do
        circle = FactoryBot.create(:circle)
        user = FactoryBot.create(:user)
        allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
        result = DmUtil.send_user_unjoin_callback_to_dm_service(circle.id, user.id)

        req_body = { userId: user.id.to_s, circleId: circle.id.to_s, membersCount: circle.members_count }
        req_url = Constants.get_dm_url + "/conversations/leave-circle-conversation"
        expect(DmUtil).to have_received(:put_request_to_dm).with(req_url, req_body)
        expect(result).to eq(true)
      end
    end
  end

  describe '#put_request_to_dm' do
    it 'return nil if url is nil' do
      result = DmUtil.put_request_to_dm(nil, {})
      expect(result).to be_nil
    end

    it 'return nil if request body is nil' do
      result = DmUtil.put_request_to_dm('some_url', nil)
      expect(result).to be_nil
    end

    it 'return nil if url and request body are nil' do
      result = DmUtil.put_request_to_dm(nil, nil)
      expect(result).to be_nil
    end

    it 'calls put request to dm service if url and request body are present' do
      req_url = Constants.get_dm_url + '/conversations/api'
      req_body = "some_body"

      stub_request(:put, req_url)
        .to_return(status: 200, body: { success: true }.to_json, headers: { "Content-Type" => "application/json", "Authorization" => "Bearer #{JsonWebToken.get_token_to_send_dm_service}" })

      response = DmUtil.put_request_to_dm(req_url, req_body)
      expect(response).not_to be_nil
    end
  end

  describe '#post_request_to_dm' do
    it 'return nil if url is nil' do
      result = DmUtil.post_request_to_dm(nil, {})
      expect(result).to be_nil
    end

    it 'return nil if request body is nil' do
      result = DmUtil.post_request_to_dm('some_url', nil)
      expect(result).to be_nil
    end

    it 'return nil if url and request body are nil' do
      result = DmUtil.post_request_to_dm(nil, nil)
      expect(result).to be_nil
    end

    it 'calls post request to dm service if url and request body are present' do
      req_url = Constants.get_dm_url + '/conversations/api'
      req_body = {
        userIds: ["123", "456"],
      }
      token = JsonWebToken.get_token_to_send_dm_service

      stub_request(:post, req_url)
        .to_return(status: 201, body: { success: true }.to_json, headers: { "Content-Type" => "application/json", "Authorization" => "Bearer #{token}" })

      response = DmUtil.post_request_to_dm(req_url, req_body)
      expect(response).not_to be_nil
    end

    it 'calls post request to dm service if url and request body are present and request is to create message in dm' do
      circle_id = 1234
      user_id = 123
      req_url = Constants.get_dm_url + '/conversations/api'
      req_body = {
        circleId: circle_id.to_s,
        sentAt: Time.zone.now.to_s,
        messageData: {
          senderId: user_id.to_s,
          text: "Hi",
          attachments: [
            {
              type: 'CREATE_POSTER',
              attachmentData: {
                params: {
                  creative_id: 345,
                  circle_id: circle_id,
                  category_kind: 'info'
                }
              }
            }
          ]
        }
      }
      token = JsonWebToken.get_token_to_send_dm_service
      user_jwt_token = User.generate_user_jwt_token(user_id)
      response = DmUtil.post_request_to_dm(req_url, req_body)
      expect(response).not_to be_nil
    end
  end

  describe '#send_circle_permission_group_update_to_dm' do
    it 'return nil if circle_id is nil' do
      result = DmUtil.send_circle_permission_group_update_to_dm(nil)
      expect(result).to be_nil
    end

    it 'calls post request to dm service if circle id and members_count are present and on success return true' do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      result = DmUtil.send_circle_permission_group_update_to_dm([1, 2, 3])
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls post request to dm service if circle id and members_count are present and on failure return false' do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.send_circle_permission_group_update_to_dm([1, 2, 3])
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(false)
    end
  end

  describe "#create_channel_callback_to_dm" do
    it 'return nil if circle_id is nil' do
      result = DmUtil.create_channel_callback_to_dm(nil, 1)
      expect(result).to be_nil
    end

    it 'return nil if members_count is nil' do
      result = DmUtil.create_channel_callback_to_dm(1, nil)
      expect(result).to be_nil
    end

    it 'return nil if circle_id and members_count are nil' do
      result = DmUtil.create_channel_callback_to_dm(nil, nil)
      expect(result).to be_nil
    end

    it 'calls post request to dm service if circle id and members_count are present and on success return true' do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      result = DmUtil.create_channel_callback_to_dm(1, 2)
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls post request to dm service if circle id and members_count are present and on failure return false' do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.create_channel_callback_to_dm(1, 2)
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(false)
    end

    it 'calls post request to dm service if circle id and members_count are present and on error raises returns false' do
      allow(DmUtil).to receive(:post_request_to_dm).and_raise(StandardError)
      result = DmUtil.create_channel_callback_to_dm(1, 2)
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(false)
    end
  end
  describe "#create_private_group_callback_to_dm" do
    it 'return nil if circle_id is nil' do
      result = DmUtil.create_private_group_callback_to_dm(nil, 1)
      expect(result).to be_nil
    end

    it 'return nil if members_count is nil' do
      result = DmUtil.create_private_group_callback_to_dm(1, nil)
      expect(result).to be_nil
    end

    it 'return nil if circle_id and members_count are nil' do
      result = DmUtil.create_private_group_callback_to_dm(nil, nil)
      expect(result).to be_nil
    end

    it 'calls post request to dm service if circle id and members_count are present and on success return true' do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      result = DmUtil.create_private_group_callback_to_dm(1, 2)
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls post request to dm service if circle id and members_count are present and on failure return false' do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.create_private_group_callback_to_dm(1, 2)
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(false)
    end

    it 'calls post request to dm service if circle id and members_count are present and on error raises returns false' do
      allow(DmUtil).to receive(:post_request_to_dm).and_raise(StandardError)
      result = DmUtil.create_private_group_callback_to_dm(1, 2)
      expect(DmUtil).to have_received(:post_request_to_dm)
      expect(result).to eq(false)
    end
  end

  describe  "#disable_circle_conversations_callback_to_dm" do

    it 'return nil if circle_id is nil' do
      result = DmUtil.disable_circle_conversations_callback_to_dm(nil)
      expect(result).to be_nil
    end

    it 'calls put request to dm service if circle id is present and on success return true' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
      result = DmUtil.disable_circle_conversations_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls put request to dm service if circle id is present and on failure return false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.disable_circle_conversations_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end

    it 'calls put request to dm service if circle id is present and on error raises returns false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_raise(StandardError)
      result = DmUtil.disable_circle_conversations_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end
  end

  describe "#disable_channel_callback_to_dm" do
    it 'return nil if circle_id is nil' do
      result = DmUtil.disable_channel_callback_to_dm(nil)
      expect(result).to be_nil
    end

    it 'calls put request to dm service if circle id is present and on success return true' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
      result = DmUtil.disable_channel_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls put request to dm service if circle id is present and on failure return false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.disable_channel_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end

    it 'calls put request to dm service if circle id is present and on error raises returns false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_raise(StandardError)
      result = DmUtil.disable_channel_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end
  end

  describe "#disable_private_group_callback_to_dm" do
    it 'return nil if circle_id is nil' do
      result = DmUtil.disable_private_group_callback_to_dm(nil)
      expect(result).to be_nil
    end

    it 'calls put request to dm service if circle id is present and on success return true' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
      result = DmUtil.disable_private_group_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls put request to dm service if circle id is present and on failure return false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.disable_private_group_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end

    it 'calls put request to dm service if circle id is present and on error raises returns false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_raise(StandardError)
      result = DmUtil.disable_private_group_callback_to_dm(1)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end
  end

  describe '#send_user_circle_permission_update_to_dm' do
    it 'return nil if circle_id is nil' do
      result = DmUtil.send_user_circle_permission_update_to_dm(nil, 1)
      expect(result).to be_nil
    end

    it 'calls post request to dm service if circle id and members_count are present and on success return true' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
      result = DmUtil.send_user_circle_permission_update_to_dm(1, 2)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(true)
    end

    it 'calls post request to dm service if circle id and members_count are present and on failure return false' do
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 500, body: { success: false }.to_json))
      result = DmUtil.send_user_circle_permission_update_to_dm(1, 2)
      expect(DmUtil).to have_received(:put_request_to_dm)
      expect(result).to eq(false)
    end
  end
end
