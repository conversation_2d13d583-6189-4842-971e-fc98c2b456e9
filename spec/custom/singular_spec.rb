# frozen_string_literal: true

require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Singular do
  let(:cache) { Rails.cache }
  describe 'disabled Rails cache' do
    before do
      @original_cache = Rails.cache
      allow(Rails).to receive(:cache).and_return(ActiveSupport::Cache.lookup_store(:memory_store))
      allow_any_instance_of(ActiveSupport::Cache::MemoryStore).to receive(:exist?).and_return(false)
    end
    after do
      allow(Rails).to receive(:cache).and_return(@original_cache)
    end
    context 'success: when a long link is given' do
      before do
        stub_request(:post, "https://s2s.singular.net/api/v1/s2s/shorten_link?a=#{Rails.application.credentials[:singular_sdk_api_key]}")
          .to_return(status: 200, body: '{"short_link":"https://praja.buzz"}', headers: {"Content-Type" => "application/json"})
      end

      it 'return a short link' do
        long_link = 'https://praja.buzz/long-link'

        short_link = Singular.shorten_link(long_link)
        expect(short_link).to eq('https://praja.buzz')
      end
    end
    context 'failed: when a long link is given' do
      before do
        stub_request(:post, "https://s2s.singular.net/api/v1/s2s/shorten_link?a=#{Rails.application.credentials[:singular_sdk_api_key]}")
          .to_return(status: 400, body: '{"error":"bad request"}', headers: {"Content-Type" => "application/json"})
      end

      it 'return the long link itself' do
        long_link = 'https://praja.buzz/long-link'

        short_link = Singular.shorten_link(long_link)
        expect(short_link).to eq(long_link)
      end
    end
  end
end
