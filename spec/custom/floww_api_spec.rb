# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FlowwApi do
  before :all do
    travel_to(Time.zone.now)
  end

  describe '.update_trial_enabled_activity' do
    let(:user_id) { 1 }
    let(:trial_duration_in_days) { 30 }
    let(:app_installed) { true }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      trial_end_date = (Time.zone.now + (trial_duration_in_days - 1).days).strftime("%Y-%m-%d")
      payload = {
        "activity_reference_id": "Trial_Enabled",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          },
          {
            "field_reference_id": "Trial_Enabled_Status",
            "value": 'Yes'
          },
          {
            "field_reference_id": "App_Install_Status",
            "value": app_installed ? 'Installed' : 'Uninstalled'
          },
          {
            "field_reference_id": "Trial_Period",
            "value": "#{trial_duration_in_days}"
          },
          {
            "field_reference_id": "c_role_name",
            "value": ""
          },
          {
            "field_reference_id": "c_role_id",
            "value": ""
          },
          {
            "field_reference_id": "trial_end_date",
            "value": "#{trial_end_date}"
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_trial_enabled_activity(user_id, trial_duration_in_days: trial_duration_in_days, trial_end_date: trial_end_date, app_installed: app_installed, role_name: "", role_id: "")
    end
  end

  describe '.update_paid_activity' do
    let(:user_id) { 1 }
    let(:subscription_duration_in_months) { 12 }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "Payment_activity",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          },
          {
            "field_reference_id": "Subscription_Period",
            "value": "#{subscription_duration_in_months}"
          },
          {
            "field_reference_id": "Payment_Status",
            "value": "Yes"
          },
          {
            "field_reference_id": "Amount_Paid",
            "value": "1000"
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_paid_activity(user_id, subscription_duration_in_months: subscription_duration_in_months, amount_paid: 1000)
    end
  end

  describe '.create_or_update_contact' do
    let(:state) { create(:circle, circle_type: :location, level: :state) }
    let(:district) { create(:circle, circle_type: :location, level: :district, parent_circle: state) }
    let(:mandal) { create(:circle, circle_type: :location, level: :mandal, parent_circle: district) }
    let(:village) { create(:circle, circle_type: :location, level: :village, parent_circle: mandal) }
    let(:user) { create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}", village_id: village.id) }
    let(:party)  { create(:circle, circle_type: :interest, level: :political_party) }

    it 'sends a request to the CONTACT_URL' do
      user.update_location
      user.circles << party

      payload = {
        "phone_number": {
          "phone_number": "#{user.phone}",
          "dial_code": "+91",
          "iso2_country_code": ""
        },
        "properties": [
          {
            "field_reference_id": "first_name",
            "value": "#{user.name}"
          },
          {
            "field_reference_id": "last_name",
            "value": ""
          },
          {
            "field_reference_id": "contact_custom_id",
            "value": "#{user.id}"
          },
          {
            "field_reference_id": "Village_Id",
            "value": "#{user.village_id}"
          },
          {
            "field_reference_id": "Village_Name",
            "value": "#{user.village.name_en}"
          },
          {
            "field_reference_id": "mandal_name",
            "value": "#{user.mandal.name_en}"
          },
          {
            "field_reference_id": "Mandal_Id",
            "value": "#{user.mandal_id}"
          },
          {
            "field_reference_id": "District_Name",
            "value": "#{user.district.name_en}"
          },
          {
            "field_reference_id": "praja_district_Id",
            "value": "#{user.district_id}"
          },
          {
            "field_reference_id": "State_Name",
            "value": "#{user.state.name_en}"
          },
          {
            "field_reference_id": "State_Id",
            "value": "#{user.state_id}"
          },
          {
            "field_reference_id": "role_name",
            "value": "#{user.get_badge_role&.role&.name}"
          },
          {
            "field_reference_id": "role_id",
            "value": "#{user.get_badge_role&.role_id}"
          },
          {
            "field_reference_id": "affiliated_party_name",
            "value": "#{party&.name_en}"
          },
          {
            "field_reference_id": "affiliated_party_id",
            "value": "#{party&.id}"
          },
          {
            "field_reference_id": "admin_url",
            "value": "http://localhost:3000/admin/users/#{user.id}"
          },
          {
            "field_reference_id": "test_user",
            "value": "no"
          }
        ]
      }

      allow(described_class).to receive(:send_api_request).with(FlowwApi::CONTACT_URL, payload).and_return({ 'contact_id': "abcd"})

      expect(described_class).to receive(:send_api_request).with(FlowwApi::CONTACT_URL, payload)
      described_class.create_or_update_contact(user.id)
    end
  end

  describe '.update_add_autopay_sales_lead_activity' do
    let(:user) { create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}") }
    let(:lead_type) { "BLT_Inbound" }
    let(:lead_score) { 75 }
    let(:release_build_number) { "1.0.0" }
    let(:package_to_pitch) { "Yearly" }
    let(:lead_source) { "LEADER_PROFESSION" }
    let(:new_tag) { "" }
    let(:auto_assign) { true }
    let(:sop) { "SOP" }

    it 'sends a request to the TRACK_ACTIVITY_URL with dashboard links' do
      allow(LeadTypeSop).to receive(:get_sop_text).with(lead_type).and_return(sop)
      allow(Constants).to receive(:get_admin_host).and_return("http://localhost:3000")

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_add_autopay_sales_lead_activity(
        user.id,
        lead_type: lead_type,
        lead_score: lead_score,
        release_build_number: release_build_number,
        package_to_pitch: package_to_pitch,
        lead_source: lead_source,
        new_tag: new_tag,
        auto_assign: auto_assign
      )
    end
  end

  describe '.update_add_lead_activity' do
    let(:user) { create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}") }
    let(:poster_usage_count) { 2 }
    let(:lead_type) { "BLT_Inbound" }
    let(:sop) { "SOP" }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "Add_Lead",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          },
          {
            "field_reference_id": "phone_number",
            "value": "{\"iso2_country_code\":\"IN\",\"dial_code\":\"+91\",\"phone_number\":\"#{user.phone}\"}"
          },
          {
            "field_reference_id": "free_poster_usage",
            "value": "#{poster_usage_count}"
          },
          {
            "field_reference_id": "lead_type",
            "value": "#{lead_type}"
          },
          {
            "field_reference_id": "SOP",
            "value": "#{sop}"
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user.id}"
        }
      }
      allow(LeadTypeSop).to receive(:get_sop_text).with(lead_type).and_return(sop)

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_add_lead_activity(user.id, poster_usage_count:, lead_type:)
    end
  end

  describe '.update_trial_used_activity' do
    let(:user_id) { 1 }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "Trial_Used",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          },
          {
            "field_reference_id": "Trial_used",
            "value": "Yes"
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_trial_used_activity(user_id)
    end
  end

  describe '.update_qualified_pitch_activity' do
    let(:user_id) { 1 }
    let(:trial_remaining_days) { 10 }
    let(:premium_poster_usage) { 5 }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "Qualified_Fl_Pitch",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          },
          {
            "field_reference_id": "Trial_period_left",
            "value": "#{trial_remaining_days}"
          },
          {
            "field_reference_id": "Premium_poster_usage",
            "value": "#{premium_poster_usage}"
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_qualified_pitch_activity(user_id,
                                                      trial_remaining_days: trial_remaining_days,
                                                      premium_poster_usage: premium_poster_usage)
    end
  end

  describe '.update_user_online_activity' do
    let(:user_id) { 1 }
    let(:premium_poster_usage) { 6 }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "user_online",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          },
          {
            "field_reference_id": "Premium_poster_usage",
            "value": "#{premium_poster_usage}"
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_user_online_activity(user_id, premium_poster_usage:)
    end
  end

  describe '.update_assign_oe_activity' do
    let(:user_id) { 1 }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "assign_oe",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_assign_oe_activity(user_id)
    end
  end

  describe '.update_badge_setup_activity' do
    let(:user_id) { 1 }
    let(:user_hashid) { 'abc123' }

    it 'sends a request to the TRACK_ACTIVITY_URL with the required user_hashid' do
      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_badge_setup_activity(user_id, user_hashid: user_hashid)
    end

    it 'raises an error when user_hashid is not provided' do
      expect { described_class.update_badge_setup_activity(user_id) }.to raise_error(ArgumentError)
    end
  end

  describe '.update_layout_setup_activity' do
    let(:user_id) { 1 }
    let(:user_hashid) { 'abc123' }
    let(:user) { instance_double(User, id: user_id, hashid: user_hashid) }

    context 'when user_hashid is provided' do
      it 'sends a request to the TRACK_ACTIVITY_URL with the provided hashid' do
        expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
        described_class.update_layout_setup_activity(user_id, user_hashid: user_hashid)
      end
    end

    context 'when user_hashid is not provided' do
      before do
        allow(User).to receive(:find_by).with(id: user_id).and_return(user)
      end

      it 'fetches the user and sends a request to the TRACK_ACTIVITY_URL' do
        expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
        described_class.update_layout_setup_activity(user_id, user_hashid: user_hashid)
      end
    end
  end

  describe '.update_incomplete_layout_badge_activity' do
    let(:user_id) { 1 }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      payload = {
        "activity_reference_id": "incomplete_lb_data",
        "activity_details": [
          {
            "field_reference_id": "activity_datetime",
            "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
          }
        ],
        "contact_identification_type": "CUSTOM_ID",
        "custom_id_details": {
          "field_reference_id": "contact_custom_id",
          "value": "#{user_id}"
        }
      }

      expect { Floww::SendApiRequest.perform_async }.to enqueue_sidekiq_job
      described_class.update_incomplete_layout_badge_activity(user_id)
    end
  end

  describe '.send_api_request to TRACK_ACTIVITY_URL' do
    let(:payload) { { "key": "value" } }
    let(:uri) { URI.parse(FlowwApi::TRACK_ACTIVITY_URL) }
    let(:http) { instance_double("Net::HTTP") }
    let(:request) do
      instance_double("Net::HTTP::Post",
                      :body= => nil,
                      :[]= => nil)
    end
    let(:response) { instance_double("Net::HTTPResponse", code: "200", body: '{"success":true}') }
    let(:failed_response) { instance_double("Net::HTTPResponse", code: "400", body: '{"success":false}') }

    it 'sends a request to the TRACK_ACTIVITY_URL' do
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:new).and_return(http)
      allow(http).to receive(:use_ssl=).with(true)
      allow(Net::HTTP::Post).to receive(:new).and_return(request)
      allow(http).to receive(:request).with(request).and_return(response)

      described_class.send_api_request(FlowwApi::TRACK_ACTIVITY_URL, payload)

      expect(URI).to have_received(:parse).with(FlowwApi::TRACK_ACTIVITY_URL)
      expect(Net::HTTP).to have_received(:new).with(uri.host, uri.port)
      expect(http).to have_received(:use_ssl=).with(true)
      expect(Net::HTTP::Post).to have_received(:new).with(uri.request_uri)
      expect(request).to have_received(:body=).with(payload.to_json)
      expect(request).to have_received(:[]=).with("Content-Type", "application/json")
      expect(request).to have_received(:[]=).with("x-api-key", Rails.application.credentials[:floww_api_key])
      expect(http).to have_received(:request).with(request)
    end

    it 'honeybadger notify if request to the TRACK_ACTIVITY_URL fails' do
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:new).and_return(http)
      allow(http).to receive(:use_ssl=).with(true)
      allow(Net::HTTP::Post).to receive(:new).and_return(request)
      allow(http).to receive(:request).with(request).and_return(failed_response)
      allow(Honeybadger).to receive(:notify)
      allow(Rails.logger).to receive(:error)

      expect {
        described_class.send_api_request(FlowwApi::TRACK_ACTIVITY_URL, payload)
      }.to raise_error(StandardError)

      expect(URI).to have_received(:parse).with(FlowwApi::TRACK_ACTIVITY_URL)
      expect(Net::HTTP).to have_received(:new).with(uri.host, uri.port)
      expect(http).to have_received(:use_ssl=).with(true)
      expect(Net::HTTP::Post).to have_received(:new).with(uri.request_uri)
      expect(request).to have_received(:body=).with(payload.to_json)
      expect(request).to have_received(:[]=).with("Content-Type", "application/json")
      expect(request).to have_received(:[]=).with("x-api-key", Rails.application.credentials[:floww_api_key])
      expect(http).to have_received(:request).with(request)
      expect(Honeybadger).to have_received(:notify)
      expect(Rails.logger).to have_received(:error)
    end
  end
end
