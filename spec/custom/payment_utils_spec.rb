require 'rails_helper'
require 'webmock/rspec'

RSpec.describe PaymentUtils do

  describe "generate_checkout_url" do
    it "should return a url" do
      user = FactoryBot.create(:user)

      order = FactoryBot.create(
        :order,
        user_id: user.id,
        status: :opened,
      )

      expect(OrderTransaction).to receive(:create).with(
        status: :CREATED,
        amount: order.payable_amount,
        gateway: :PHONEPE,
        order_id: order.id,
        transaction_id: "1234567890"
      ).and_return(
        OrderTransaction.new(
          status: :CREATED,
          amount: order.payable_amount,
          gateway: :PHONEPE,
          order: order,
          transaction_id: "1234567890"
        )
      )

      expect(PaymentUtils).to receive(:phonepe_post).and_return(
        {
          "data" => {
            "merchantTransactionId" => "1234567890",
            "instrumentResponse" => {
              "redirectInfo" => {
                "url" => "https://sandbox.phonepe.com/transact/pay/1234567890",
                "method" => "POST"
              }
            }
          }
        }
      )

      expect(PaymentUtils).to receive(:generate_transaction_id).and_return("1234567890")

      response = PaymentUtils.generate_checkout_url(order, host_url: "http://localhost:3000")
      expect(response[:success]).to eq(true)
      expect(response[:url]).to eq("https://sandbox.phonepe.com/transact/pay/1234567890")
      expect(response[:method]).to eq("POST")
      expect(response[:transactionId]).to eq("1234567890")
    end
  end

  describe "generate_transaction_id" do
    it "should return a transaction id" do
      expect(PaymentUtils.generate_transaction_id.length).to eq(21)
    end
  end

  describe "get_base64_encoded_string" do
    it "should return a get_base64_encoded_string" do
      expect(PaymentUtils.get_base64_encoded_string("test")).to eq("dGVzdA==")
    end
  end

  describe "get_sha256_hash" do
    it "should return a get_sha256_hash" do
      expect(PaymentUtils.get_sha256_hash("test")).to eq("9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08")
    end
  end

  describe "generate_x_verify" do
    it "should return a generate_x_verify" do
      expect(PaymentUtils.generate_x_verify("test")).to eq(PaymentUtils.get_sha256_hash("test" + PaymentUtils::SALT_KEYS[0][:key]).to_s + '###1')
    end
  end

  describe "get_transaction_status" do
    it "should return a get_transaction_status" do
      transaction_id = "1234567890"
      response = "Some response"
      expect(PaymentUtils).to receive(:phonepe_get).with("/pg/v1/status/#{PaymentUtils::MERCHANT_ID}/#{transaction_id}").and_return(response)
      expect(PaymentUtils).to receive(:update_transaction_status).with(response)
      PaymentUtils.get_transaction_status(transaction_id)
    end
  end

  describe "update_transaction_status" do
    it "should update transaction status to success" do
      transaction_id = "1234567890"
      pg_transaction_id = "tg:1234567890"
      transaction_state = "SUCCESS"
      amount = 100 * 100

      response = {
        "code" => "PAYMENT_SUCCESS",
        "data" => {
          "merchantTransactionId" => transaction_id,
          "transactionId" => pg_transaction_id,
          "state" => transaction_state,
          "amount" => amount,
        }
      }

      payload_string = response.to_json

      user = FactoryBot.create(:user)

      pending_order = FactoryBot.create(
        :order,
        user_id: user.id,
        status: :pending,
        payable_amount: amount / 100,
      )
      item_price = FactoryBot.create(
        :item_price,
        item_id: Constants.get_poster_product_id,
        item_type: 'Product',
        price: 1,
        maintenance_price: 0,
        duration_in_months: 1)
      order = FactoryBot.create(
        :order,
        user_id: user.id,
        status: :opened,
        payable_amount: amount / 100,
        order_items: [
          FactoryBot.build(
            :order_item,
            item_id: Constants.get_poster_product_id,
            item_type: 'Product',
            item_price_id: item_price.id,
            duration_in_months: 1,
            total_item_price: 1
          )
        ]
      )

      transaction = OrderTransaction.new(
        status: :CREATED,
        amount: amount,
        gateway: :PHONEPE,
        order: order,
        transaction_id: transaction_id
      )

      expect(OrderTransaction).to receive(:find_by).with(transaction_id: transaction_id).and_return(transaction)
      expect(transaction).to receive(:update).with(
        status: transaction_state,
        raw_pg_request: payload_string,
        gateway_transaction_id: pg_transaction_id,
        amount: (amount / 100)
      )
      expect(SubscriptionUtils).to receive(:add_subscriptions_for_order).with(order)

      PaymentUtils.update_transaction_status(payload_string)
      expect(order.reload.status).to eq("successful")
      expect(pending_order.reload.status).to eq("closed")
    end

    it "should update transaction status to failed" do
      transaction_id = "1234567890"
      pg_transaction_id = "tg:1234567890"
      transaction_state = "FAILED"
      amount = 100 * 100

      response = {
        "code" => "PAYMENT_DECLINED",
        "data" => {
          "merchantTransactionId" => transaction_id,
          "transactionId" => pg_transaction_id,
          "state" => transaction_state,
          "amount" => amount,
        }
      }

      payload_string = response.to_json

      user = FactoryBot.create(:user)

      order = FactoryBot.create(
        :order,
        user_id: user.id,
        status: :opened,
      )

      transaction = OrderTransaction.new(
        status: :CREATED,
        amount: amount,
        gateway: :PHONEPE,
        order: order,
        transaction_id: transaction_id
      )

      expect(OrderTransaction).to receive(:find_by).with(transaction_id: transaction_id).and_return(transaction)

      expect(order).to receive(:update).with(status: :last_transaction_failed)
      expect(transaction).to receive(:update).with(
        status: transaction_state,
        raw_pg_request: payload_string,
        gateway_transaction_id: pg_transaction_id,
        amount: (amount / 100)
      )

      PaymentUtils.update_transaction_status(payload_string)
    end

    it "should update transaction status to pending" do
      transaction_id = "1234567890"
      pg_transaction_id = "tg:1234567890"
      transaction_state = "PAYMENT_PENDING"
      amount = 100 * 100
      response = {
        "code" => "PAYMENT_PENDING",
        "data" => {
          "merchantTransactionId" => transaction_id,
          "transactionId" => pg_transaction_id,
          "state" => transaction_state,
          "amount" => amount,
        }
      }
      payload_string = response.to_json

      user = FactoryBot.create(:user)

      order = FactoryBot.create(
        :order,
        user_id: user.id,
        status: :opened,
      )

      transaction = OrderTransaction.new(
        status: :CREATED,
        amount: amount,
        gateway: :PHONEPE,
        order: order,
        transaction_id: transaction_id
      )

      expect(OrderTransaction).to receive(:find_by).with(transaction_id: transaction_id).and_return(transaction)

      expect(order).to receive(:update).with(status: :pending)
      expect(transaction).to receive(:update).with(
        status: transaction_state,
        raw_pg_request: payload_string,
        gateway_transaction_id: pg_transaction_id,
        amount: (amount / 100)
      )

      PaymentUtils.update_transaction_status(payload_string)
    end
  end

  describe "validate_payload" do
    it "should return true if payload is valid" do
      payload = "test"
      PaymentUtils.get_sha256_hash(payload + PaymentUtils::SALT_KEYS[0][:key]).to_s + '###1'
      x_verify = PaymentUtils.generate_x_verify(payload)
      expect(PaymentUtils.validate_payload(payload, x_verify)).to eq(true)
    end
  end

  describe "phonepe_post" do
    it "should return a phonepe_post response" do
      payload = { body: "test" }
      endpoint = "/test"
      url = URI(Rails.application.credentials[:phonepe_host] + endpoint)

      http = Net::HTTP.new(url.host, url.port)

      expect(Net::HTTP).to receive(:new).with(
        url.host,
        url.port
      ).and_return(http)

      response = Net::HTTPResponse.new("1.1", "200", "OK")
      expect(response).to receive(:body).and_return({ test: "test" }.to_json)

      expect(http).to receive(:request).and_wrap_original do |original_method, *args, &block|
        request = args[0]
        expect(request.class).to eq(Net::HTTP::Post)
        expect(request["Accept"]).to eq('application/json')
        expect(request["Content-Type"]).to eq('application/json')
        expect(request["X-Verify"]).to eq(PaymentUtils.generate_x_verify(endpoint, payload: payload.to_json))
        expect(request.body).to eq({ "request": PaymentUtils.get_base64_encoded_string(payload.to_json) }.to_json)
        response
      end

      PaymentUtils.phonepe_post(endpoint, payload)
    end

    it "should throw exception" do
      payload = { body: "test" }
      endpoint = "/test"
      url = URI(Rails.application.credentials[:phonepe_host] + endpoint)

      http = Net::HTTP.new(url.host, url.port)

      expect(Net::HTTP).to receive(:new).with(
        url.host,
        url.port
      ).and_return(http)

      response = Net::HTTPResponse.new("1.1", "500", "OK")
      expect(response).to receive(:body).at_least(:once).and_return({ test: "test" }.to_json)

      expect(http).to receive(:request).and_wrap_original do |original_method, *args, &block|
        request = args[0]
        expect(request.class).to eq(Net::HTTP::Post)
        expect(request["Accept"]).to eq('application/json')
        expect(request["Content-Type"]).to eq('application/json')
        expect(request["X-Verify"]).to eq(PaymentUtils.generate_x_verify(endpoint, payload: payload.to_json))
        expect(request.body).to eq({ "request": PaymentUtils.get_base64_encoded_string(payload.to_json) }.to_json)
        response
      end
      expect { PaymentUtils.phonepe_post(endpoint, payload) }
        .to raise_error(RuntimeError, "PhonePe Post Request failed with status code: 500")
    end
  end

  describe "phonepe_get" do
    it "should return a phonepe_get response" do
      endpoint = "/test"
      url = URI(Rails.application.credentials[:phonepe_host] + endpoint)

      http = Net::HTTP.new(url.host, url.port)
      expect(Net::HTTP).to receive(:new).with(
        url.host,
        url.port
      ).and_return(http)

      response = Net::HTTPResponse.new("1.1", "200", "OK")
      expect(response).to receive(:body).and_return({ test: "test" }.to_json)

      expect(http).to receive(:request).and_wrap_original do |original_method, *args, &block|
        request = args[0]
        expect(request.class).to eq(Net::HTTP::Get)
        expect(request["Accept"]).to eq('application/json')
        expect(request["Content-Type"]).to eq('application/json')
        expect(request["X-Verify"]).to eq(PaymentUtils.generate_x_verify(endpoint))
        expect(request["X-MERCHANT-ID"]).to eq(PaymentUtils::MERCHANT_ID)
        response
      end

      PaymentUtils.phonepe_get(endpoint)
    end

    it "should throw exception" do
      endpoint = "/test"
      url = URI(Rails.application.credentials[:phonepe_host] + endpoint)

      http = Net::HTTP.new(url.host, url.port)
      expect(Net::HTTP).to receive(:new).with(
        url.host,
        url.port
      ).and_return(http)

      response = Net::HTTPResponse.new("1.1", "500", "OK")
      expect(response).to receive(:body).and_return({ test: "test" }.to_json)

      expect(http).to receive(:request).and_wrap_original do |original_method, *args, &block|
        request = args[0]
        expect(request.class).to eq(Net::HTTP::Get)
        expect(request["Accept"]).to eq('application/json')
        expect(request["Content-Type"]).to eq('application/json')
        expect(request["X-Verify"]).to eq(PaymentUtils.generate_x_verify(endpoint))
        expect(request["X-MERCHANT-ID"]).to eq(PaymentUtils::MERCHANT_ID)
        response
      end

      expect { PaymentUtils.phonepe_get(endpoint) }.to raise_error('Invalid response')
    end
  end

end
