FactoryBot.define do
  factory :circle_package do
    name { Faker::Name.name }
    enable_channel { true }
    enable_fan_posters { true }
    eligibile_for_post_push_notifications { true }
    eligibile_for_wati { [true, false].sample }
    fan_poster_creatives_limit { Faker::Number.number(digits: 2) }
    channel_post_msg_limit { Faker::Number.number(digits: 2) }
    channel_post_msg_notification_limit { Faker::Number.number(digits: 2) }
  end
end
