FactoryBot.define do
  factory :user do
    name { Faker::Name.unique.name }
    # active { true }
    # signed_up { true }
    status { :active }

    # trait :with_location do
    #   state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
    #   district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location,
    #                                       parent_circle: state_circle)
    #   mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location,
    #                                     parent_circle: district_circle)
    #   village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location,
    #                                      parent_circle: mandal_circle)
    #   mp_constituency_circle = FactoryBot.create(:circle, name: 'mp_constituency_circle', level: :mp_constituency,
    #                                              circle_type: :location)
    #   FactoryBot.create(:circle, name: 'mla_constituency_circle', level: :mla_constituency,
    #                     circle_type: :location, parent_circle: mp_constituency_circle)
    #   village_id { village_circle.id }
    #   mandal_id { mandal_circle.id }
    #   mla_constituency_id { mp_constituency_circle.id }
    #   mp_constituency_id { mp_constituency_circle.id }
    #   district_id { district_circle.id }
    #   state_id { state_circle.id }
    # end

    # create trial user with trial_user trait
    trait :trial_user do
      after(:create) do |user|
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_duration_key,
                          value: 15)
      end
    end

    # create user with trial extension
    trait :trial_extension_user do
      after(:create) do |user|
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today - 15.days)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_duration_key,
                          value: 15)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_end_date_key,
                          value: Time.zone.today)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_duration_key,
                          value: 15)
      end
    end

    # create trial expired user
    trait :trial_expired_user do
      after(:create) do |user|
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today - 20.days)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_duration_key,
                          value: 15)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_end_date_key,
                          value: Time.zone.today - 5.days)
      end
    end

    # create trial about to expire user
    trait :trial_about_to_expire_user do
      after(:create) do |user|
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today - 15.days)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_duration_key,
                          value: 15)
      end
    end

    # with_premium_subscription # for now this is enough
    trait :with_premium_subscription do
      after(:create) do |user|
        create(:user_plan, user: user, end_date: Time.zone.now + 1.month)
      end
    end

    # with user role
    trait :with_user_role do
      after(:create) do |user|
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)
        # random number in 1 to 5
        number = rand(1..5)
        @user_role = FactoryBot.create(:user_role, user: user, role: @role, parent_circle_id: @circle.id,
                                       active: true, start_date: Time.zone.now, end_date: Time.zone.now + 1.month,
                                       grade_level: "grade_#{number}")

      end
    end

    trait :with_poster_layout do
      after(:create) do |user|
        photo_path = Rails.root.join("app/assets/images/praja-full-logo.png")
        photo = Rack::Test::UploadedFile.new(photo_path, "image/png")

        # Set poster photo
        user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)

        # Create user poster layout
        user_poster_layout = FactoryBot.create(:user_poster_layout, entity: user, h1_count: 1, h2_count: 1)

        # Create leader photos
        FactoryBot.create(:user_leader_photo, user: user, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                          header_type: :header_1, priority: 1, user_poster_layout: user_poster_layout)
        FactoryBot.create(:user_leader_photo, user: user, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                          header_type: :header_2, priority: 1, user_poster_layout: user_poster_layout)
      end
    end

  end
end
