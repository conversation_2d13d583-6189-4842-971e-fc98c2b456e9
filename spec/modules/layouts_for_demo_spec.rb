require 'spec_helper'
require 'rails_helper'

RSpec.describe LayoutsForDemo do
  include described_class

  let(:user) { create(:user) }
  let(:circle) { create(:circle) }
  let(:affiliated_party_circle_id) { circle.id }

  before do
    allow(user).to receive(:affiliated_party_circle_id).and_return(circle.id)
    allow(Circle).to receive(:find_by).with(id: circle.id.to_i).and_return(circle)
    allow(circle).to receive(:get_gradients_for_posters_tab).and_return(["#FFFFFF", "#000000"])
    allow(circle).to receive(:get_primary_highlight_color).and_return("#FFFFFF")
    allow(circle).to receive(:get_secondary_highlight_color).and_return("#000000")
    allow(user).to receive(:get_affiliated_party_icon).and_return("https://example.com/icon.png")
    @admin_user = FactoryBot.create(:admin_user)
    data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
    FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
  end

  describe '#demo_layouts_for_premium_experience' do
    context 'with valid user role and circle' do
      it 'generates layout jsons for premium experience' do
        result = user.demo_layouts_for_premium_experience

        expect(result).to be_an(Array)
        expect(result.first).to include(:layout_type, :gradients, :identity)
        expect(result.first[:identity]).to include(:user, :type, :party_highlight_color_primary)
      end
    end

    context 'when name_type and badge_text_type are long' do
      it 'sets is_user_position_back to true' do
        allow(user).to receive_messages(name_type: 'long', badge_text_type: 'long')
        result = user.demo_layouts_for_premium_experience

        expect(result.first[:identity][:is_user_position_back]).to be true
      end
    end

    context 'when affiliated_party_circle_id is not present' do
      it 'does not include circle specific photos in headers' do
        allow(self).to receive(:affiliated_party_circle_id).and_return(nil)

        result = user.demo_layouts_for_premium_experience

        expect(result.first[:header_1_photos]).to all(include(:photo_url))
        expect(result.first[:header_2_photos]).to all(include(:photo_url))
      end
    end

    context 'when dummy photos are not found' do
      it 'excludes dummy photos from headers' do
        allow(AdminMedium).to receive(:find_by).and_return(nil)

        result = user.demo_layouts_for_premium_experience

        expect(result.first[:header_1_photos]).to be_empty
        expect(result.first[:header_2_photos]).to be_empty
      end
    end

    context 'with different layout configurations' do
      it 'generates layout jsons for each configuration' do
        result = user.demo_layouts_for_premium_experience

        expect(result.size).to eq(4)
        expect(result.map { |layout| layout[:identity][:type] }).to match_array(%w[semi_circular_identity polygonal_profile_identity gold_lettered_user top_trapezoidal_identity])
      end
    end
  end
end
