require 'spec_helper'
require 'rails_helper'

RSpec.describe ProfileViewHelper do
  include described_class
  describe '#fetch_views_data' do
    let(:user) { create(:user) }
    let(:loaded_user_ids) { [create(:user).id] }
    let(:count) { 5 }

    context 'when there are viewed profiles' do
      it 'returns viewed user ids, user data for premium experience, and lock status' do
        viewed_users = create_list(:user, 3)
        viewed_users.each { |u| create(:profile_view, user: user, viewer: u) }
        allow(user).to receive(:is_poster_subscribed).and_return(true)

        result = fetch_views_data(user, loaded_user_ids, count)

        expect(result[:viewed_user_ids]).to match_array(viewed_users.map(&:id))
        expect(result[:users].size).to eq(3)
        expect(result[:locked]).to be_falsey
      end
    end

    context 'when no profiles have been viewed' do
      it 'returns empty arrays for user ids and users, and lock status' do
        allow(user).to receive(:is_poster_subscribed).and_return(false)

        result = fetch_views_data(user, loaded_user_ids, count)

        expect(result[:viewed_user_ids]).to be_empty
        expect(result[:users]).to be_empty
        expect(result[:locked]).to be_truthy
      end
    end

    context 'when user is not subscribed or on trial' do
      it 'indicates that the feature is locked' do
        viewed_users = create_list(:user, 2)
        viewed_users.each { |u| create(:profile_view, user: user, viewer: u) }
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(false)

        result = fetch_views_data(user, loaded_user_ids, count)

        expect(result[:locked]).to be_truthy
        expect(result[:users].size).to eq(2)
      end
    end
  end

  describe '#generate_title' do
    let(:user) { create(:user) }

    context 'when loaded_user_ids is blank' do
      it 'returns profile views count in title' do
        viewed_users = create_list(:user, 2)
        viewed_users.each { |u| create(:profile_view, user: user, viewer: u) }

        title, views_count = generate_title(user, [])

        expect(title).to include("2 మంది వీక్షించారు")
        expect(views_count).to eq(2)
      end
    end

    context 'when loaded_user_ids is not blank' do
      it 'returns nil for title and views_count' do
        title, views_count = generate_title(user, [1, 2])

        expect(title).to be_nil
        expect(views_count).to be_nil
      end
    end
  end

  describe '#generate_see_more_text' do
    context 'when views count is greater than count' do
      it 'returns see more text and color' do
        see_more_text, see_more_text_color = generate_see_more_text(15, 10)

        expect(see_more_text).to eq("ఇంకా చూడండి")
        expect(see_more_text_color).to eq(0xff8F8F8F)
      end
    end

    context 'when views count is not greater than count' do
      it 'returns nil for see more text and color' do
        see_more_text, see_more_text_color = generate_see_more_text(5, 10)

        expect(see_more_text).to be_nil
        expect(see_more_text_color).to be_nil
      end
    end
  end
end
