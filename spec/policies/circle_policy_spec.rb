require 'rails_helper'

RSpec.describe CirclePolicy, type: :policy do

  describe CirclePolicy do
    subject { described_class }

    permissions :is_user_can_remove_tag? do
      context "check whether user able to remove tag or not" do

        it "denies access if user don't have permission" do
          @user = FactoryBot.create(:user)
          @circle = FactoryBot.create(:circle)
          expect(subject).not_to permit(@user, @circle)
        end

        it "should permit to remove tag if user have permission" do
          @user_1 = FactoryBot.create(:user)
          @circle_1 = FactoryBot.create(:circle)
          @permission_group = FactoryBot.build(:permission_group)
          @permission_group.save(validate: false)
          @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group_id: @permission_group.id, permission_identifier: :remove_tag)
          @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user_1.id, circle_id: @circle_1.id, permission_group_id: @permission_group.id)
          expect(subject).to permit(@user_1, @circle_1)
        end

      end
    end

    permissions :can_upload_creative? do
      context "check whether user able to upload creative or not" do

        it "denies access if user don't have permission" do
          @user = FactoryBot.create(:user)
          @circle = FactoryBot.create(:circle)
          expect(subject).not_to permit(@user, @circle)
        end

        it "should permit to upload creative if user have permission" do
          @user_1 = FactoryBot.create(:user)
          @circle_1 = FactoryBot.create(:circle)
          @permission_group = FactoryBot.build(:permission_group)
          @permission_group.save(validate: false)
          @permission_group_permission = FactoryBot.create(:permission_group_permission, permission_group_id: @permission_group.id, permission_identifier: :circle_upload_creative)
          @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user_id: @user_1.id, circle_id: @circle_1.id, permission_group_id: @permission_group.id)

          # to have circle_upload_creative permission circle should have active circle package mapping
          @circle_package = FactoryBot.create(:circle_package)
          FactoryBot.create(:circle_package_mapping, circle_package: @circle_package, circle: @circle_1)
          FactoryBot.create(:circle_monthly_usage, circle: @circle_1)

          expect(subject).to permit(@user_1, @circle_1)
        end

      end
    end
  end

end
