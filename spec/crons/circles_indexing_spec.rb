require 'rails_helper'

RSpec.describe CirclesIndexing, type: :worker do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perfom' do
    context 'enqueues the workers' do
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        @circle = FactoryBot.create(:circle)
      end
      it 'calls the method' do
        allow($redis).to receive(:get).and_return(@circle.id - 1)
        allow_any_instance_of(Circle).to receive(:index_for_search).and_return(nil)
        allow($redis).to receive(:set).and_return(1)
        expect_any_instance_of(Circle).to receive(:index_for_search).and_return(nil)
        described_class.new.perform
      end
    end
  end
end
