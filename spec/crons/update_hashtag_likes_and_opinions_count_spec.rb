require 'rails_helper'

RSpec.describe UpdateHashtagLikesAndOpinionsCount, type: :worker do
  describe "update likes count & opinions count on database" do
    it "when hashtag created, post created & hashtag liked" do
      name = Faker::Lorem.unique.word
      hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))
      FactoryBot.create(:hashtag_like, hashtag: hashtag, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))

      described_class.new.perform

      hashtag.reload

      expect(hashtag.get_opinions_count).to eq(0)
      expect(hashtag.get_likes_count).to eq(1)
    end

    it "when hashtag created, post created & post liked" do
      name = Faker::Lorem.unique.word
      hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))
      post = FactoryBot.create(:post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))
      FactoryBot.create(:post_hashtag, hashtag: hashtag, post: post)
      FactoryBot.create(:post_like, post: post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))

      described_class.new.perform

      hashtag.reload

      expect(hashtag.get_opinions_count).to eq(1)
      expect(hashtag.get_likes_count).to eq(2)
    end

    it "when hashtag created, post created, post liked & hashtag liked" do
      name = Faker::Lorem.unique.word
      hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))
      post = FactoryBot.create(:post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))
      FactoryBot.create(:post_hashtag, hashtag: hashtag, post: post)
      FactoryBot.create(:post_like, post: post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))
      FactoryBot.create(:hashtag_like, hashtag: hashtag, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))

      described_class.new.perform

      hashtag.reload

      expect(hashtag.get_opinions_count).to eq(1)
      expect(hashtag.get_likes_count).to eq(3)
    end
  end
end