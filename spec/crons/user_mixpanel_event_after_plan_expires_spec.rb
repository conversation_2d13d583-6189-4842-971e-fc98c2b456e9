require 'rails_helper'

RSpec.describe UserMixpanelEventAfterPlanExpires do
  describe '#perform' do
    let(:user_plan) { create(:user_plan, end_date: Time.zone.yesterday) }

    context 'when user plans have expired' do
      before :each do
        allow(UserPlan).to receive(:where).and_return(UserPlan.where(id: user_plan.id))
        allow(SyncMixpanelUser).to receive(:perform_async)
      end
      it 'schedules SyncMixpanelUser job for each expired user plan' do
        subject.perform
        expect(SyncMixpanelUser).to have_received(:perform_async).with(user_plan.user_id)
      end
    end

    context 'when no user plans have expired' do
      before :each do
        allow(UserPlan).to receive(:where).and_return(UserPlan.none)
        allow(SyncMixpanelUser).to receive(:perform_async)
      end

      it 'does not schedule any SyncMixpanelUser jobs' do
        subject.perform
        expect(SyncMixpanelUser).not_to have_received(:perform_async)
      end
    end

    context 'when user plans expired before yesterday' do
      let(:user_plan) { create(:user_plan, end_date: 2.days.ago) }
      before :each do
        allow(SyncMixpanelUser).to receive(:perform_async)
      end

      it 'does not schedule any SyncMixpanelUser jobs' do
        subject.perform
        expect(SyncMixpanelUser).not_to have_received(:perform_async)
      end
    end

    context 'when user plans expire today' do
      let(:user_plan) { create(:user_plan, end_date: Time.zone.today.end_of_day) }
      before :each do
        allow(SyncMixpanelUser).to receive(:perform_async)
      end
      it 'does not schedule any SyncMixpanelUser jobs' do
        subject.perform
        expect(SyncMixpanelUser).not_to have_received(:perform_async)
      end
    end
  end
end
