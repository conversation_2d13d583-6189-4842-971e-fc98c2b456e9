# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CronForSendingChargesToPg, type: :worker do
  describe '#perform' do
    context 'when there is a charge with charge_date as tomorrow' do
      it 'enqueues to SendChargeToPg' do
        subscription_charge = FactoryBot.create(:subscription_charge,
                                                amount: 299,
                                                charge_date: Time.zone.now.tomorrow.beginning_of_day,
                                                status: :created)

        allow(SendChargeToPg).to receive(:perform_async)
        expect(SendChargeToPg).to receive(:perform_async).with(subscription_charge.id, subscription_charge.subscription.payment_gateway)

        described_class.new.perform
      end
    end
  end
end
