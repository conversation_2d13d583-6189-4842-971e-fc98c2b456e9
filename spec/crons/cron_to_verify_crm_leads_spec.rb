require 'rails_helper'
require 'webmock/api'

RSpec.describe CronToVerifyCrmLeads, type: :worker do
  describe '#perform' do
    context 'when there are interested leads with an empty crm_stage' do
      before do
        @user1 = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user)
        @url  = "*******************************************************************************"
        @premium_pitch1 = FactoryBot.create(:premium_pitch, user: @user1, status: :interested, crm_stage: nil, created_at: 30.minutes.ago)
        @premium_pitch2 = FactoryBot.create(:premium_pitch, user: @user2, status: :interested, crm_stage: nil, created_at: 10.minutes.ago)
      end

      it 'only sends a notification if there are interested leads with an empty crm_stage' do
        stub_request(:post, @url).to_return(status: 200, body: "", headers: {})
        described_class.new.perform
        expect(@premium_pitch1.crm_stage).to be_nil
        expect(@premium_pitch2.crm_stage).to be_nil
        expect(WebMock).to have_requested(:post, @url).once
      end
    end

    context 'when there are interested leads with an empty crm_stage and created at now' do
      before do
        @user1 = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user, phone: 2999999999)
        @user3 = FactoryBot.create(:user)
        @url  = "*******************************************************************************"
        @premium_pitch1 = FactoryBot.create(:premium_pitch, user: @user1, status: :interested, crm_stage: nil, created_at: 30.minutes.ago)
        @premium_pitch2 = FactoryBot.create(:premium_pitch, user: @user2, status: :interested, crm_stage: nil, created_at: 18.minutes.ago)
        @premium_pitch3 = FactoryBot.create(:premium_pitch, user: @user3, status: :interested, crm_stage: nil)
      end

      it 'only sends a notification if there are interested leads with an empty crm_stage till 15 minutes ago' do
        stub_request(:post, @url).to_return(status: 200, body: "", headers: {})
        described_class.new.perform

        expect(@premium_pitch1.crm_stage).to be_nil
        expect(@premium_pitch2.crm_stage).to be_nil
        expect(@premium_pitch3.crm_stage).to be_nil

        interested_leads = PremiumPitch.joins(:user)
                                       .where("premium_pitches.created_at >= ?", Time.zone.yesterday.beginning_of_day)
                                       .where("premium_pitches.created_at <= ?", 15.minutes.ago)
                                       .where(premium_pitches: { status: :interested, crm_stage: nil })
                                       .where(users: { internal: false })
                                       .pluck(:user_id)

        expect(interested_leads.count).to eq(1)
        expect(WebMock).to have_requested(:post, @url).once
      end
    end


    context 'when interested leads with not an empty crm_stage' do
      before do
        @user1 = FactoryBot.create(:user)
        @url  = "*******************************************************************************"
        @premium_pitch = FactoryBot.create(:premium_pitch, user: @user1, status: :interested, crm_stage: "Fresh Lead")
      end

      it 'does not send a notification to Slack' do
        stub_request(:post, @url).to_return(status: 200, body: "", headers: {})
        described_class.new.perform
        expect(WebMock).not_to have_requested(:post, @url)
      end
    end
  end
end
