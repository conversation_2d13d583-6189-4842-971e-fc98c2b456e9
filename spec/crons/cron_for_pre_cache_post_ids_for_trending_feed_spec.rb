require 'rails_helper'

RSpec.describe CronForPreCachePostIdsForTrendingFeed, type: :worker do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perfom' do
    context 'enqueues the workers' do
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        FactoryBot.create(:circle, circle_type: :location, level: :state, active: true)
      end
      it 'calls the method' do
        @post1 = FactoryBot.create(:post)
        @post2 = FactoryBot.create(:post)
        @post3 = FactoryBot.create(:post)
        allow(ES_CLIENT).to receive(:search).and_return(
          "hits" => {
            "total" => { "value" => 5, "relation" => "eq" },
            "hits" => [
              { "_id" => @post1.id, "fields" => { "id" => [@post1.id] } },
              { "_id" => @post2.id, "fields" => { "id" => [@post2.id] } },
              { "_id" => @post3.id, "fields" => { "id" => [@post3.id] } }
            ]
          }
        )
        described_class.new.perform
      end
    end
  end
end
