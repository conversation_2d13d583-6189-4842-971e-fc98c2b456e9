require 'rails_helper'
RSpec.describe CronForCreatingSubscriptionCharges, type: :worker do
  describe '#perform' do
    context 'when there is active layout' do
      it 'creates a subscription charge' do
        subscription = FactoryBot.create(:subscription, status: :active, max_amount: 100)
        FactoryBot.create(:user_poster_layout, entity: subscription.user)
        FactoryBot.create(:user_plan, user: subscription.user, plan: subscription.plan, amount: 50,
                          end_date: Time.zone.tomorrow.end_of_day)
        subject.perform
        expect(SubscriptionCharge.where(user: subscription.user, subscription: subscription).last).to have_attributes(status: 'created')
      end
    end
    context 'when there is no active layout' do
      it 'doesn\'t create a subscription charge & pauses the subscription' do
        subscription = FactoryBot.create(:subscription, status: :active, max_amount: 100)
        FactoryBot.create(:user_poster_layout, entity: subscription.user, active: false)
        FactoryBot.create(:user_plan, user: subscription.user, plan: subscription.plan, amount: 50,
                          end_date: Time.zone.tomorrow.end_of_day)
        subject.perform
        expect(SubscriptionCharge.where(user: subscription.user, subscription: subscription).last).to be_nil
        expect(subscription.reload.paused?).to be_truthy
      end
    end
  end
end
