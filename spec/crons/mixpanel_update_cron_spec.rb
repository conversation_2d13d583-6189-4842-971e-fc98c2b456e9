require 'rails_helper'

RSpec.describe MixpanelUpdateCron, type: :worker do

  # describe "perform" do
  #   it "should call MixpanelIntegration.perform_async" do
  #     @user = FactoryBot.create(:user)
  #     allow($redis).to receive(:get).and_return(@user.id)
  #     allow(User).to receive(:where).and_return([@user])
  #     allow(MixpanelIntegration).to receive(:perform_async).with(@user.id, { :current_badge_role_ids => [],
  #                                                                            :followers_count_backend => @user.followers_count,
  #                                                                            :following_count_backend => @user.following_count,
  #                                                                            :joined_interest_circle_ids => [],
  #                                                                            :joined_political_party_ids => [] })
  #     allow($redis).to receive(:set).with('mix_panel_update_last_user_id_v2', @user.id.to_s)
  #     MixpanelUpdateCron.new.perform
  #   end
  # end
end
