require 'rails_helper'

RSpec.describe CronToFollowOwnerAfterOwnerAssignedToCircle, type: :cron do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perfom' do
    context 'enqueues the workers' do
      before :each do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, circle: @circle, user: @user,
                                                          permission_group_id: Constants.owner_permission_group_id)
      end
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, circle: @circle, user: @user,
                                                          permission_group_id: Constants.owner_permission_group_id)
        described_class.new.perform
      end
      it 'schedules import circle owner follow worker' do
        expect(ImportCircleOwnerFollow.jobs.last['args'][0..1]).to eq([@circle.id, @user.id])
      end
    end
  end
end
