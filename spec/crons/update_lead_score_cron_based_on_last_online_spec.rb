require 'rails_helper'

RSpec.describe UpdateLeadScoreCronBasedOnLastOnline do
  describe '#perform' do
    let(:redis) { double('Redis') }
    let(:logger) { double('Logger', info: nil) }
    let(:user_id) { 123 }
    let(:time_now) { Time.zone.now.to_i }

    before do
      stub_const('Constants', double(update_floww_lead_score_based_on_last_online_key: 'test_key'))
      allow($redis).to receive(:zrangebyscore).and_return([user_id])
      allow($redis).to receive(:zremrangebyscore)
      allow(LastOnlineSchedulerWorker).to receive(:perform_async)
      allow(subject).to receive(:logger).and_return(logger)
    end

    context 'when there are user ids to process' do
      it 'logs the cron running' do
        subject.perform
        expect(logger).to have_received(:info).with('Update lead score cron running')
      end

      it 'schedules the LastOnlineSchedulerWorker for each user id' do
        subject.perform
        expect(LastOnlineSchedulerWorker).to have_received(:perform_async).with(user_id)
      end

      it 'removes processed user ids from Redis' do
        subject.perform
        expect($redis).to have_received(:zremrangebyscore).with('test_key', 0, time_now)
      end
    end

    context 'when there are no user ids to process' do
      before do
        allow($redis).to receive(:zrangebyscore).and_return([])
      end

      it 'does not schedule any workers' do
        subject.perform
        expect(LastOnlineSchedulerWorker).not_to have_received(:perform_async)
      end

      it 'removes no user ids from Redis' do
        subject.perform
        expect($redis).to have_received(:zremrangebyscore).with('test_key', 0, time_now)
      end
    end
  end
end
