require 'rails_helper'

RSpec.describe PosterPremiumExtensionEndedEventCron, type: :worker do
  # describe "#perform" do
  #   let(:poster_product_id) { Constants.get_poster_product_id }
  #
  #   context "when there are no premium extensions ending yesterday" do
  #     it "does not trigger any jobs" do
  #       expect(UserProductSubscription).to receive(:where).with(source: :premium_extension, item_type: :Product,
  #                                                               item_id: poster_product_id)
  #                                                         .and_return(UserProductSubscription.none)
  #       expect(EventTracker).not_to receive(:perform_async)
  #       expect(SyncMixpanelUser).not_to receive(:perform_async)
  #
  #       subject.perform
  #     end
  #   end
  #
  #   context "when there are premium extensions ending yesterday" do
  #
  #     it "enqueues a EventTracker job for each user" do
  #       user = FactoryBot.create(:user)
  #       order = FactoryBot.create(:order, :with_order_items, user_id: user.id)
  #       start_date = Time.zone.yesterday.beginning_of_day # Ensure the time is set to start of day to match expected
  #       ups = FactoryBot.create(:user_product_subscription, user_id: user.id, start_date: start_date,
  #                               end_date: Time.zone.yesterday.end_of_day,
  #                               item_type: 'Product', item_id: poster_product_id, order: order, source: 'premium_extension')
  #       expected_data = {
  #         "user_id" => user.id,
  #         "start_date" => start_date,
  #         "end_date" => ups.end_date,
  #         "source" => 'premium_extension'
  #       }
  #       expect(EventTracker).to receive(:perform_async).with(user.id, "premium_extension_ended", expected_data)
  #                                                              .once
  #
  #       subject.perform
  #     end
  #   end
  # end
end
