# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ProcessPosterCreativeViewsBatch, type: :worker do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perform' do
    context 'enqueues the workers' do
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        @user = FactoryBot.create(:user)
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
      end
      it 'calls the method' do
        redis_mock = instance_double(Redis)
        allow(redis_mock).to receive(:spop).and_return(
          JSON.dump({ "poster_creative_id" => @poster_creative.id, "user_id" => @user.id }),
          nil
        )

        allow($redis).to receive(:spop) { |key| redis_mock.spop(key) }
        allow(PosterCreativeView).to receive(:import).and_return(nil)

        described_class.new.perform

        expect(PosterCreativeView).to have_received(:import)
      end
    end
  end
end
