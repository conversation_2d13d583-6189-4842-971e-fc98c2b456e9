require 'rails_helper'

RSpec.describe UpdateUserTrialEndDates, type: :worker do
  describe '#perform' do
    let(:worker) { described_class.new }

    context 'when there are users with trial start date but no trial end date' do
      it 'should set trial end dates for expired trials' do
        user_1 = FactoryBot.create(:user, :trial_about_to_expire_user)
        user_2 = FactoryBot.create(:user, :trial_about_to_expire_user)
        user_3 = FactoryBot.create(:user, :trial_user)
        user_4 = FactoryBot.create(:user, :trial_expired_user)
        allow(EventTracker).to receive(:perform_async).with(user_1.id, "poster_trial_ended",
                                                            { "trial_end_date" => Time.zone.yesterday
                                                                                              .strftime("%Y-%m-%d") })
        allow(EventTracker).to receive(:perform_async).with(user_2.id, "poster_trial_ended",
                                                            { "trial_end_date" => Time.zone.yesterday
                                                                                              .strftime("%Y-%m-%d") })
        expect(Metadatum.where(entity: user_1, key: Constants.user_poster_trial_end_date_key).count).to eq(0)
        expect(Metadatum.where(entity: user_2, key: Constants.user_poster_trial_end_date_key).count).to eq(0)
        expect(Metadatum.where(entity: user_3, key: Constants.user_poster_trial_end_date_key).count).to eq(0)
        expect(Metadatum.where(entity: user_4, key: Constants.user_poster_trial_end_date_key).count).to eq(1)
        described_class.new.perform
        expect(Metadatum.where(entity: user_1, key: Constants.user_poster_trial_end_date_key).count).to eq(1)
        expect(Metadatum.where(entity: user_2, key: Constants.user_poster_trial_end_date_key).count).to eq(1)
        expect(Metadatum.where(entity: user_3, key: Constants.user_poster_trial_end_date_key).count).to eq(0)
        expect(Metadatum.where(entity: user_4, key: Constants.user_poster_trial_end_date_key).count).to eq(1)
        # Sync Mixpanel User worker should be called for each user
        expect(SyncMixpanelUser.jobs.size).to eq(4)

        expect(EventTracker).to have_received(:perform_async).with(user_1.id, "poster_trial_ended",
                                                                   { "trial_end_date" => Time.zone.yesterday
                                                                                                     .strftime("%Y-%m-%d") })
        expect(EventTracker).to have_received(:perform_async).with(user_2.id, "poster_trial_ended",
                                                                   { "trial_end_date" => Time.zone.yesterday
                                                                                                     .strftime("%Y-%m-%d") })
      end

    end
  end
end
