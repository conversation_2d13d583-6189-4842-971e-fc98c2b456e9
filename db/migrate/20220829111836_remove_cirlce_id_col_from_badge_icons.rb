class RemoveCirlceIdColFromBadgeIcons < ActiveRecord::Migration[6.1]
  def change
    remove_index :badge_icons, :circle_id if index_exists?(:badge_icons, :circle_id)
    remove_foreign_key :badge_icons, :circles if foreign_key_exists?(:badge_icons, :circles)
    remove_index :badge_icons, name: "index_badge_icons_on_circle_id_and_color", if_exists: true
    remove_column :badge_icons, :circle_id
  end
end
