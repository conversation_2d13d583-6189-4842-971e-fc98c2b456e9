class CreateUserCirclePermissionGroups < ActiveRecord::Migration[6.1]
  def change
    create_table :user_circle_permission_groups do |t|
      t.references :user, null: false
      t.references :circle, null: false
      t.references :permission_group, null: false
      t.timestamps
    end
    add_index :user_circle_permission_groups, [:user_id, :circle_id], unique: true,
              name: 'index_circle_permission_groups_on_user_id_and_circle_id'
  end
end
