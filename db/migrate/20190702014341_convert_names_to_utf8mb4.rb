class ConvertNamesToUtf8mb4 < ActiveRecord::Migration[5.2]
  def change
    execute "ALTER TABLE circles MODIFY `name` VARCHAR(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin"
    execute "ALTER TABLE users MODIFY `name` VARCHAR(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin"
    execute "ALTER TABLE notifications MODIFY `description` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin"
    execute "ALTER TABLE links MODIFY `description` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin"
    execute "ALTER TABLE links MODIFY `title` VARCHAR(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin"
  end
end
