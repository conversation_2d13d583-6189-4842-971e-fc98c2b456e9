class ChangeTitleAndBodyCollationOnLeaderProjects < ActiveRecord::Migration[6.1]
  def up
    change_column :leader_projects, :title, :string, null: false, collation: 'utf8mb4_unicode_ci'
    change_column :leader_projects, :body, :string, null: false, collation: 'utf8mb4_unicode_ci'
  end

  def down
    change_column :leader_projects, :title, :string, null: false, collation: 'utf8mb4_0900_ai_ci'
    change_column :leader_projects, :body, :string, null: false, collation: 'utf8mb4_0900_ai_ci'
  end
end
