class CreateCirclePermissionGroups < ActiveRecord::Migration[6.1]
  def change
    create_table :circle_permission_groups do |t|
      t.references :circle
      t.integer :circle_type
      t.integer :circle_level
      t.references :permission_group, null: false
      t.boolean :is_user_joined, null: false
      t.timestamps
    end
    add_index :circle_permission_groups, [:circle_id, :is_user_joined], unique: true
    add_index :circle_permission_groups, [:circle_type, :circle_level, :is_user_joined], unique: true,
              name: 'index_on_circle_type_level_and_is_user_joined'

  end
end
