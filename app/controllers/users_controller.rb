# noinspection RubyControlFlowConversionInspection
class UsersController < ApiController

  include UsersControllerSearchConcern
  include PostsConcern
  include UsersControllerFanPostersConcern
  include UsersControllerProfileViewsConcern
  include PlanHelper
  include CommonJsonBuilder
  include PosterCarouselFeedItem

  before_action :set_logged_in_user,
                except: %i[get_otp get_exotel_otp_text get_voice_otp login_with_truecaller login logout get_email_otp menu_selection call_status]
  before_action :set_user, only: %i[show follow unfollow block followers following get_posts get_liked_posts report invite_link
                                    fb_invite_link update_profile_pic delete_profile_pic close_toast suggested_seen
                                    get_following_circles_for_tag get_suggested_circles_for_tag search_circles_for_tag
                                    info_pop_up_accept alert_pop_up_accept get_users_with_ids unblock
                                    get_dm_recommended_users_for_share get_dm_recommended_users_for_new_conversation
                                    get_dm_recommended_users_for_share_v1 get_dm_recommended_users_for_new_conversation_v1
                                    menu_selection call_status]
  before_action :set_circle, only: %i[request_fan_poster request_circle_premium get_fan_poster_requests_data]

  def get_users_with_ids
    batch_size = Constants.users_batch_size_for_dm_batch_api
    user_ids = params[:user_ids]

    # check if user ids are present, not empty and not nil and throw error
    if user_ids.blank?
      render json: {
        success: false,
        message: 'user_ids is required'
      }, status: :bad_request
      return
    end

    # convert user ids to integer
    user_ids = user_ids.map(&:to_i)

    # initialize variables
    errors = []
    users_map = {}
    batch_size_exceeded_user_ids = []

    # check if user ids are more than batch size and split them
    if user_ids.length > batch_size
      batch_size_exceeded_user_ids = user_ids[batch_size..-1]
      user_ids = user_ids[0..batch_size]
    end

    users_from_cache = User.get_users_batch(user_ids)
    users_from_cache.each do |user|
      users_map[user['id']] = user.get_user_response_hash_for_dm(@user)
    end

    # add error for batch size exceeded user ids
    if batch_size_exceeded_user_ids.present? && batch_size_exceeded_user_ids.length > 0
      error = {
        error_code: 500,
        message: "Batch size exceeded for user_ids #{batch_size_exceeded_user_ids}",
      }
      errors << error
    end

    # render response
    render json: { users: users_map, errors: errors }, status: :ok
  end

  # GET /users/1
  def show
    user_blocked = if @request_user.id == @user.id
                     false
                   else
                     BlockedUser.where(blocked_user: @request_user,
                                       user: @user).exists?
                   end

    logged_in_user_blocked = if @request_user.id == @user.id
                               false
                             else
                               BlockedUser.where(blocked_user: @user,
                                                 user: @request_user).exists?
                             end
    # if user_blocked
    #   render json: { success: false, message: '‌బ్లాక్ చేయబడ్డారు!' }, status: :forbidden
    #   return
    # end

    user_badge = @request_user.get_badge_role&.get_json
    @request_user.photo.compressed_url!(size: 1024) if @request_user.photo.present?

    user_badge['verification_text'] = "*#{@request_user.name}* పేరుతో ఉన్న ఈ అకౌంట్ వారిదే అని మరియు వారిని *#{user_badge['description']}* గా ప్రజా యాప్ టీమ్ స్వయంగా వెరిఫై చేసింది" if user_badge.present?

    # track profile view of user
    ProfileViewWorker.perform_async(@request_user.id, @user.id, Time.zone.now)

    is_premium = @user.phone != Constants.app_store_account_phone &&
                 @request_user.eligible_for_premium_features?

    render json: {
      id: @request_user.id,
      name: @request_user.name,
      phone: @request_user.id == @user.id ? @request_user.phone : @request_user.generate_random_phone,
      photo: @request_user.photo,
      email: @request_user.email,
      created_at: @request_user.created_at,
      posts_count: @request_user.get_posts_count,
      mentions_count: 0,
      likes_count: 0, # @request_user.get_likes_count
      village: User.core_party_user_ids.include?(@request_user.id) ? nil : @request_user.village, # TODO: remove this hack
      short_bio: @request_user.short_bio,
      dob: @request_user.dob,
      gender: @request_user.gender,
      gender_new: @request_user.gender_new,
      loggedInUser: @request_user.id == @user.id,
      followers_count: @request_user.followers_count,
      following_count: @request_user.following_count,
      verified: @request_user.verified,
      show_phone: @request_user.show_phone,
      circles: [],
      follows: @request_user.id == @user.id ? false : UserFollower.where(user: @request_user, follower: @user).exists?,
      blocked: user_blocked,
      show_message_action: @request_user.dm_enabled?,
      logged_in_user_blocked: logged_in_user_blocked,
      profile_tags: [],
      badge: user_badge,
      avatar_color: @request_user.avatar_color,
      is_premium: is_premium,
      eligible_for_premium: is_premium # This is required a true for "Premium" logo to be clickable
    }, status: :ok
  end

  def support_sheet
    source = params[:source] || ""

    badge_role = @user.get_badge_role_including_unverified

    is_user_atleast_paid_once = SubscriptionCharge.where(user_id: @user.id, status: :success).where("charge_amount > 1").exists?

    analytics = {
      is_layout_exists: false,
      is_badge_exists: false,
      subscription_status: @user.get_subscription_status,
      is_user_atleast_paid_once: is_user_atleast_paid_once
    }

    options = []

    if @user.has_premium_layout?
      user_selected_frame_types = UserFrame.get_user_frame_types(@user.id)
      photos_sub_options = [
        build_support_option(identifier: "photo_change_cutout", text: "support_sheet.photo_change.cutout.text",
                             type: "deeplink", deeplink: "/poster-photo-selection?user_photo_type=cutout&source=support_sheet",
                             button_text: "support_sheet.photo_change.cutout.button_text",
                             button_icon: "person_outline",
                             analytics: analytics)
      ]
      photos_sub_options << build_support_option(identifier: "photo_change_family_cutout", text: "support_sheet.photo_change.family_cutout.text",
                                                 type: "deeplink", deeplink: "/poster-photo-selection?user_photo_type=family_cutout&source=support_sheet",
                                                 button_text: "support_sheet.photo_change.family_cutout.button_text",
                                                 button_icon: "diversity_1_outlined",
                                                 analytics: analytics) if user_selected_frame_types.include?(:family_frame_premium)

      photos_sub_options << build_support_option(identifier: "photo_change_hero_cutout", text: "support_sheet.photo_change.hero_cutout.text",
                                                 type: "deeplink", deeplink: "/poster-photo-selection?user_photo_type=hero_cutout&source=support_sheet",
                                                 button_text: "support_sheet.photo_change.hero_cutout.button_text",
                                                 button_icon: "group_outlined",
                                                 analytics: analytics) if user_selected_frame_types.include?(:hero_frame_premium)

      analytics[:is_layout_exists] = true

      options << build_support_option(identifier: "photo_change", text: "support_sheet.photo_change.text", type: "deeplink",
                                      deeplink: "/poster-photo-selection?source=support_sheet", button_text: "support_sheet.photo_change.button_text",
                                      sub_options: photos_sub_options,
                                      button_icon: "photo_camera_outlined",
                                      analytics: analytics)

      options << build_support_option(identifier: "protocol_change", text: "support_sheet.protocol_change.text", type: "api",
                                      api_url: Constants.support_request_callback_url, button_text: "support_sheet.protocol_change.button_text",
                                      button_icon: "account_circle_outlined",
                                      body_data: { reason: "protocol_change" }, analytics: analytics)
    end

    if badge_role.present?
      analytics[:is_badge_exists] = true

      options << build_support_option(identifier: "badge_change", text: "support_sheet.badge_change.text", type: "api",
                                      api_url: Constants.support_request_callback_url, button_text: "support_sheet.badge_change.button_text",
                                      button_icon: "workspace_premium_outlined",
                                      body_data: { reason: "badge_change" }, analytics: analytics)
    else
      if @user.has_premium_layout?
        options << build_support_option(identifier: "badge_request", text: "support_sheet.badge_request.text", type: "api",
                                        api_url: Constants.support_request_callback_url, button_text: "support_sheet.badge_request.button_text",
                                        button_icon: "workspace_premium_outlined",
                                        body_data: { reason: "badge_request" }, analytics: analytics)
      else
        options << build_support_option(identifier: "badge_request", text: "support_sheet.badge_request.text", type: "deeplink",
                                        deeplink: "/premium-experience?source=support_sheet_badge", button_text: "support_sheet.badge_request.button_text",
                                        button_icon: "workspace_premium_outlined",
                                        analytics: analytics)
      end
    end

    options << build_support_option(identifier: "premium_request", text: "support_sheet.premium_request.text", type: "deeplink",
                                    deeplink: "/premium-experience?source=support_sheet_premium", button_text: "support_sheet.premium_request.button_text",
                                    button_icon: "crown",
                                    analytics: analytics) unless @user.eligible_for_premium_features?

    options << build_support_option(identifier: "feature_request", text: "support_sheet.feature_request.text", type: "api",
                                    api_url: Constants.support_request_callback_url, button_text: "support_sheet.feature_request.button_text",
                                    button_icon: "rocket_launch_outlined",
                                    body_data: { reason: "feature_request" }, analytics: analytics) if source != 'layout_feedback' && @user.eligible_for_premium_features?

    subscription = @user.cancellable_latest_subscription
    if source != 'layout_feedback' && AppVersionSupport.cancellation_flow_v2_supported? && subscription.present? && @user.is_poster_subscribed && subscription.may_cancel?
      deeplink = @user.get_cancellation_deeplink(subscription: subscription, source: 'support_sheet')

      options << build_support_option(
        identifier: "cancel_subscription",
        text: "cancellation_flow_v2.support_sheet.text",
        type: "deeplink",
        deeplink: deeplink,
        button_text: "cancellation_flow_v2.support_sheet.button_text",
        button_icon: "crown",
        analytics: analytics
      )
    end

    options << build_support_option(identifier: "others", text: "support_sheet.others.text", type: "api",
                                    api_url: Constants.support_request_callback_url, button_text: "support_sheet.others.button_text",
                                    button_icon: "help_outline",
                                    body_data: { reason: "others" }, analytics: analytics)

    if @user.eligible_for_premium_features? && subscription.present?
      analytics[:duration_in_months] = subscription.plan.duration_in_months
    end

    response = {
      title: I18n.t("support_sheet.title"),
      sub_title: I18n.t("support_sheet.sub_title"),
      options: options,
      is_premium_user: @user.is_poster_subscribed,
      analytics_params: analytics
    }

    render json: response, status: :ok
  end

  def support_callback_request
    reason = params[:reason] || ""
    source = params[:source] || "" # source will be coming from Version: 2502.08.00 (Layout Feedback Supported Version)
    response_text = log_to_support_requests_sheet(@user, reason, source)

    render json: { message: response_text }, status: :ok
  end

  def log_to_support_requests_sheet(user, reason, source)
    user_id = user.id
    phone = user.phone
    name = user.name
    time_stamp = Time.zone.now.strftime('%Y-%m-%d %H:%M:%S')
    rm_user_name = user.get_rm_user&.name || ""
    response_text = I18n.t("support_sheet.others.response_text")
    if reason == "badge_change"
      response_text = I18n.t("support_sheet.badge_change.response_text")
    elsif reason == "protocol_change"
      response_text = I18n.t("support_sheet.protocol_change.response_text")
    elsif reason == "feature_request"
      response_text = I18n.t("support_sheet.feature_request.response_text")
    end

    subscription_status = user.get_subscription_status&.humanize
    badge = user.get_badge_role&.get_description || ""

    is_user_atleast_paid_once = SubscriptionCharge.where(user_id: user.id, status: :success).where("charge_amount > 1").exists?

    if UserPosterLayout.where(entity_id: user.id, entity_type: user.class.name).exists?
      layout_status = "Layout Created"
    else
      layout_status = "No Layout"
    end

    data = [
      [time_stamp, user_id, name, phone, reason.humanize, rm_user_name, layout_status, badge, subscription_status,
       is_user_atleast_paid_once, source.humanize]
    ]
    spreadsheet_id = '1KTwyQMdQfLOynvjlTZaSy690YtH4wyKmhXhYVscOl-0'

    ExportDataToGoogleSheets.perform_async(user.id, data, spreadsheet_id)
    Zoho::CreateTicket.perform_async(user.id, reason, source, {
      rm_name: rm_user_name,
      layout_status: layout_status,
      subscription_status: subscription_status,
      is_user_atleast_paid_once: is_user_atleast_paid_once,
      badge: badge,
    })

    response_text
  end

  def invite_contacts
    invites = []

    params[:contacts].each do |contact|
      phone_object = TelephoneNumber.parse(contact[:phone], :IN)
      next unless phone_object.valid?([:mobile])

      phone = phone_object.national_number(formatted: false).slice(1..-1).to_i

      invites << UserInvite.new(name: contact[:name], phone: phone, user: @user) if !phone.nil? && phone != 0
    end

    UserInvite.import invites, on_duplicate_key_ignore: true, batch_size: 100 if invites.count.positive?

    render json: { success: true }, status: :ok
  end

  def follow_contacts
    to_follow_user_ids = params[:contacts].map { |e| e['user_id'] }.compact

    to_follow_user_ids.each do |user_id|
      UserFollower.create!(user_id: user_id, follower_id: @user.id, source_of_follow: :follow_contacts)
    rescue StandardError => e
      Honeybadger.notify(e)
    end

    render json: { success: true }, status: :ok
  end

  def follow_contacts_v1
    # get ignored ids from user contacts
    ignored_ids = params[:ignored_ids] || []

    ignored_ids = ignored_ids.map(&:to_i)
    # get signed up user ids of user contacts list
    user_ids = @user.not_yet_followed_signed_up_user_ids_of_user_contacts

    # remove ignored ids from signed up user ids of user contacts list
    user_ids = user_ids - ignored_ids

    # do a batch of 50 user ids and send it to worker to follow
    user_ids.in_groups_of(50, false) do |user_ids_group|
      BulkFollowUsersWorker.perform_async(@user.id, user_ids_group, @app_version.to_s, @app_build_number, "follow_contacts")
    end

    if user_ids.count > 0 && @user.contact_screen_max_seen_limit_reached?
      @user.set_show_contacts_screen_value_to_redis(false, 90.days.to_i)
    end

    render json: { success: true }, status: :ok
  end

  def validate_contacts
    valid_contacts = []
    invalid_contacts = []
    params[:contacts].each do |contact|
      phone = contact[:phone]
      phone = phone.strip.gsub(' ', '').gsub('+91', '').gsub('-', '').gsub('(', '').gsub(')', '')
      phone = phone.reverse[0...10].reverse if phone.length > 10
      phone = phone.to_i

      if !phone.nil? && phone != 0 && phone.to_s.length == 10
        if phone != @user.phone
          contact[:phone] = phone
          valid_contacts << contact
        end
      else
        invalid_contacts << contact
      end
    end
    [valid_contacts, invalid_contacts]
  end

  def search_contacts
    return render json: { success: false, message: 'Not in use' }, status: :bad_request

    all_contacts = []
    invalid_contacts = []
    found_contacts = []
    not_found_contacts = []

    # Skylight.instrument title: 'Validating user contacts' do
    all_contacts, invalid_contacts = validate_contacts
    # end

    found_phones = []
    # Skylight.instrument title: 'Searching contacts' do
    all_contacts.in_groups_of(100, false) do |contacts_group|
      User.select('id, phone').where(phone: contacts_group.map do |c|
        c[:phone]
      end).active.all.each do |u|
        found_phones << {
          phone: u.phone,
          user_id: u.id
        }
      end
    end
    # end

    # Skylight.instrument title: 'Filtering all contacts into found_contacts and not_found_contacts' do
    all_contacts.each do |contact|
      match_index = found_phones.index(found_phones.find { |l| l[:phone] == contact[:phone] })
      if !match_index.nil? && match_index >= 0
        found_contacts << {
          name: contact[:name],
          phone: contact[:phone].to_s,
          user_id: found_phones[match_index][:user_id]
        }
      else
        not_found_contacts << {
          name: contact[:name],
          phone: contact[:phone].to_s
        }
      end
    end
    # end

    follow_users = []
    # Skylight.instrument title: 'Filtering followed users' do
    found_contacts.in_groups_of(100, false) do |contacts_group|
      UserFollower.select('id, user_id')
                  .where(follower_id: @user.id)
                  .where(user_id: contacts_group.map { |c| c[:user_id] }).all.each do |uf|
        follow_users << {
          user_id: uf.user_id
        }
      end
    end
    # end

    follow_contacts = []

    found_contacts.each do |contact|
      match_index = follow_users.index(follow_users.find { |l| l[:user_id] == contact[:user_id] })
      # If the contact is not in follow_users list, then suggest for follow
      next unless match_index.nil? || match_index.negative?

      follow_contacts << {
        name: contact[:name],
        phone: contact[:phone].to_s,
        user_id: contact[:user_id]
      }
    end

    unless @user.internal
      # import validated & not found contacts
      # Skylight.instrument title: 'Importing validated contact' do
      invites = []
      not_found_contacts.each do |contact|
        invites << UserInvite.new(name: contact[:name], phone: contact[:phone], user: @user, invited: false)
      end
      invites.each_slice(200) do |invites_batch|
        # WARN: This below line should be used with a worker instead of .delay
        UserInvite.delay(queue: :low).import invites_batch, on_duplicate_key_ignore: true
      end
      # end

      # import invalid contacts
      # Skylight.instrument title: 'Importing invalid contact' do
      invalid_invites = []
      invalid_contacts.each do |contact|
        invalid_invites << InvalidUserInvite.new(name: contact[:name], phone: contact[:phone], user: @user)
      end
      invalid_invites.each_slice(500) do |invalid_invites_batch|
        InvalidUserInvite.import invalid_invites_batch, on_duplicate_key_ignore: true, batch_size: 100
      end
      # end
    end

    render json: { follow: follow_contacts, found: found_contacts, not_found: not_found_contacts }, status: :ok
  end

  def upload_contacts
    valid_contacts, invalid_contacts = validate_contacts
    valid_contacts.in_groups_of(100) do |valid_contact_batch|
      batch = Sidekiq::Batch.new
      batch.on(:complete, ProcessValidContactsBatch, user_id: @user.id)
      batch.jobs do
        valid_contact_batch.compact.each do |contact|
          QueueValidContact.perform_async(contact.to_json, @user.id)
        end
      end
    end

    invalid_contacts.in_groups_of(100) do |invalid_contact_batch|
      batch = Sidekiq::Batch.new
      batch.on(:complete, ProcessInvalidContactsBatch, user_id: @user.id)
      batch.jobs do
        invalid_contact_batch.compact.each do |contact|
          QueueInvalidContact.perform_async(contact.to_json, @user.id)
        end
      end
    end
    # create or update last contacts uploaded at time of user
    Metadatum.find_or_create_by(key: Constants.last_contacts_uploaded_time_key, entity: @user).update(value: Time.zone.now)

    # trigger contact notification worker
    @user.trigger_contact_notification_worker

    render json: {
      success: true,
      message: 'contacts uploaded'
    }, status: :ok
  end

  def search_tags
    user_tags = []

    if !params[:name].nil? && params[:name].present?
      User.joins('INNER JOIN user_followers ON user_followers.user_id = users.id')
          .active
          .where("user_followers.follower_id = ? AND user_followers.active = 1 AND users.name LIKE '%#{params[:name]}%'", @user.id)
          .all
          .map do |user|
        user.phone = user.generate_random_phone
        user_tags << user
      end

      @user.circles.each do |circle|
        User.joins('INNER JOIN user_circles ON users.id = user_circles.user_id')
            .active
            .where("user_circles.circle_id = ? AND user_circles.active = 1 AND users.name LIKE '%#{params[:name]}%'", circle.id)
            .all
            .map do |user|
          user.phone = user.generate_random_phone
          user_tags << user unless user_tags.include?(user)
        end
      end
    end

    render json: user_tags, status: :ok
  end

  def block
    if params[:reason].present? && params[:reason]['name'].present?
      begin
        # creating a blocked user object to validate it and will validate it and handle it
        blocked_user = BlockedUser.new(blocked_user: @request_user, user: @user, reason: params[:reason]['name'])

        if blocked_user.valid? && blocked_user.errors.blank?
          # Send blocked data to DM service
          # Here I have used Camelcase for body instead of snake case because DM service is using camelcase
          body = {
            "userId": @user.id.to_s,
            "blockedUserId": @request_user.id.to_s
          }
          url = Constants.get_dm_url + "/conversations/block-user"
          DmUtil.blocked_info_callback_to_dm(url, body)
          blocked_user.save!
          UserFollower.where(user_id: [@user.id, @request_user.id], follower_id: [@user.id, @request_user.id]).destroy_all
          render json: {
            success: true,
            message: 'బ్లాక్ చేయబడ్డారు!' # Blocked!
          }, status: :ok
        else
          if blocked_user.errors.size == 1 && blocked_user.errors.first.type == :taken
            render json: {
              success: true,
              message: 'బ్లాక్ చేయబడ్డారు!' # Blocked!
            }, status: :ok
          else
            render json: {
              success: false,
              message: blocked_user.errors.full_messages.first
            }, status: :bad_request
          end
        end
      rescue => e
        Honeybadger.notify(e)
        render json: { success: false, message: e.message }, status: :bad_request
      end
    else
      render json: { success: false, message: 'Reason should be specified' }, status: :bad_request
    end
  end

  def unblock

    begin
      # Send un-blocked data to DM service
      # Here I have used Camelcase for body instead of snake case because DM service is using camelcase
      body = {
        "userId": @user.id.to_s,
        "blockedUserId": @request_user.id.to_s
      }
      url = Constants.get_dm_url + "/conversations/unblock-user"
      DmUtil.blocked_info_callback_to_dm(url, body)
      BlockedUser.where(blocked_user: @request_user, user: @user).first&.destroy

      render json: {
        success: true,
        message: 'అన్‌బ్లాక్ చేయబడ్డారు!' # Unblocked!
      }, status: :ok
    rescue => e
      Honeybadger.notify(e)
      render json: { success: false, message: e.message }, status: :bad_request
    end
  end

  # PUT /users/1/report
  def report
    if params[:reason].present? && params[:reason]['name'].present?
      Report.create!(reference: @request_user, user: @user, report_reason: params[:reason]['name'])

      render json: {
        success: true,
        message: 'నివేదించబడింది!' # Reported!
      }, status: :ok
    else
      render json: { success: false, message: 'Reason should be specified' }, status: :bad_request
    end
  end

  # GET /users/1/followers

  def followers
    # if @app_version > Gem::Version.new('1.12.0')
    #   followers_v2
    # else
    followers_v1
    # end
  end

  def followers_v1
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?
    count = 0
    count = params[:count].to_i if params[:count].present?

    render json: @request_user.get_followers(@user, offset, count), status: :ok
  end

  # removed followers_v2 code as we stopped indexing followers in index_search_entity

  # GET /users/1/following

  def following
    # if @app_version > Gem::Version.new('1.12.0')
    #   following_v2
    # else
    following_v1
    # end
  end

  def following_v1
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?
    count = 0
    count = params[:count].to_i if params[:count].present?

    render json: @request_user.get_following(@user, offset, count), status: :ok
  end

  # removed following_v2 code as we stopped indexing followers in index_search_entity

  # GET /badge-notify
  def get_badge_notification
    user_role = @user.get_badge_role_including_unverified
    if user_role.present?
      user_role.update(is_celebrated: true) if user_role.verified_verification_status?
      render json: @user.get_badge_notification_data(include_unverified_badge: true), status: :ok
    else
      render json: { success: false, message: "User don't have badge" }, status: :unprocessable_entity
    end
  end

  # GET /my-profile
  def profile
    user_role = @user.get_badge_role_including_unverified
    user_badge_json = nil
    notify_badge_issue = false

    if user_role.present?
      user_badge_json = user_role.get_json
      notify_badge_issue = user_role.can_notify_user?
      if user_role.unverified_verification_status? && AppVersionSupport.supports_unverified_badge_with_verification_text?
        user_badge_json[:verification_text] = I18n.t('unverified_badge_pending_text')
      end
    end

    permissions = @user.get_all_internal_permissions

    referral_points = @user.get_referral_points
    @user.photo.compressed_url!(size: 1024) if @user.photo.present?
    render json: {
      id: @user.id,
      name: @user.name,
      phone: permissions.include?('super_admin') ? @user.phone : @user.generate_random_phone,
      email: @user.email,
      short_bio: @user.short_bio,
      dob: @user.dob,
      photo: @user.photo,
      created_at: @user.created_at,
      gender: @user.gender,
      gender_new: @user.gender_new,
      posts_count: @user.get_posts_count,
      mentions_count: 0,
      likes_count: 0, # @user.get_likes_count
      village: User.core_party_user_ids.include?(@user.id) ? nil : @user.village, # TODO: remove this hack
      followers_count: @user.followers_count,
      following_count: @user.following_count,
      verified: @user.verified,
      show_phone: @user.show_phone,
      circles: [],
      loggedInUser: true,
      follows: false,
      profile_tags: [],
      badge: user_badge_json,
      avatar_color: @user.avatar_color,
      notify_badge_issue: notify_badge_issue,
      internal_journalist: @user.internal_journalist,
      referral_points: referral_points
    }, status: :ok
  end

  def get_dm_recommended_users_and_user_ids(count, loaded_user_ids = [], recommended_user_ids = [])

    follower_ids = []
    following_ids = []
    user_blocked_ids = []
    user_blocked_by_ids = []
    if recommended_user_ids.blank? || recommended_user_ids.present? && recommended_user_ids.length < count
      recommended_user_ids = recommended_user_ids.present? ? recommended_user_ids : []
      count = count - recommended_user_ids.length
      # extracting blocked users and blocked by users of logged in users
      user_blocked_ids = @user.get_blocked_user_ids
      user_blocked_by_ids = @user.get_blocked_by_user_ids
      blocked_ids = user_blocked_ids + user_blocked_by_ids

      # adding logged in user id to exclude list
      user_ids_to_exclude = blocked_ids + [@user.id] + recommended_user_ids + loaded_user_ids

      # fetching user contacts ids
      user_contacts_ids = UserContactSuggestion.where(user_id: @user.id).where.not(phone_user_id: user_ids_to_exclude).pluck(:phone_user_id)

      # getting followers and following ids of logged in user if user's following count is less than 100
      if @user.following_count < Constants.get_max_following_count_to_fetch_dm_recommended_users
        following_ids = UserFollower.where(follower_id: @user.id).pluck(:user_id)
      end

      if @user.followers_count < Constants.get_max_followers_count_to_fetch_dm_recommended_users
        follower_ids = UserFollower.where(user_id: @user.id).pluck(:follower_id)
      end

      mutual_following_ids = following_ids & follower_ids

      user_party_circle_ids = @user.get_user_joined_party_circle_ids

      available_ids = recommended_user_ids + user_contacts_ids
      available_ids = available_ids.uniq
      if available_ids.length > count
        recommended_user_ids = available_ids.take(count)
      else
        recommended_users_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: DmUtil.dm_recommended_users_es_query(
          @user,
          follower_ids,
          following_ids,
          mutual_following_ids,
          user_contacts_ids,
          user_party_circle_ids,
          user_ids_to_exclude,
          count
        )

        recommended_users_es['hits']['hits'].each do |hit|
          if hit.present? && hit['fields'].present?
            user_fields = hit['fields']
            user_id = user_fields['id'].first.to_i
            recommended_user_ids << user_id
          end
        end
      end
    end

    recommended_user_ids = recommended_user_ids.take(count)

    page_follower_ids = UserFollower.where(user_id: @user.id, follower_id: recommended_user_ids).pluck(:follower_id)
    page_following_ids = UserFollower.where(user_id: recommended_user_ids, follower_id: @user.id).pluck(:user_id)

    users_from_cache = User.get_users_batch(recommended_user_ids)
    recommended_users = users_from_cache.map do |user|
      user.get_user_response_hash_for_dm(@user, page_follower_ids, page_following_ids, user_blocked_ids, user_blocked_by_ids)
    end
    recommended_users.compact!
    {
      users: recommended_users,
      user_ids: recommended_user_ids
    }
  end

  def get_dm_recommended_users_for_share_v1

    count = params[:count].present? ? params[:count].to_i : 10
    loaded_user_ids = params[:loaded_user_ids].present? ? params[:loaded_user_ids].map(&:to_i) : []
    recommended_users_and_ids = get_dm_recommended_users_and_user_ids(count, loaded_user_ids, [])
    next_page_query_params = {
      count: count
    }
    render json: { users: recommended_users_and_ids[:users], next_page_query_params: next_page_query_params, success: true }, status: :ok
  end

  def get_dm_recommended_users_for_new_conversation_v1
    count = params[:count].present? ? params[:count].to_i : 10
    loaded_user_ids = params[:loaded_user_ids].present? ? params[:loaded_user_ids].map(&:to_i) : []
    recommended_users_and_ids = get_dm_recommended_users_and_user_ids(count, loaded_user_ids, [])
    next_page_query_params = {
      count: count
    }
    render json: { users: recommended_users_and_ids[:users], next_page_query_params: next_page_query_params, success: true }, status: :ok
  end

  def get_dm_recommended_users_for_share
    count = 10
    count = params[:count].to_i if params[:count].present?
    recommended_user_ids_cache = $redis.get(Constants.dm_recommended_users_redis_cache_key(@user.id))
    recommended_users_and_ids = get_dm_recommended_users_and_user_ids(count, [], recommended_user_ids_cache.present? ? JSON.parse(recommended_user_ids_cache) : [])
    $redis.set(Constants.dm_recommended_users_redis_cache_key(@user.id), recommended_users_and_ids[:user_ids].to_s, ex: 1.hour)

    render json: { users: recommended_users_and_ids[:users], success: true }, status: :ok
  end

  def get_dm_recommended_users_for_new_conversation
    count = 10
    count = params[:count].to_i if params[:count].present?
    # limiting count to 50
    recommended_user_ids_cache = $redis.get(Constants.dm_recommended_users_redis_cache_key(@user.id))
    recommended_users_and_ids = get_dm_recommended_users_and_user_ids(count, [], recommended_user_ids_cache.present? ? JSON.parse(recommended_user_ids_cache) : [])
    $redis.set(Constants.dm_recommended_users_redis_cache_key(@user.id), recommended_users_and_ids[:user_ids].to_s, ex: 1.hour)

    render json: { users: recommended_users_and_ids[:users], success: true }, status: :ok
  end

  def get_posts
    # user_blocked = @request_user.id == @user.id ? false : BlockedUser.where(blocked_user: @request_user, user: @user).exists?
    #
    # if user_blocked
    #   render json: { success: false, message: '‌బ్లాక్ చేయబడ్డారు!' }, status: :forbidden
    #   return
    # end

    offset = 0
    offset = params[:offset].to_i if params[:offset].present?
    count = 0
    count = params[:count].to_i if params[:count].present?
    render json: @request_user.get_posts(@user, offset, count, @app_version), status: :ok
  end

  def get_liked_posts
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?
    count = 0
    count = params[:count].to_i if params[:count].present?

    render json: @request_user.get_liked_posts(@user, offset, count, @app_version), status: :ok
  end

  def search_transliteration(value)
    request_uri = Timeout.timeout(0.1) do
      URI("https://inputtools.google.com/request?text=#{value}&itc=te-t-i0-und&num=2&cp=0&cs=1&ie=utf-8&oe=utf-8&app=demopage")
    end
    response = Net::HTTP.get_response(request_uri)
    response_arr = JSON.parse response.read_body
    response_arr[1][0][1]
  rescue Timeout::Error
    ''
  end

  # GET /search
  def search
    if @app_version > Gem::Version.new('1.12.0')
      search_v2
      # if @user.is_test_user?
      #   search_v3
      # else
      #   search_v2
      # end
    else
      search_v1
    end
  end

  def search_v1
    value = params[:value].to_s
    search_result = []
    if params[:value].present? && !params[:value].empty?

      search_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: {
        query: {
          bool: {
            should: [
              {
                multi_match: {
                  query: value,
                  type: 'phrase_prefix',
                  fields: %w[name phone],
                  boost: 2
                }
              },
              {
                multi_match: {
                  query: value,
                  fields: %w[name phone],
                  fuzziness: 'AUTO'
                }
              }
            ],
            minimum_should_match: 1,
            filter: {
              term: { type: 'user' }
            }
          }
        }
      }

      search_es['hits']['hits'].map do |hit|
        search_feed = hit['_source']
        id = search_feed['id']
        user = user_info(id)
        search_result << user if user.present?
      end
    else
      users = if @user.circles.count.positive?
                @user.circles.first.get_users(@user, 0, 10)
              else
                User.get_ordered_users_list(@user, 0, 10)
              end
      search_result << users
    end
    render json: search_result, status: :ok
  end

  def search_v2
    value = params[:value].to_s
    search_result = []
    begin
      search_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: SearchUtil.search_query(value, @user)
      search_es['hits']['hits'].each do |hit|
        search_feed = hit['fields']
        type = search_feed['type'].first
        id = search_feed['id'].first
        case type
        when 'user'
          user = user_info(id)
          search_result << user
        when 'circle'
          circle = circle_info(id)
          search_result << circle
        else
          next
        end
      end

      search_result.compact!
    rescue StandardError => e
      render json: { success: false, error: e.to_s }
      return
    end

    render json: search_result, status: :ok
  end

  # GET /dm_search
  def dm_search
    value = params[:value].to_s
    resulting_user_ids = []
    DmUtil.search(value, @user).each do |hit|
      search_feed = hit['fields']
      type = search_feed['type'].first
      id = search_feed['id'].first
      case type
      when 'user'
        resulting_user_ids << id
      else
        next
      end
    end
    users_from_cache = User.get_users_batch(resulting_user_ids)
    search_result = users_from_cache.map do |user|
      user.get_user_response_hash_for_dm(@user)
    end
    render json: search_result, status: :ok
  end

  def user_info(id)
    user = User.find(id)
    return unless user.active_status?

    user.photo&.compressed_url!(size: 200)

    {
      type: 'user',
      id: user.id,
      name: user.name,
      phone: user.generate_random_phone,
      photo: user.photo,
      village: User.core_party_user_ids.include?(user.id) ? nil : user.village, # TODO: remove this hack
      followers_count: user.followers_count,
      loggedInUser: user.id == @user.id,
      follows: user.id == @user.id ? false : UserFollower.where(user: user, follower: @user).exists?,
      badge: user.get_badge_role&.get_json,
      avatar_color: user.avatar_color
    }
  end

  def circle_info(id)
    circle = Circle.find(id)
    circle.photo&.compressed_url!(size: 200)

    {
      type: 'circle',
      id: circle.id,
      name: circle.name,
      name_en: circle.name_en,
      photo: circle.photo,
      level: circle.level,
      level_verbose: circle.level_verbose,
      type_of_organisation: circle.type_of_organisation,
      profile_photo: circle.photo,
      circle_background: circle.circle_background,
      circle_type: circle.circle_type,
      is_user_joined: circle.location_circle_type? ? true : circle.check_user_joined(@user),
      members_count: circle.get_members_count,
      banner: circle.banner
    }
  end

  # POST /save-profile
  def update
    update_photo = true
    update_photo = false if params[:photo_update].present? && params[:photo_update] == 'no_action'

    if update_photo
      if !params[:photo].nil?
        photo = Photo.upload(params[:photo], @user.id)

        unless photo.nil?
          @user.photo = photo
          # ThumbnailGeneratorWorker.perform_async(photo.id)
        end
      else
        @user.photo = nil
      end
    end

    begin
      dob = @user.dob
      dob = params[:dob] if params[:dob].present? && !params[:dob].empty?

      gender = @user.gender
      gender = @user.gender_before_type_cast if !gender.nil? && @app_version > Gem::Version.new('1.7.9')

      gender = params[:gender].downcase if params[:gender].present? && !params[:gender].empty?
      gender = params[:gender].to_i if @app_version > Gem::Version.new('1.7.9')

      show_phone = @user.show_phone
      show_phone = params[:show_phone] if params[:show_phone].present? && !params[:show_phone].empty?

      params[:short_bio] = '' if params[:short_bio].nil? || params[:short_bio].empty?
      @user.update!(name: params[:name], short_bio: params[:short_bio].squish, dob: dob, gender: gender,
                    show_phone: show_phone)
      # IndexPostsUser.perform_async(@user.id)

      user_hash = @user.get_user_response_hash(@app_version, include_unverified_badge: true)
      render json: user_hash, status: :ok
    rescue StandardError
      if @user.errors[:name].any?
        render json: {
          message: I18n.t('errors.empty_name_error')
        }, status: :unprocessable_entity
      else
        render json: {
          message: I18n.t('errors.profile_details_save_error') # Failed to save profile info
        }, status: :unprocessable_entity
      end
    end
  end

  # PUT /users/1/follow
  def follow
    if @request_user.id == @user.id
      return render json: { success: false, message: I18n.t('errors.follow_failed_error') }, status: :bad_request
    end

    following = @request_user.follow(@user.id, params[:source])
    if following
      @user.user_groups.each do |user_group|
        PopulateFollows.perform_async(@request_user.id, user_group.id)
      end
      render json: { success: true }, status: :ok
    else
      render json: {
        success: false,
        message: I18n.t('errors.follow_failed_error') # Unable to follow
      }, status: :unprocessable_entity
    end
  end

  # PUT /users/1/unfollow
  def unfollow
    uf = UserFollower.where(user: @request_user, follower: @user).first
    uf&.destroy

    render json: { success: true }, status: :ok
  end

  def login_with_truecaller
    @user = nil
    phone = nil

    if !params[:phone].present? || !params[:payload].present? || !params[:signature].present?
      render json: { success: false, message: 'Phone is mandatory' }, status: :bad_request
    else
      verified = VerifySignatureUtil.verify('RSA',
                                            User.get_truecaller_public_key,
                                            params[:payload],
                                            params[:signature],
                                            'sha512')

      render json: { success: false, message: 'Invalid request' }, status: :bad_request and return unless verified

      phone_object = TelephoneNumber.parse(params[:phone], :IN)
      phone = phone_object.national_number(formatted: false).slice(1..-1).to_i if phone_object.valid?([:mobile])

      if !phone.nil? && phone != 0
        @user = User.find_by_phone(phone)

        if @user.nil?
          gender = nil
          gender = params[:gender].to_i if params[:gender].present?
          email = nil
          email = params[:email].downcase if params[:email].present?

          begin
            @user = User.create(phone: phone,
                                internal: false,
                                gender: gender,
                                app_version: @app_version.to_s,
                                app_build_number: @app_build_number)
            @user.metadatum.create(key: Constants.truecaller_user_email_key, value: email)
            if params[:photo_url].present?
              @user.photo = Photo.create(user: @user, url: params[:photo_url], service: :aws)
              @user.save!
              UploadPhotoToS3UsingUrl.perform_in(1.minutes, @user.photo.id, @user.photo.url, @user.id)
            end
          rescue => e
            Honeybadger.notify(e, context: { user_id: @user&.id, phone: phone })
          end

          if @user.nil?
            return render json: { success: false, message: 'Something went wrong' }, status: :bad_request
          end
        elsif !@user.can_login?
          return render json: { success: false, message: I18n.t('errors.account_suspended') }, status: :bad_request
        end

        is_login_flow = !@user.pre_signup_status?

        @user.activate! if is_login_flow && @user.may_activate?

        user_hash = @user.get_user_response_hash(@app_version, include_unverified_badge: true)

        # if it is login, do the following; Otherwise ignore
        if is_login_flow
          user_hash[:token] = @user.generate_login_token(@app_version, @app_os)
          user_hash[:show_party_suggestions] = false

          response.set_header(Constants.jwt_access_token_header, @user.generate_jwt_token)
          @user.update_login_time

          # Reset the show contacts screen value to nil after login
          # Reset the contact screen seen value to nil after login
          @user.del_redis_keys_of_contact_screen_after_login
        end

        render json: user_hash, status: :ok
      else
        render json: { success: false, message: 'Something went wrong' }, status: :bad_request
      end
    end
  end

  # POST /get-otp
  def get_otp
    @user = nil

    # Validate phone number
    phone = params[:phone].to_i
    is_internal = User.is_internal(phone)

    if !is_internal
      phone_object = TelephoneNumber.parse(phone, :IN)
      if !phone_object.valid?([:mobile])
        return render json: { success: false, message: I18n.t('errors.invalid_phone_number') }, status: :bad_request
      end

      phone = phone_object.national_number(formatted: false).slice(1..-1).to_i
    end

    # If a valid phone number is provided, check if the user exists & create if not
    @user = User.find_by_phone(phone)

    if @user.nil?
      @user = User.create(phone: phone,
                          internal: is_internal,
                          app_version: @app_version.to_s,
                          app_build_number: @app_build_number)
    elsif !@user.can_login?
      return render json: { success: false, message: I18n.t('errors.account_suspended') }, status: :bad_request
    end

    # for some reason if the user is still nil, return error
    return render json: { success: false, message: I18n.t('errors.invalid_phone_number') }, status: :bad_request if @user.nil?

    hash = '-PRAJA APP'
    hash = params[:hash] if !params[:hash].nil? && !params[:hash].empty?

    counter, otp = @user.get_unique_otp_with_counter
    @user.send_otp(otp, counter, hash)

    user_hash = @user.get_user_response_hash(@app_version, include_unverified_badge: true)

    # Send OTP only in development & test environments
    # Must send OTP as string, as the App assumes it is a string
    user_hash[:otp] = otp.to_s unless Rails.env.production?

    render json: user_hash, status: :ok
  end

  def saw_trend_tutorial
    @user.mark_as_seen_trend_tutorial

    render json: { success: true }, status: :ok
  end

  # POST /login
  def login
    @user = User.find_by_phone(params[:phone].to_i)
    # if the user is not found, return error
    return render json: {
      success: false,
      message: I18n.t('errors.invalid_phone_number') # No user with the phone number
    }, status: :not_found unless @user.present?
    # if user is present & not active, inform him to contact support.
    return render json: { success: false, message: I18n.t('errors.account_suspended') }, status: :bad_request unless @user.can_login?

    is_signup = @user.pre_signup_status?

    # if the user is not signed up & the village is not provided, return error
    if is_signup && (params[:village].blank? || params[:village][:id].blank? || params[:name].blank?)
      return render json: { success: false, message: I18n.t('errors.blank_details') }, status: :unprocessable_entity
    end

    # If payload & signature are present, consider it as a truecaller login
    is_truecaller_flow = params[:payload].present? && params[:signature].present?
    if !is_truecaller_flow && params[:otp].blank?
      return render json: { success: false, message: I18n.t('errors.blank_otp') }, status: :unprocessable_entity
    end

    if is_truecaller_flow
      # validate the truecaller signature
      valid = VerifySignatureUtil.verify(
        'RSA',
        User.get_truecaller_public_key,
        params[:payload],
        params[:signature],
        'sha512'
      )

      # if the signature vlaidation failed, return error
      return render json: {
        success: false,
        message: I18n.t('errors.invalid_truecaller_details') # Truecaller login failed!
      }, status: :unprocessable_entity unless valid
    else
      # validate the OTP
      valid = @user.validate_otp(params[:otp].to_s)

      # if the OTP is invalid, return error
      return render json: {
        success: false,
        message: I18n.t('errors.invalid_otp') # Invalid OTP!
      }, status: :unprocessable_entity unless valid
    end

    if params[:device_token].present?
      device_token = params[:device_token]

      # Disable for all users having this device token
      UserDeviceToken
        .where(device_token: device_token, active: true)
        .update_all(active: false, updated_at: Time.zone.now)

      UserDeviceToken
        .create(
          device_token: device_token,
          user: @user,
          app_version: @app_version,
          app_os: @app_os
        )
    end

    @user.activate if @user.may_activate?

    # if it is signup flow, populate & save the user details
    if is_signup
      village_id = params[:village][:id].to_i

      @user.village_id = village_id
      @user.populate_location
      @user.name = params[:name]
      @user.signed_up = true

      if params[:email].present? && is_truecaller_flow
        # save email on user metadata for truecaller users
        email = params[:email].downcase
        @user.metadatum.build(key: Constants.truecaller_user_email_key, value: email)
      end

      @user.user_circles.build(circle_id: village_id) unless @user.circles.ids.include? village_id
    end

    begin
      @user.save!
    rescue => e
      Honeybadger.notify(e)
      return render json: {
        message: 'Something went wrong'
      }, status: :unprocessable_entity
    end

    user_hash = @user.get_user_response_hash(@app_version, include_unverified_badge: true)

    # Override the party suggestions flag to false, if it is login flow
    user_hash[:show_party_suggestions] = false unless is_signup

    user_hash[:token] = @user.generate_login_token(@app_version, @app_os)

    response.set_header(Constants.jwt_access_token_header, @user.generate_jwt_token)

    @user.update_login_time

    # Reset the show contacts screen value to nil after login
    # Reset the max count seen for contacts screen to nil after login
    @user.del_redis_keys_of_contact_screen_after_login

    render json: user_hash, status: :ok
  end

  def menu_selection
    user_selection = params[:Digits]
    case user_selection
    when '1'
      EventTracker.perform_async(@request_user.id, "charge_ivr_support_requested", { selection: '1' })
      reason = '2399_charge_support'
      source = 'IVR CALL'
      log_to_support_requests_sheet(@request_user, reason, source)
      response = get_success_twiml_response
    else
      EventTracker.perform_async(@request_user.id, "charge_ivr_invalid_support_request", { selection: user_selection })
      response = get_retry_twiml_response
    end
    render xml: response.to_s, status: :ok
  end

  def call_status
    call_status = params[:CallStatus]

    case call_status
    when 'in-progress'
      EventTracker.perform_async(@request_user.id, "charge_ivr_answered", { call_status: call_status })
    when 'completed'
      EventTracker.perform_async(@request_user.id, "charge_ivr_completed", { call_status: call_status, duration: params[:CallDuration] })
    when 'failed', 'no-answer', 'busy'
      EventTracker.perform_async(@request_user.id, "charge_ivr_incomplete", { call_status: call_status })
    end
    render status: :ok
  end

  # PUT /logout
  def logout
    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')
      unless token.nil?
        user_token = UserToken.where(access_token: token).last
        user_token.update(active: false) if !user_token.nil? && user_token.active
      end
    end

    if !params[:device_token].nil? && params[:device_token].present?
      UserDeviceToken.where(device_token: params[:device_token], active: true).delete_all
    end

    # Reset the show contacts screen value & max count seen for contacts screen to nil after logout if the jwt token
    # is present
    if token.present? && token.length > 32
      user = User.get_user_from_jwt_token(token)
      user.del_redis_keys_of_contact_screen_after_login if user.present?
    end

    render json: { success: true, message: 'Logged out!' }, status: :ok
  end

  def get_logged_in_user
    render json: @user.get_user_response_hash(@app_version, include_unverified_badge: true), status: :ok
  end

  def get_posting_circles
    render json: @user.circles.order(level: :asc, name: :asc), status: :ok
  end

  def get_create_post_data
    posting_circles = [Circle.find(0)]
    posting_circles += @user.circles.taggable_circles.order(level: :asc, name: :asc).all if @app_version < Gem::Version.new('1.15.2')

    # story_threads = CircleStoryThread.includes(:circle).where(circle: @user.circles).where('expires_at > ?', Time.now)

    hashtags = []
    hashtags = Hashtag.get_trending_hashtags if @app_version >= Gem::Version.new('1.7.0')
    hashtags_new = hashtags.map do |h|
      {
        id: h['id'],
        name: h['name']
      }
    end

    tag_circles = {
      pre_selected_circles: get_pre_selected_circles,
      tag_header_text: I18n.t('post_data.tag_header_text'),
      screen_header_text: I18n.t('post_data.screen_header_text'),
      info_pop_up_config: {
        enabled: @user.info_pop_up,
        header_text: I18n.t('post_data.info_pop_up_header_text'),
        green_text: I18n.t('post_data.info_pop_up_green_text'),
        red_text: I18n.t('post_data.info_pop_up_red_text'),
        button_text: I18n.t('post_data.info_pop_up_button_text')
      },
      max_circles: Circle::MAX_CIRCLES,
      max_circles_toast_text: I18n.t('post_data.max_circles_toast_text', max_circles: Circle::MAX_CIRCLES),
      circle_levels_count_config: [
        {
          level: Circle.levels.key(1),
          toast_text: I18n.t('post_data.village_toast_text'),
          max_count: Circle::MAX_VILLAGE_CIRCLES
        },
        {
          level: Circle.levels.key(6),
          toast_text: I18n.t('post_data.party_toast_text'),
          max_count: Circle::MAX_PARTY_CIRCLES
        },
        {
          level: Circle.levels.key(7),
          toast_text: I18n.t('post_data.leader_toast_text', max_leader_circles: Circle::MAX_LEADER_CIRCLES),
          max_count: Circle::MAX_LEADER_CIRCLES
        }
      ],
      alert_pop_up_config: {
        enabled: @user.alert_pop_up,
        alert_text: I18n.t('post_data.alert_pop_up_text'),
        button_text: I18n.t('post_data.alert_pop_up_button_text')
      }
    }

    comments_config = {
      comments_info_header_text: I18n.t('post_data.comments_info_header_text'),
      comments_info_body_text: I18n.t('post_data.comments_info_body_text'),
      default_comments_type_identifier: Post.comments_types['all_users'],
      available_comment_options: @user.available_post_comment_options(true)
    }

    post_media_upload_urls = Constants.get_user_post_media_upload_urls

    user_create_post_data =
      {
        hashtags: hashtags_new,
        tag_circles: tag_circles,
        threads: [],
        circles: posting_circles,
        user: @user,
        comments_config: comments_config,
        post_upload_urls: post_media_upload_urls,
        max_photos_count: Photo::MAX_POST_PHOTOS_COUNT,
      }

    # threads: story_threads.as_json(include: :circle)
    render json: user_create_post_data,
           status: :ok
  end

  def get_following_circles_for_tag
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?
    count = 0
    count = params[:count].to_i if params[:count].present?

    user_circles = []
    @user.circles
         .where(active: true, level: Constants.village_levels + [:political_party, :political_leader])
         .order('user_circles.id ASC')
         .offset(offset)
         .limit(count)
         .each do |circle|
      c = {
        id: circle.id,
        name: circle.name,
        photo_url: circle.photo&.compressed_url(size: 200),
        members_count: circle.get_members_count,
        level: circle.is_village_level? ? Circle.levels.key(1) : circle.level,
        circle_tag_config: @user.get_circle_tag_config(circle.id, @app_version)
      }
      user_circles << c
    end

    render json: user_circles, status: :ok
  end

  def get_suggested_circles_for_tag
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 0
    count = params[:count].to_i if params[:count].present?

    suggested_circles = []
    get_suggested_circles = @user.get_suggested_circles_v2(offset, count)
    get_suggested_circles.each do |circle|
      c = {
        id: circle.id,
        name: circle.name,
        photo_url: circle.photo&.compressed_url(size: 200),
        members_count: circle.get_members_count,
        level: circle.is_village_level? ? Circle.levels.key(1) : circle.level,
        circle_tag_config: @user.get_circle_tag_config(circle.id, @app_version)
      }
      suggested_circles << c
    end

    render json: suggested_circles, status: :ok
  end

  def search_circles_for_tag
    value = params[:value].to_s
    search_result = []

    begin
      search_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: {
        _source: %w[id],
        query: {
          bool: {
            should: [
              {
                multi_match: {
                  query: value,
                  type: 'phrase_prefix',
                  fields: %w[name name._2gram name._3gram name_en name_en._2gram name_en._3gram],
                  boost: 2
                }
              },
              {
                multi_match: {
                  query: value,
                  fields: %w[name name_en phone],
                  fuzziness: 'AUTO'
                }
              }
            ],
            minimum_should_match: 1,
            filter: {
              bool: {
                must: [
                  {
                    term: {
                      active: true
                    }
                  },
                  {
                    terms: {
                      level: %w[political_party political_leader village municipality corporation]
                    }
                  }
                ]
              }
            }
          }
        },
        sort: {
          _score: 'desc',
          members_count: 'desc'
        },
        size: 20
      }
      search_es['hits']['hits'].each do |hit|
        search_feed = hit['_source']
        id = search_feed['id']
        circle = Circle.find id
        level = circle.is_village_level? ? Circle.levels.key(1) : circle.level
        c = {
          id: circle.id,
          name: circle.name,
          photo_url: circle.photo&.compressed_url(size: 200),
          members_count: circle.get_members_count,
          level: level,
          circle_tag_config: @user.get_circle_tag_config(circle.id, @app_version)
        }
        if level == Circle.levels.key(1)
          c[:mandal] = circle.parent_circle&.name
          c[:district] = circle.parent_circle&.parent_circle&.name
        end
        search_result << c
      end

      search_result.compact!
    rescue StandardError => e
      render json: { success: false, error: e.to_s }
      return
    end

    render json: search_result, status: :ok
  end

  def info_pop_up_accept
    key_name = "info_pop_up_user_#{@user.id}"
    count = $redis.get(key_name).to_i
    $redis.incr(key_name) if count <= 2
    render json: { success: true }, status: :ok
  end

  def alert_pop_up_accept
    key_name = "alert_pop_up_user_#{@user.id}"
    count = $redis.get(key_name).to_i
    $redis.incr(key_name) if count <= 2
    render json: { success: true }, status: :ok
  end

  def get_viewing_circles
    user_posting_circles = if @app_version < Gem::Version.new('0.3.7')
                             @user.circles.where(circle_type: :location)
                           else
                             @user.circles
                           end

    user_viewing_circles = []

    user_posting_circles.each do |posting_circle|
      loop do
        break if posting_circle.nil?

        posting_circle.members_count = posting_circle.get_members_count
        posting_circle.photo&.compressed_url!(size: 200)

        user_viewing_circles.push(posting_circle)
        posting_circle = posting_circle.parent_circle
      end
    end

    # user_viewing_circles = user_viewing_circles.sort_by{|p| [p.level.to_i, p.name]}.reverse

    render json: user_viewing_circles, status: :ok
  end

  def get_viewing_circles_v2
    location_circles = @user.circles.where(circle_type: :location, level: Constants.village_levels).map do |circle|
      circle.is_user_joined = circle.check_user_joined(@user)
      circle.members_count = circle.get_members_count
      circle.photo&.compressed_url!(size: 200)
      circle
    end

    suggested_circles = Circle.where(circle_type: :interest, active: true)
                              .where.not(id: @user.circles.map { |c| c.id }).limit(10).map do |circle|
      circle.is_user_joined = circle.check_user_joined(@user)
      circle.members_count = circle.get_members_count
      circle.photo&.compressed_url!(size: 200)
      circle
    end

    interest_circles = @user.circles.where(circle_type: :interest).map do |circle|
      circle.is_user_joined = circle.check_user_joined(@user)
      circle.members_count = circle.get_members_count
      circle.photo&.compressed_url!(size: 200)
      circle
    end

    render json: {
      location_circles: location_circles,
      suggested_circles: suggested_circles,
      interest_circles: interest_circles
    }, status: :ok
  end

  def invite_link
    if @app_version >= Gem::Version.new('1.11.3')
      invite_link_v2
    else
      invite_link_v1
    end
  end

  def invite_link_v1
    link = @user.get_dynamic_link
    profile_link = @request_user.get_profile_share_link

    msg = "నేను #{@request_user.name} గారిని  PRAJA APP లో కనుగొన్నాను.\n" \
      "#{profile_link}\n\n" \
      "మీకు కూడా ఈ యాప్ ఖచ్చితంగా నచ్చుతుంది,ఇప్పుడే డౌన్లోడ్ చేయండి.\n" \
      "👇👇👇👇👇\n" \
      "#{link} "

    render json: { success: true, text: msg }, status: :ok
  end

  def invite_link_v2
    profile_link = if @app_version >= Gem::Version.new('1.11.3')
                     link = "https://prajaapp.sng.link/A3x5b/7lmr?paffid=#{@user.id}"
                     if @user.id != @request_user.id
                       link += "&_dl=praja%3A%2F%2Fusers/#{@request_user.id}&_ddl=praja%3A%2F%2Fusers/#{@request_user.id}"
                     end
                     Singular.shorten_link(link)
                   else
                     @request_user.get_profile_share_link
                   end

    if @request_user.id == @user.id
      if @user.internal_journalist?
        district = @user.district

        msg = "నేను *Praja App* వాడుతున్నాను. ఇప్పటికే *#{district.name}* జిల్లాలో *#{district.get_members_count}* సభ్యులు ఉన్నారు " \
          "మరియు *#{district.get_badge_users_count}* మంది గ్రామ మండల, నగర స్థాయి నాయకులు, అధికారులు, జర్నలిస్టులు, " \
          "*Praja App* లో తాజా వార్తా విశేషాలు పంచుకుంటున్నారు.\n\n" \
          'మీరు కూడా మీ *స్థానిక వార్తలు* కోసం మరియు *నాయకులు* తో కనెక్ట్ అయ్యి వీరి సహకారం పొందడానికి వెంటనే ఈ క్రింది లింక్ ను నొక్కి *ఉచితంగా ' \
          "యాప్* లో చేరండి. 😊👇\n" \
          "#{profile_link}"
      else
        msg = "హాయ్, లేటెస్ట్ పొలిటికల్ ట్రెండ్స్ మరియు స్థానిక లీడర్ అప్డేట్స్ కోసం నేను *PRAJA* యాప్ లో చేరాను.\n" \
          "లక్షలాది మంది తెలుగు ప్రజలు వాడుతున్న యాప్, మీరు కూడా ఇప్పుడే ఉచితంగా డౌన్లోడ్ చేసుకోండి.\n" \
          "👇👇👇\n" \
          "#{profile_link}"
      end
    else
      msg = "నేను #{@request_user.name} గారిని *PRAJA* యాప్ లో కనుగొన్నాను.\n" \
        "లక్షలాది మంది తెలుగు ప్రజలు వాడుతున్న Praja App ని మీరు కూడా ఇప్పుడే ఉచితంగా డౌన్లోడ్ చేసుకోండి.\n" \
        "👇👇👇\n" \
        "#{profile_link}"
    end

    render json: { success: true, text: msg, invite_card_data: @request_user.get_badge_notification_data, generate_invite_card: !@user.internal_journalist? || @request_user.id != @user.id },
           status: :ok
  end

  def fb_invite_link
    profile_link = "https://prajaapp.sng.link/A3x5b/7lmr?paffid=#{@user.id}"

    if @request_user.id == @user.id
      if @user.internal_journalist?
        district = @user.district

        msg = "నేను *Praja App* వాడుతున్నాను. ఇప్పటికే *#{district.name}* జిల్లాలో *#{district.get_members_count}* సభ్యులు ఉన్నారు " \
          "మరియు *#{district.get_badge_users_count}* మంది గ్రామ మండల, నగర స్థాయి నాయకులు, అధికారులు, జర్నలిస్టులు, " \
          "*Praja App* లో తాజా వార్తా విశేషాలు పంచుకుంటున్నారు.\n\n" \
          'మీరు కూడా మీ *స్థానిక వార్తలు* కోసం మరియు *నాయకులు* తో కనెక్ట్ అయ్యి వీరి సహకారం పొందడానికి వెంటనే ఈ క్రింది లింక్ ను నొక్కి *ఉచితంగా ' \
          "యాప్* లో చేరండి. 😊👇\n" \
          "#{profile_link}"
      else
        msg = "హాయ్, లేటెస్ట్ పొలిటికల్ ట్రెండ్స్ & లీడర్ అప్డేట్స్ కోసం లక్షలాది తెలుగు ప్రజలు వాడుతున్న Praja App లో నేను చేరాను , \n" \
          "మీరు ఇప్పుడే ఉచితంగా డౌన్లోడ్ చేసుకోండి .\n" \
          "👇👇👇\n" \
          "#{profile_link}"
      end
    else
      profile_link += "&_dl=praja%3A%2F%2Fusers/#{@request_user.id}&_ddl=praja%3A%2F%2Fusers/#{@request_user.id}"

      msg = "నేను #{@request_user.name} గారిని PRAJA యాప్ లో కనుగొన్నాను.\n" \
        "లక్షలాది మంది తెలుగు ప్రజలు వాడుతున్న Praja App ని ఇప్పుడే ఉచితంగా డౌన్లోడ్ చేసుకోండి.\n" \
        "👇👇👇\n" \
        "#{profile_link}"
    end
    render json: { success: true, text: msg }, status: :ok
  end

  def get_viewing_circles_v3
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?
    count = 0
    count = params[:count].to_i if params[:count].present?

    circles = []

    internal_user_clause = ""
    if @user.internal?
      internal_user_clause = " OR (circle_type = 6)"
    end

    @user.circles
         .where("((circle_type = 0 AND level IN (1,13,14)) OR (circle_type = 1) OR (circle_type = 2)#{internal_user_clause}) AND circles.active = 1")
         .order('circles.circle_type ASC, circles.level DESC, user_circles.id ASC')
         .offset(offset)
         .limit(count)
         .map do |circle|
      # TODO: remove this hack
      next if User.core_party_user_ids.include?(@user.id) && circle.is_village_level?

      circle.is_user_joined = true
      circle.new_posts_count = 0 # circle.get_new_posts_count(@user)
      circle.last_posted_at = nil # circle.get_last_posted_at
      circle.photo&.compressed_url(size: 200)
      circles << circle
    end

    render json: circles, status: :ok
  end

  def get_suggested_circles_v3
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 0
    count = params[:count].to_i if params[:count].present?

    render json: @user.get_suggested_circles_v2(offset, count), status: :ok
  end

  def update_profile_pic
    if params[:photo].present?
      photo = Photo.upload(params[:photo], @user.id)
      if !photo.nil?
        @user.photo = photo
        @user.save!
        render json: { success: true, message: 'profile pic updated', user: @user.get_user_response_hash(@app_version, include_unverified_badge: true) },
               status: :ok
      else
        render json: { success: false, message: 'unable to save the photo' }, status: :unprocessable_entity
      end
    else
      render json: { success: false, message: 'photo is required' }, status: :bad_request
    end
  end

  def delete_profile_pic
    if !@user.photo.nil?
      @user.photo = nil
      @user.save!
      render json: { success: true, message: 'deleted profile pic', user: @user.get_user_response_hash(@app_version, include_unverified_badge: true) },
             status: :ok
    else
      render json: { success: false, message: 'Invalid request' }, status: :bad_request
    end
  end

  # PUT users/1/close-toast
  def close_toast
    if params[:toast_id].nil?
      render json: { success: false, message: 'Invalid request' }, status: :bad_request && return
    end

    @user.mark_toast_as_close(params[:toast_id])

    render json: { success: true, message: 'Marked as closed!' }, status: :ok
  end

  def suggested_seen
    @request_user.mark_suggested_as_seen(@user.id)
    render json: { success: true }, status: :ok
  end

  def suggested_list_seen
    $redis.hincrby("suggested_users_list_#{params['list_id']}", @user.id, 1)
    render json: { success: true }, status: :ok
  end

  def get_contacts_for_follow
    ActiveRecord::Base.connected_to(role: :reading) do
      user_ids = @user.not_yet_followed_signed_up_user_ids_of_user_contacts
      users_list = []

      max_limit_for_profile_photo_sort = 0
      user_ids.each do |user_id|
        user = User.get_feed_item(user_id)
        user.loggedInUser = false
        user.follows = false
        user.blocked = false

        # if user has profile photo and max limit for profile photo sort is less than 5
        # then insert the user at that index and increment the max limit
        if user.photo.present? && max_limit_for_profile_photo_sort < 5
          users_list.insert(max_limit_for_profile_photo_sort, user)
          max_limit_for_profile_photo_sort += 1
        else
          users_list << user
        end
      end

      # increment the count of contacts screen shown of user
      @user.set_contact_screen_shown_count_from_redis

      # set the redis key to false and set expiry for 30 days
      if @user.contact_screen_max_seen_limit_reached?
        @user.set_show_contacts_screen_value_to_redis(false, 30.days.to_i)
      end

      if AppVersionSupport.new_contacts_screen_enabled?
        render json: { header_text: I18n.t('follow_contacts.header_text'),
                       sub_header_text: I18n.t('follow_contacts.sub_header_text'),
                       users: users_list }, status: :ok
      else
        render json: users_list, status: :ok
      end
    end
  end

  def get_suggested_users_lists
    suggested_users_lists = @user.get_suggested_users_lists
    render json: { suggested_users_lists: suggested_users_lists }, status: :ok
  end

  def follow_all_suggested_users
    feed_item_id = params[:feed_item_id]

    if feed_item_id.present?
      source_of_follow = "suggested_list"

      user_ids_list =
        case feed_item_id
        when User::SUGGESTED_LIST_1
          @user.get_user_ids_of_interest_list_2(nil)
        when User::SUGGESTED_LIST_3
          @user.get_user_ids_of_interest_list_5(nil) + @user.get_user_ids_of_interest_list_6(nil) + @user.get_user_ids_of_interest_list_7(nil)
        when User::SUGGESTED_LIST_5
          @user.get_user_ids_of_interest_list_6(nil)
        when User::SUGGESTED_LIST_6
          @user.get_user_ids_of_interest_list_7(nil)
        when User::SUGGESTED_LIST_8
          @user.get_user_ids_of_interest_list_4(nil)
        when User::SUGGESTED_LIST_2
          @user.get_user_ids_of_location_list_1(nil) + @user.get_user_ids_of_location_list_2(nil) + @user.get_user_ids_of_location_list_3(nil) + @user.get_user_ids_of_location_list_4(nil)
        when User::SUGGESTED_LIST_4
          @user.get_user_ids_of_location_list_5(nil) + @user.get_user_ids_of_location_list_6(nil)
        when User::SUGGESTED_LIST_7
          @user.get_user_ids_of_location_list_5(nil) + @user.get_user_ids_of_location_list_7(nil)
        when User::SUGGESTED_LIST_9
          @user.get_user_ids_of_location_list_3(nil) + @user.get_user_ids_of_location_list_4(nil)
        when "CONTACTS_LIST"
          source_of_follow = "follow_contacts"
          @user.not_yet_followed_signed_up_user_ids_of_user_contacts
        else
          []
        end

      if user_ids_list.present?
        user_ids_list.in_groups_of(100, false).each do |user_ids|
          BulkFollowUsersWorker.perform_async(@user.id, user_ids, @app_version.to_s, @app_build_number, source_of_follow)
        end

        render json: { success: true, message: I18n.t('follow_all_suggested_users.success') }, status: :ok
      else
        render json: { success: false, message: I18n.t('follow_all_suggested_users.failed') }, status: :unprocessable_entity
      end
    else
      render json: { success: false, message: I18n.t('follow_all_suggested_users.failed') }, status: :unprocessable_entity
    end
  end

  # send user dm circles to client
  def get_user_dm_circles
    # TODO: keeping it for the sake of the internal released versions
    circle_ids = []
    circle_ids = params[:circle_ids] if params[:circle_ids].present?

    user_dm_circles_list = @user.get_user_dm_circles(circle_ids)

    user_dm_circles_hash = {
      circles: user_dm_circles_list
    }
    render json: user_dm_circles_hash, status: :ok
  end

  # circles batch fetch
  # TODO: Need to decide on moving to circles controller
  def get_circles
    circle_ids = []
    circle_ids = params[:circle_ids] if params[:circle_ids].present?

    circles_list = @user.get_circles(circle_ids)

    circles_hash = {
      circles: circles_list
    }
    render json: circles_hash, status: :ok
  end

  def get_verification_data
    render json: {
      designations: Constants.designations,
      choose_designation_text: 'క్రింది వాటిలో మిమ్మల్ని మీరు దేనిగా గుర్తిస్తారు',
    }, status: :ok
  end

  def save_verification_designation
    valid_designations = Constants.designations.map { |designation| designation[:name] }
    designation_name = params[:designation_name]
    if designation_name.blank? || !valid_designations.include?(designation_name)
      render json: { success: false, message: I18n.t('designation_verification.valid_designation_required') }, status: :bad_request
      return
    end

    # Currently we are not saving this data, we might save it after the trial run for get-verified, after community team
    # has identified the requirements for verification requests dashboard UI

    if designation_name == 'other'
      render json: {
        status: I18n.t('designation_verification.coming_soon.status'),
        title: I18n.t('designation_verification.coming_soon.title'),
        message: I18n.t('designation_verification.coming_soon.message'),
      }, status: :ok
      return
    end

    render json: {
      status: I18n.t('designation_verification.initiated.status'),
      title: I18n.t('designation_verification.initiated.title'),
      message: I18n.t('designation_verification.initiated.message'),
    }, status: :ok
  end

  def save_mla_constituency
    options = params[:selected_options]

    if options.blank? || options.count > 1
      render json: { success: false, message: I18n.t('save_mla_constituency.invalid_options') }, status: :bad_request
      return
    end

    mla_constituency = Circle.find_by(id: options.first.to_i)
    if mla_constituency.blank?
      render json: { success: false, message: I18n.t('save_mla_constituency.invalid_selection') }, status: :bad_request
      return
    end

    @user.mla_constituency_id = mla_constituency.id
    @user.mp_constituency_id = mla_constituency.parent_circle_id

    @user.save!
    render json: { success: true, message: I18n.t('save_mla_constituency.update_success') }, status: :ok
  end

  def save_premium_lead
    premium_pitch = @user.find_or_initialize_premium_pitch(source: :PREMIUM_PITCH_INTERESTED)

    if premium_pitch.may_shown_interest?
      premium_pitch.shown_interest!
    end

    render json: { success: true, message: I18n.t('save_premium_lead.success') }, status: :ok
  end

  def wait_list
    user_pp = @user.find_or_initialize_premium_pitch(source: :WAITLIST)

    if user_pp.wait_list? || user_pp.interested?
      Honeybadger.notify("User already in waitlist", context: { user_id: @user.id })
    else
      if user_pp.new_record?
        if @user.verify_eligibility_rules_for_premium_pitch? && @user.leader_profession?
          user_pp.shown_interest!
        else
          user_pp.mark_wait_list!
        end
      elsif user_pp.to_be_pitched?
        user_pp.shown_interest!
      end
    end

    waiting_list_number = @user.waiting_list_number

    if waiting_list_number.present?
      render json: { success: true,
                     type: 'waitlist',
                     disabled: true,
                     text: I18n.t('wait_list.request_sent', waiting_list_number: waiting_list_number),
                     analytics_params: { waiting_list_number: waiting_list_number } }, status: :ok
    else
      render json: { success: true, message: I18n.t('wait_list.team_contact') }, status: :ok
    end
  end

  def premium_experience
    user_role = @user.get_badge_role
    button_details = premium_experience_button_json(user: @user)

    feed_item_deep_link = nil
    if button_details[:type] == "subscribe"
      feed_item_deep_link = @user.get_paywall_deeplink
    elsif button_details[:type] == "buy"
      feed_item_deep_link = "/payment-sheet"
    end

    feed_items = if @user.is_poster_subscribed || SubscriptionUtils.has_user_ever_subscribed?(@user.id)
                   [
                     @user.subscription_info_toast(user: @user),
                     @user.premium_members_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.usage_counts_feed_item(user: @user),
                     @user.profile_views_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.upcoming_events_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.my_poster_styles_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.relation_manager_feed_item(user: @user),
                     @user.premium_experience_media_carousel(user: @user, user_role: user_role,
                                                             deeplink: feed_item_deep_link),
                     @user.faqs_feed_item(user: @user),
                   ].compact
                 elsif @user.is_eligible_for_start_trial? && @user.has_user_frames?
                   [
                     @user.subscription_info_toast(user: @user),
                     @user.premium_experience_carousel_for_trial_eligible_user(user: @user, user_role: user_role,
                                                                               deeplink: feed_item_deep_link),
                     @user.premium_members_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.profile_views_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.upcoming_events_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.my_poster_styles_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.relation_manager_feed_item(user: @user),
                     @user.premium_experience_media_carousel(user: @user, user_role: user_role, deeplink: feed_item_deep_link),
                     @user.faqs_feed_item(user: @user)
                   ].compact
                 else
                   [
                     @user.subscription_info_toast(user: @user),
                     @user.premium_experience_media_carousel(user: @user, user_role: user_role,
                                                             deeplink: feed_item_deep_link, ignore_title: true),
                     @user.premium_members_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.profile_views_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.upcoming_events_feed_item(user: @user, deeplink: feed_item_deep_link),
                     @user.faqs_feed_item(user: @user)
                   ].compact
                 end
    analytics_params = {
      is_self_trial_user: @user.eligible_for_self_trial?,
    }
    # delete yet to start trial key after user has seen the premium experience
    @user.delete_yet_to_start_trial_key

    render json: { feed_items: feed_items, button_details: button_details,
                   analytics_params: analytics_params }, status: :ok
  end

  def premium_experience_button_json(user:)
    recharge_json = {
      type: 'deeplink',
      disabled: false,
      deeplink: '/payment-sheet?source=premium_experience',
      text: 'రీచార్జ్ చేయండి',
      analytics_params: {
        type: 'deeplink',
        deeplink: '/payment-sheet?source=premium_experience',
        disabled: false
      }
    }
    is_user_in_grace_period = SubscriptionUtils.is_user_in_grace_period?(user.id)
    if is_user_in_grace_period
      return recharge_json
    end
    if user.is_poster_subscribed
      subscription_id = user.active_subscription&.id
      premium_end_date = SubscriptionUtils.active_poster_premium_end_date(user.id)
      last_charge_failed = SubscriptionCharge.last_recharge_failed?(subscription_id)
      if premium_end_date.present? and premium_end_date <= Constants.default_premium_duration_days
        return {
          type: 'buy',
          disabled: false,
          deeplink: '/payment-sheet?source=premium_experience',
          text: 'ఆటో రీఛార్జ్ సెట్ చేయండి',
          analytics_params: {
            type: 'buy',
            deeplink: '/payment-sheet?source=premium_experience',
            disabled: false
          }
        } if subscription_id.blank?
        return recharge_json if last_charge_failed
      end

      if @user.app_version_supports_upgrade_package_sheet?
        campaign = @user.user_eligible_1_year_campaign

        deeplink = if campaign.present? && @user.upgrade_package_using_offer_conditions_met?
                     "/upgrade?source=premium_experience&campaign_id=#{campaign.id}&offer=true"
                   elsif @user.common_upgrade_package_conditions_met?
                     "/upgrade?source=premium_experience"
                   end

        unless deeplink.blank?
          user_plan = UserPlan.find_by(user_id: @user.id)
          return nil if user_plan.blank?
          target_plan_duration_in_months = 12
          target_plan = Plan.get_plan_based_on_duration(user: @user, duration_in_months: target_plan_duration_in_months)
          return nil if target_plan.blank?
          target_plan_amount = @user.get_plan_amount_based_on_duration(plan: target_plan,
                                                                       duration: target_plan.duration_in_months)
          monthly_savings = user_plan.amount - (target_plan_amount / target_plan_duration_in_months)

          return {
            type: 'deeplink',
            disabled: false,
            deeplink: deeplink,
            text: I18n.t('upgrade_package_nudge.sub_text', monthly_savings: monthly_savings),
            analytics_params: {
              type: 'deeplink',
              deeplink: deeplink,
              disabled: false
            }
          }
        end
      end

      return {
        type: "deeplink",
        disabled: false,
        deeplink: "/posters",
        text: "ప్రీమియం పోస్టర్‌లను చూడండి",
        analytics_params: {
          type: 'deeplink',
          deeplink: '/posters',
          disabled: false
        }
      }
    end

    if user.get_user_poster_layout.present? && SubscriptionUtils.has_user_ever_subscribed?(user.id)
      subscription_id = user.active_subscription&.id

      return {
        type: 'buy',
        disabled: false,
        deeplink: '/payment-sheet?source=premium_experience',
        text: 'ఆటో రీఛార్జ్ సెట్ చేయండి',
        analytics_params: {
          type: 'buy',
          deeplink: '/payment-sheet?source=premium_experience',
          disabled: false
        }
      } if subscription_id.blank?

      premium_end_date = SubscriptionUtils.last_poster_premium_end_date(user.id)

      return {
        type: 'deeplink',
        disabled: false,
        deeplink: '/posters',
        text: 'ప్రీమియం పోస్టర్‌లను చూడండి',
        analytics_params: {
          type: 'deeplink',
          deeplink: '/posters',
          disabled: false
        }
      } if premium_end_date.present? && premium_end_date.advance(days: Constants.duration_days_after_premium_end_date).end_of_day >= Time.zone.now

      return {
        type: 'buy',
        disabled: false,
        deeplink: '/payment-sheet?source=premium_experience',
        text: 'ఆటో రీఛార్జ్ సెట్ చేయండి',
        analytics_params: {
          type: 'buy',
          deeplink: '/payment-sheet?source=premium_experience',
          disabled: false
        }
      }
    end

    if (user.is_eligible_for_start_trial? && user.has_premium_layout?) || user.eligible_for_self_trial?
      duration = Metadatum.get_user_trail_duration(user_id: user.id)
      duration = duration.positive? ? duration : Constants.poster_trial_default_duration
      text = "#{duration}- రోజులు ఉచిత ట్రయల్ ప్రారంభించండి"
      deeplink = @user.get_paywall_deeplink(source: 'premium_experience')
      return {
        type: "subscribe",
        disabled: false,
        deeplink: deeplink,
        text: text,
        analytics_params: {
          type: 'subscribe',
          disabled: false,
          deeplink: deeplink
        }
      }
    end

    if user.is_in_waitlist?
      waiting_list_number = user.waiting_list_number
      return {
        type: "waitlist",
        disabled: true,
        text: "రిక్వెస్ట్ పంపబడినది, మీ వెయిట్‌లిస్ట్ నంబర్ #{waiting_list_number}",
        analytics_params: {
          type: 'waitlist',
          disabled: true,
          waiting_list_number: waiting_list_number
        }
      }
    end

    {
      type: "waitlist",
      disabled: false,
      text: "ట్రయల్ కోసం వెయిట్‌లిస్ట్‌లో చేరండి",
      analytics_params: {
        type: 'waitlist',
        disabled: false
      }
    }
  end

  def premium_success
    unless @user.is_poster_subscribed
      return render json: { success: false, message: I18n.t('premium_success.no_subscription') }, status: :bad_request
    end

    poster_premium_end_date = SubscriptionUtils.active_poster_premium_end_date(@user.id)

    if poster_premium_end_date.blank?
      return render json: { success: false, message: I18n.t('premium_success.no_subscription') }, status: :bad_request
    end

    user_plan = @user.get_active_user_plan
    amount = user_plan.amount
    if amount.blank?
      Honeybadger.notify(I18n.t('premium_success.no_amount_error'), context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('premium_success.no_subscription') }, status: :bad_request
    end

    subscription = @user.active_subscription
    if subscription.blank?
      # fetch user last cancelled subscription
      subscription = Subscription.where(user_id: @user.id, status: :cancelled).last
    end
    last_subscription_charge = SubscriptionCharge.where(subscription_id: subscription.id).last
    if last_subscription_charge.blank?
      Honeybadger.notify("last subscription is not found", context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('premium_success.premium_failure') }, status: :bad_request
    elsif last_subscription_charge.is_trial_charge?
      if @user.has_premium_layout?
        amount_text = I18n.t('premium_success.trial_started')
        label = nil
        premium_details_text = I18n.t('premium_success.valid_until', date: poster_premium_end_date.strftime('%d/%m/%Y'))
      else
        amount_text = I18n.t('premium_success.self_trial_booking_text')
        label = nil
        premium_details_text = I18n.t('premium_success.self_trial_success_label')
      end
    else
      amount_text = I18n.t('premium_success.paid_amount', amount: last_subscription_charge.amount)
      label = I18n.t('premium_success.membership_expiry')
      premium_details_text = I18n.t('premium_success.end_date_text',
                                    date: poster_premium_end_date.strftime('%d/%m/%Y'))
    end
    if SubscriptionUtils.subscribed_poster_package_multiple_times?(user_id: @user.id)
      current_plan_duration_in_months = user_plan.plan.duration_in_months
      if current_plan_duration_in_months == 1
        title = I18n.t('premium_success.welcome_back.one', duration: current_plan_duration_in_months)
      else
        title = I18n.t('premium_success.welcome_back.other', duration: current_plan_duration_in_months)
      end
      last_but_not_previous_log = UserPlanLog.where(user_id: @user.id).order(end_date: :desc).offset(1).limit(1)
      previous_plan = last_but_not_previous_log.first&.plan
      if previous_plan.present? && previous_plan.duration_in_months < current_plan_duration_in_months
        total_savings = (previous_plan.amount * current_plan_duration_in_months) - last_subscription_charge.amount
        success_message = I18n.t('premium_success.success_message', amount: total_savings)
      end
    else
      title = I18n.t('premium_success.welcome')
    end

    # change amount text if subscription is cancelled
    if subscription.cancelled?
      amount_text = I18n.t('premium_success.no_active_subscription')
    end

    render json: {
      user: @user.get_user_response_hash(@app_version, include_unverified_badge: true),
      title: title,
      success_message: success_message,
      premium_details: {
        amount_text: amount_text,
        type: "info",
        label: label,
        text: premium_details_text,
      },
      button_details: {
        text: I18n.t('premium_success.continue'),
        auto_perform_action: true,
        type: 'deeplink',
        deeplink: '/feeds/my_feed',
        auto_perform_action_time: 10,
      },
      analytics_params: {
        paid_amount: last_subscription_charge.amount,
        end_date: poster_premium_end_date,
      }
    }, status: :ok
  end

  def get_refund_info(show_annual, amount)
    if show_annual
      I18n.t('premium_success.v2.annual_refund_info', amount: amount)
    else
      I18n.t('premium_success.v2.monthly_refund_info', amount: amount)
    end
  end

  def premium_success_v2
    unless @user.is_poster_subscribed
      return render json: { success: false, message: I18n.t('premium_success.no_subscription') }, status: :bad_request
    end

    user_plan = UserPlan.where(user_id: @user.id).last
    amount = user_plan.amount
    if amount.blank?
      Honeybadger.notify(I18n.t('premium_success.no_amount_error'), context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('premium_success.no_subscription') }, status: :bad_request
    end

    subscription = @user.active_subscription || @user.on_hold_subscription
    if subscription.blank?
      # fetch user last cancelled subscriptions
      subscription = Subscription.where(user_id: @user.id, status: :cancelled).last
    end

    last_subscription_charge = SubscriptionCharge.where(subscription_id: subscription.id).last
    if last_subscription_charge.blank?
      Honeybadger.notify("last subscription is not found", context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('premium_success.premium_failure') }, status: :bad_request
    end

    # Get premium success data based on user state
    formatted_end_date = user_plan.end_date.strftime('%d/%m/%Y')

    # Determine user subscription state
    is_user_in_extension = @user.in_subscription_extension?
    downgraded_date = subscription.downgraded_date
    is_user_subscription_downgraded = downgraded_date.present? ? Time.zone.parse(downgraded_date) < user_plan.end_date : false
    user_plan_extension = UserPlanExtension.where(user_id: @user.id).last

    # Default values
    title = I18n.t('premium_success.v2.title') # "ప్రజా ప్రీమియంకు స్వాగతం!" for all users
    success_message = nil
    amount_text = nil
    savings_text = nil
    premium_details_text = nil
    type = 'info'
    button_text = I18n.t('premium_success.v2.view_premium_posters') # "మీ ప్రత్యేక పోస్టర్లు చూడండి"
    button_deeplink = '/posters?source=premium_success'
    button_auto_perform_action = true
    button_auto_perform_action_time = 10

    refund_info = nil

    # Handle different user states
    if is_user_subscription_downgraded && is_user_in_extension
      # Annual downgrade user with extension
      if downgraded_date.present? && Time.zone.parse(downgraded_date) < user_plan_extension.created_at
        # Extension happened after downgrade - Annual extension user
        title = I18n.t('premium_success.v2.title')
        success_message = I18n.t('premium_success.v2.extension_success_message')

        if last_subscription_charge.sent_to_pg?
          refund_amount = last_subscription_charge.amount
          amount_text = get_refund_info(last_subscription_charge.charge_date.beginning_of_day < user_plan.end_date.beginning_of_day, refund_amount)
        end
        premium_details_text = I18n.t('premium_success.v2.extension_membership_text', date: formatted_end_date)

        type = 'quiet'
        button_text = I18n.t('premium_success.v2.view_premium_posters_with_extension')
      else
        # Downgrade happened after extension - Annual downgrade user
        title = I18n.t('premium_success.v2.title')
        success_message = I18n.t('premium_success.v2.annual_downgrade_success_message')

        if last_subscription_charge.sent_to_pg?
          refund_amount = last_subscription_charge.amount - user_plan.amount
          amount_text = get_refund_info(true, refund_amount)
        end
        premium_details_text = I18n.t('premium_success.v2.annual_downgrade_membership_text', amount: 299, date: formatted_end_date)

        type = 'quiet'
        button_text = I18n.t('premium_success.v2.view_premium_posters')
      end
    elsif is_user_subscription_downgraded
      # Annual downgrade user (without extension)
      title = I18n.t('premium_success.v2.title')
      success_message = I18n.t('premium_success.v2.annual_downgrade_success_message')

      if last_subscription_charge.sent_to_pg?
        refund_amount = last_subscription_charge.amount - user_plan.amount
        amount_text = get_refund_info(true, refund_amount)
      end
      premium_details_text = I18n.t('premium_success.v2.annual_downgrade_membership_text', amount: 299, date: formatted_end_date)

      type = 'quiet'
      button_text = I18n.t('premium_success.v2.view_premium_posters')
    elsif is_user_in_extension
      # User in extension period (annual or monthly)
      title = I18n.t('premium_success.v2.title')
      success_message = I18n.t('premium_success.v2.extension_success_message')

      if last_subscription_charge.sent_to_pg?
        amount_text = get_refund_info(user_plan.plan.annual?, last_subscription_charge.amount)
      end
      premium_details_text = I18n.t('premium_success.v2.extension_membership_text', date: formatted_end_date)

      type = 'quiet'
      button_text = I18n.t('premium_success.v2.view_premium_posters_with_extension')
    elsif last_subscription_charge.is_trial_charge?
      # Trial user (sales led or self trial)
      if @user.has_premium_layout?
        # Sales led trial user
        success_message = I18n.t('premium_success.v2.sales_led_trial_success_message')
        premium_details_text = I18n.t('premium_success.v2.sales_led_trial_membership_text', date: formatted_end_date)
        button_text = I18n.t('premium_success.v2.view_premium_posters')
      else
        # Self trial user
        success_message = I18n.t('premium_success.v2.self_trial_success_message')
        premium_details_text = I18n.t('premium_success.v2.self_trial_membership_text')
        button_text = I18n.t('premium_success.v2.continue')
        button_auto_perform_action = false
      end

    elsif SubscriptionUtils.subscribed_poster_package_multiple_times?(user_id: @user.id)
      # Recharge or upgrade user
      current_plan_duration_in_months = user_plan.plan.duration_in_months

      if current_plan_duration_in_months == 1
        # Monthly recharge user
        success_message = I18n.t('premium_success.v2.monthly_recharge_success_message')
        amount_text = I18n.t('premium_success.v2.amount_text', amount: last_subscription_charge.amount)
        premium_details_text = I18n.t('premium_success.v2.membership_text', date: formatted_end_date)
      else
        # Annual recharge or upgrade user
        success_message = I18n.t('premium_success.v2.annual_recharge_success_message')
        amount_text = I18n.t('premium_success.v2.amount_text', amount: last_subscription_charge.amount)
        premium_details_text = I18n.t('premium_success.v2.membership_text', date: formatted_end_date)

        # Check if user upgraded from a lower plan
        last_but_not_previous_log = UserPlanLog.where(user_id: @user.id).order(end_date: :desc).offset(1).limit(1)
        previous_plan = last_but_not_previous_log.first&.plan

        if previous_plan.present? && previous_plan.duration_in_months < current_plan_duration_in_months
          # Annual upgrade user - calculate savings
          total_savings = (previous_plan.amount * current_plan_duration_in_months) - user_plan.plan.amount
          savings_text = I18n.t('premium_success.v2.savings_text', amount: total_savings)
        end
      end
    else
      # First time paid user
      current_plan_duration_in_months = user_plan.plan.duration_in_months

      if current_plan_duration_in_months == 1
        # Monthly recharge user
        success_message = I18n.t('premium_success.v2.monthly_recharge_success_message')
      else
        # Annual recharge user
        success_message = I18n.t('premium_success.v2.annual_recharge_success_message')

        # Calculate savings for annual plan
        user_monthly_plan = Plan.get_plan_based_on_duration(user: @user, duration_in_months: 1)
        if user_monthly_plan.present?
          total_savings = (user_monthly_plan.amount * 12) - user_plan.amount
          savings_text = I18n.t('premium_success.v2.savings_text', amount: total_savings)
        end
      end

      amount_text = I18n.t('premium_success.v2.amount_text', amount: last_subscription_charge.amount)
      premium_details_text = I18n.t('premium_success.v2.membership_text', date: formatted_end_date)
    end

    if user_plan.plan.annual? and !is_user_in_extension and !is_user_subscription_downgraded
      user_monthly_plan = Plan.get_plan_based_on_duration(user: @user, duration_in_months: 1)
      if user_monthly_plan.present?
        total_savings = (user_monthly_plan.amount * 12) - user_plan.amount
        savings_text = I18n.t('premium_success.v2.savings_text', amount: total_savings)
      end
    end

    button_deeplink = @user.has_premium_layout? ? button_deeplink : '/feeds/my_feed?source=premium_success'
    # Build the response
    response_data = {
      user: @user.get_user_response_hash(@app_version, include_unverified_badge: true),
      title: title,
      success_message: success_message,
      savings_message: savings_text,
      amount_text: amount_text,
      membership_text: premium_details_text,
      button_details: {
        text: button_text,
        auto_perform_action: button_auto_perform_action,
        type: 'deeplink',
        deeplink: button_deeplink,
        auto_perform_action_time: button_auto_perform_action_time,
      },
      analytics_params: {
        paid_amount: last_subscription_charge.amount,
        end_date: user_plan.end_date,
        is_user_in_extension: is_user_in_extension,
        is_user_subscription_downgraded: is_user_subscription_downgraded,
      }
    }

    render json: response_data, status: :ok
  end

  def recharge_paywall

    if SubscriptionUtils.has_user_ever_subscribed?(@user.id)
      return render json: { success: false, message: I18n.t('errors.subscriptions.already_used_trail') }, status: :conflict
    end

    if @user.active_subscription.present?
      return render json: { success: false, message: I18n.t('errors.subscriptions.already_active') }, status: :conflict
    end

    plan = @user.default_plan
    rm_user = @user.rm_user_json
    pay_wall = build_pay_wall(plan)

    help = build_help(rm_user)

    auto_pay_cancel_text = I18n.t('recharge_paywall.auto_pay_cancel_text')
    existing_premium_users = build_existing_premium_users
    button_details = { text: I18n.t('recharge_paywall.button_text') }
    terms = @user.terms_json_v1

    @user.delete_yet_to_start_trial_key

    render json: {
      title: I18n.t('recharge_paywall.title'),
      plan_id: plan.id,
      rm_user: rm_user,
      pay_wall: pay_wall,
      help: help,
      auto_pay_cancel_text: auto_pay_cancel_text,
      existing_premium_users: existing_premium_users,
      button_details: button_details,
      terms: terms,
      use_juspay_sdk: AppVersionSupport.juspay_sdk_enabled?,
      payment_gateway: 'intent',
      analytics_params: build_analytics_params(plan, rm_user),
    }, status: :ok
  end

  def recharge_paywall_v2
    if SubscriptionUtils.has_user_ever_subscribed?(@user.id)
      return render json: { success: false, message: I18n.t('errors.subscriptions.already_used_trail') }, status: :conflict
    end

    plan = @user.default_plan
    rm_user = @user.rm_user_json
    pay_wall = build_pay_wall(plan)

    help = build_help(rm_user) if @user.has_premium_layout?

    auto_pay_cancel_text = I18n.t('recharge_paywall.auto_pay_cancel_text')
    existing_premium_users = build_existing_premium_users
    button_details = { text: I18n.t('recharge_paywall.button_text') }
    terms = @user.terms_json_v1

    @user.delete_yet_to_start_trial_key
    render json: {
      title: I18n.t('recharge_paywall.title'),
      plan_id: plan.id,
      rm_user: rm_user,
      pay_wall: pay_wall,
      help: help,
      auto_pay_cancel_text: auto_pay_cancel_text,
      existing_premium_users: existing_premium_users,
      button_details: button_details,
      terms: terms,
      use_juspay_sdk: AppVersionSupport.juspay_sdk_enabled?,
      payment_gateway: 'intent',
      analytics_params: build_analytics_params(plan, rm_user),
    }, status: :ok
  end

  def annual_recharge_paywall

    if SubscriptionUtils.has_user_ever_subscribed?(@user.id)
      return render json: { success: false, message: I18n.t('errors.subscriptions.already_used_trail') }, status: :conflict
    end

    plans_objects = Plan.get_premium_plans(user: @user)
    plans = premium_plans_json_for_annual_recharge_paywall(user: @user, plans: plans_objects)

    pay_wall = build_annual_pay_wall

    auto_pay_cancel_text = I18n.t('annual_recharge_paywall.auto_pay_cancel_text')
    existing_premium_users = build_existing_premium_users
    button_details = { text: I18n.t('annual_recharge_paywall.button_text') }
    terms = @user.terms_json_v1

    @user.delete_yet_to_start_trial_key

    rm_user = @user.relation_manager_feed_item(user: @user)&.dig(:rm_user)

    render json: {
      title: I18n.t('annual_recharge_paywall.title'),
      sub_title: I18n.t('annual_recharge_paywall.sub_title'),
      plans: plans,
      pay_wall: pay_wall,
      auto_pay_cancel_text: auto_pay_cancel_text,
      existing_premium_users: existing_premium_users,
      button_details: button_details,
      terms: terms,
      use_juspay_sdk: AppVersionSupport.juspay_sdk_enabled?,
      payment_gateway: 'intent',
      analytics_params: {
        is_self_trial_user: @user.eligible_for_self_trial?,
        plans_count: plans.count,
        plans_ids: plans.map { |plan| plan[:id] }.join(','),
        default_plan_duration: plans.map { |plan| plan[:selected] ? plan[:duration] : nil }.compact.first,
        default_plan_id: plans.map { |plan| plan[:selected] ? plan[:id] : nil }.compact.first,
        default_plan_amount: plans.map { |plan| plan[:selected] ? plan[:amount] : nil }.compact.first,
        plans_shown: plans.map { |plan| plan.slice(:id, :duration, :amount, :per_month_amount, :discount_percentage) },
        rm_user_id: rm_user&.dig(:id),
        yearly_pitch_user: @user.should_pitch_yearly_package?,
      }.compact,
    }, status: :ok
  end

  def downgrade_bottom_sheet
    present_user_plan = UserPlan.where(user_id: @user.id).last
    current_plan = present_user_plan.plan
    switching_plan = Plan.get_plan_based_on_duration(user: @user, duration_in_months: 1)

    if current_plan.duration_in_months == switching_plan.duration_in_months
      render json: { message: I18n.t('downgrade_bottom_sheet.not_eligible_text')
      }, status: :bad_request
      return
    end

    plans = premium_plans_json_for_downgrade(user: @user, plans: [current_plan, switching_plan])
    previous_plan_json = plans.find { |plan| plan[:id] == current_plan.id }
    switching_plan_json = plans.find { |plan| plan[:id] == switching_plan.id }

    plan_switch_details = {
      previous_plan: previous_plan_json,
      switching_plan: switching_plan_json,
    }

    on_hold_subscription = @user.on_hold_subscription
    render json: {
      title: I18n.t('downgrade_bottom_sheet.title'),
      name_text: I18n.t('downgrade_bottom_sheet.name_text', name: @user.name),
      notify_text: I18n.t('downgrade_bottom_sheet.notify_text', duration: current_plan.duration_in_months, count: current_plan.duration_in_months,
                          amount: switching_plan.amount, downgrade_date: on_hold_subscription.get_down_gradable_date.strftime('%Y-%m-%d')),
      plan_switch_details: plan_switch_details,
      button_text: I18n.t('downgrade_bottom_sheet.button_text'),
      analytics_params: {
        previous_plan_id: current_plan.id,
        previous_plan_amount: current_plan.amount,
        previous_plan_duration_in_months: current_plan.duration_in_months,
        switching_plan_id: switching_plan.id,
        switching_plan_amount: switching_plan.amount,
        switching_plan_duration_in_months: switching_plan.duration_in_months,
        last_failed_attempt_number: on_hold_subscription.last_failed_retry_attempt_number,
        days_left_in_grace_period: on_hold_subscription.get_days_left_in_grace_period,
        subscription_status: on_hold_subscription.status,
      }
    }, status: :ok

  end

  def downgrade_consent

    unless @user.show_downgrade_consent_sheet?
      render json: { success: false, message: I18n.t('downgrade_consent.already_got_consent') }, status: :bad_request
      return
    end
    user_subscription = @user.active_subscription
    if user_subscription.blank?
      user_subscription = @user.on_hold_subscription
    end
    if user_subscription.blank?
      render json: { success: false, message: I18n.t('downgrade_consent.no_active_subscription_error') }, status: :bad_request
      return
    end

    consent_approved = params[:consent_approved]

    if consent_approved.nil?
      render json: { success: false, message: I18n.t('downgrade_consent.consent_param_missing_required') }, status: :bad_request
      return
    end

    if consent_approved
      user_subscription.mark_subscription_plan_downgrade_consent_as_yes
    else
      user_subscription.mark_subscription_plan_downgrade_consent_as_no
    end

    render json: { success: true }, status: :ok
  end

  def cancel_flow_downgrade_to_monthly_details
    # Get the current user's plan
    present_user_plan = UserPlan.where(user_id: @user.id).last if @user.is_poster_subscribed
    if present_user_plan.blank?
      return render json: { success: false, message: I18n.t('cancellation_flow_v2.downgrade.no_active_subscription') },
                    status: :bad_request
    end

    current_plan = present_user_plan.plan
    target_plan = Plan.get_plan_based_on_duration(user: @user, duration_in_months: 1)

    if current_plan.duration_in_months == target_plan.duration_in_months
      return render json: { success: false, message: I18n.t('cancellation_flow_v2.downgrade.already_on_monthly_plan') },
                    status: :conflict
      return
    end

    cancel_button_deeplink = '/cancel-membership?source=cancel_flow_downgrade_sheet'
    if current_plan.annual? && @user.cancellable_latest_subscription&.subscription_charges&.success.blank?
      cancel_button_deeplink = "/premium-benefits-loss-screen?source=cancel_flow_downgrade_sheet"
    end
    is_user_atleast_paid_once = SubscriptionCharge.where(user_id: @user.id, status: :success).where("amount > 1").exists?
    # Format the response according to the specified structure
    render json: {
      title: I18n.t('cancellation_flow_v2.downgrade.title'),
      sub_title: I18n.t('cancellation_flow_v2.downgrade.sub_title'),
      current_plan: {
        plan_id: current_plan.id,
        title: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.current_plan.title'),
        amount: "₹#{current_plan.amount}",
        duration_text: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.current_plan.duration_text'),
        per_month_text: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.current_plan.per_month_text', amount: target_plan.amount / target_plan.duration_in_months),
      },
      target_plan: {
        plan_id: target_plan.id,
        title: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.target_plan.title'),
        amount: "₹#{target_plan.amount}",
        duration_text: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.target_plan.duration_text'),
      },
      downgrade_plan_button: {
        text: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan_button_text'),
        type: 'api',
        api_url: Constants.get_api_host + '/downgrade-plan?target_plan_id=' + target_plan.id.to_s
      },
      cancel_membership_button: {
        text: I18n.t('cancellation_flow_v2.downgrade.cancel_membership_button_text'),
        type: 'deeplink',
        deeplink: cancel_button_deeplink
      },
      analytics_params: {
        subscription_status: @user.get_subscription_status,
        is_user_atleast_paid_once: is_user_atleast_paid_once,
        is_in_extension: @user.in_subscription_extension?
      }
    }, status: :ok
  end

  def downgrade_plan

    unless @user.is_poster_subscribed
      return render json: { success: false, message: I18n.t('cancellation_flow_v2.downgrade.no_active_subscription') },
                    status: :bad_request
    end

    target_plan_id = params[:target_plan_id]
    if target_plan_id.blank?
      Honeybadger.notify("Target plan id is missing", context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.missing_target_plan') },
                    status: :bad_request
    end

    if target_plan_id.present?
      target_plan = Plan.find_by(id: target_plan_id)
      if target_plan.blank?
        Honeybadger.notify("Target plan not found", context: { user_id: @user.id, target_plan_id: target_plan_id })
        return render json: { success: false, message: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.invalid_target_plan') },
                      status: :bad_request
      end
    end

    user_plan = UserPlan.where(user_id: @user.id).last
    current_plan = user_plan.plan
    user_plan_log = UserPlanLog.where(user_id: @user.id, active: true).last
    # If current plan is already monthly, return error
    if current_plan.duration_in_months == target_plan.duration_in_months
      Honeybadger.notify("Current plan is already monthly", context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.already_in_target_plan') },
                    status: :conflict
    end

    current_subscription = @user.active_subscription || @user.on_hold_subscription
    ActiveRecord::Base.transaction do
      current_subscription.set_plan_downgraded_date_key
      user_plan_log.plan_id = target_plan.id
      current_subscription.plan = target_plan
      # Update the subscription plan
      current_subscription.save!
      # Update the user plan
      user_plan.plan = target_plan
      user_plan.amount = target_plan.amount
      user_plan_log.save!
      user_plan.save!
    end

    # Return success response
    render json: {
      success: true,
      message: I18n.t('cancellation_flow_v2.downgrade.downgrade_plan.success'),
    }, status: :ok

  end

  def get_subscription_cancel_data
    subscription_id = @user.cancellable_latest_subscription.id
    render json: {
      rm_user: @user.rm_user_json,
      title: I18n.t('subscription_cancel.title'),
      subscription_id: subscription_id.to_s,
      continue_membership_button_text: I18n.t('subscription_cancel.continue_membership_button_text'),
      cancel_membership_button_text: I18n.t('subscription_cancel.cancel_membership_button_text'),
      cancel_reasons: {
        title: I18n.t('subscription_cancel.cancel_reasons.title'),
        reasons: I18n.t('subscription_cancel.cancel_reasons.reasons'),
        button_text: I18n.t('subscription_cancel.cancel_reasons.button_text')
      },
      analytics_params: {
        subscription_id: subscription_id
      }
    }, status: :ok
  end

  def get_subscription_cancel_data_v2
    subscription_id = @user.cancellable_latest_subscription.id
    is_eligible_for_extension, msg = @user.eligible_for_user_plan_extension_in_cancellation_flow?
    is_user_atleast_paid_once = SubscriptionCharge.where(user_id: @user.id, status: :success).where("amount > 1").exists?

    render json: {
      title: I18n.t('cancellation_flow_v2.title'),
      subscription_id: subscription_id.to_s,
      text: I18n.t('cancellation_flow_v2.text'),
      sub_text: is_eligible_for_extension ? I18n.t('cancellation_flow_v2.sub_text') : nil,
      cancel_reasons: {
        title: I18n.t('cancellation_flow_v2.cancel_reasons.title'),
        sub_title: I18n.t('cancellation_flow_v2.cancel_reasons.sub_title'),
        reasons: I18n.t('cancellation_flow_v2.cancel_reasons.reasons'),
        text: I18n.t('cancellation_flow_v2.cancel_reasons.text'),
        button: {
          type: 'deeplink',
          deeplink: '/cancellation-confirmation-sheet?source=cancel_flow_downgrade_sheet',
          text: I18n.t('cancellation_flow_v2.cancel_reasons.button_text')
        }
      },
      extend_plan_button: is_eligible_for_extension ? {
        type: 'api',
        text: I18n.t('cancellation_flow_v2.extend_plan_button_text'),
        api_url: Constants.extend_plan_url_for_cancel_flow
      } : nil,
      cancel_membership_button: {
        type: 'deeplink',
        deeplink: '/cancel-reasons?source=cancel_flow_downgrade_sheet',
        text: I18n.t('cancellation_flow_v2.cancel_membership_button_text')
      },
      continue_membership_button: is_eligible_for_extension ? nil : {
        type: 'deeplink',
        deeplink: '',
        text: I18n.t('cancellation_flow_v2.continue_membership_button_text')
      },
      analytics_params: {
        subscription_id: subscription_id,
        subscription_status: @user.get_subscription_status,
        is_user_atleast_paid_once: is_user_atleast_paid_once,
        is_in_extension: @user.in_subscription_extension?
      }
    }, status: :ok
  end

  def cancellation_confirmation_sheet
    user_plan = UserPlan.where(user_id: @user.id).last

    unless user_plan.present?
      Honeybadger.notify("No user plan found", context: { user_id: @user.id })
      return render json: {
        success: false,
        message: I18n.t('cancellation_flow_v2.confirmation_sheet.no_active_plan')
      }, status: :bad_request
    end

    if user_plan.end_date > Time.zone.now.end_of_day
      plan_end_date_text = user_plan.end_date.strftime('%d/%m/%y')
    else
      plan_end_date_text = Time.zone.yesterday.strftime('%d/%m/%y')
    end
    render json: {
      title: I18n.t('cancellation_flow_v2.confirmation_sheet.title'),
      sub_title: I18n.t('cancellation_flow_v2.confirmation_sheet.sub_title'),
      description: I18n.t('cancellation_flow_v2.confirmation_sheet.description'),
      text: plan_end_date_text.present? ? I18n.t('cancellation_flow_v2.confirmation_sheet.text', date: plan_end_date_text) : nil,
      button: {
        text: I18n.t('cancellation_flow_v2.confirmation_sheet.button.text')
      }
    }, status: :ok
  end

  def premium_benefits_loss_screen
    # Get carousel data using existing premium_experience_media_carousel method
    is_eligible_for_extension, msg = @user.eligible_for_user_plan_extension_in_cancellation_flow?
    subscription_id = @user.cancellable_latest_subscription.id
    is_user_atleast_paid_once = SubscriptionCharge.where(user_id: @user.id, status: :success).where("amount > 1").exists?

    render json: {
      title: I18n.t('cancellation_flow_v2.premium_benefits_loss_screen.title', default: 'Premium Benefits Loss'),
      sub_title: I18n.t('cancellation_flow_v2.premium_benefits_loss_screen.sub_title'),
      carousel: poster_carousel_in_premium_benefits_loss_screen(user: @user),
      extend_plan_button: is_eligible_for_extension ? {
        type: 'api',
        text: I18n.t('cancellation_flow_v2.extend_plan_button_text'),
        api_url: Constants.extend_plan_url_for_cancel_flow
      } : nil,
      continue_membership_button: is_eligible_for_extension ? nil : {
        type: 'deeplink',
        deeplink: '',
        text: I18n.t('cancellation_flow_v2.continue_membership_button_text')
      },
      cancel_membership_button: {
        text: I18n.t('cancellation_flow_v2.premium_benefits_loss_screen.cancel_membership_button_text'),
        type: 'deeplink',
        deeplink: '/cancel-membership?source=premium_benefits_loss_screen'
      },
      analytics_params: {
        subscription_id: subscription_id,
        subscription_status: @user.get_subscription_status,
        is_user_atleast_paid_once: is_user_atleast_paid_once,
        is_in_extension: @user.in_subscription_extension?
      }

    }, status: :ok
  end

  def start_trial_popup
    trial_duration = Metadatum.get_user_trail_duration(user_id: @user.id)
    if trial_duration.blank? || trial_duration.zero?
      Honeybadger.notify("No trial duration found for user", context: { user_id: @user.id })
      return render json: { success: false, message: 'ఏదో సరిగ్గా లేదు.' }, status: :bad_request
    end
    button_text = "మీ #{trial_duration}-రోజుల ఉచిత ట్రయల్‌ని ప్రారంభించండి"
    render json: {
      user: @user.get_user_response_hash(@app_version, include_unverified_badge: true),
      title: "ప్రజా ప్రీమియంకు స్వాగతం!",
      button_details: {
        text: button_text,
        auto_perform_action: true,
        type: 'start_trial',
        deeplink: "/feeds/my_feed",
        end_point: "/start-trial"
      }
    }, status: :ok
  end

  def get_posters_feed_filters
    poster_affiliated_party_id = @user.get_poster_affiliated_party_id&.to_i
    user_joined_circle_ids = @user.get_user_joined_interest_circle_ids
    user_circle_ids = []

    if poster_affiliated_party_id.present?
      if user_joined_circle_ids.include?(poster_affiliated_party_id)
        # Poster affiliated party ID exists and is part of user's joined circles
        user_circle_ids = [poster_affiliated_party_id]
        relevant_leader_circle_ids = CirclesRelation.party_affiliated_circle_ids(party_id: poster_affiliated_party_id,
                                                                                 circle_ids: user_joined_circle_ids)
        user_circle_ids += relevant_leader_circle_ids
      else
        user_circle_ids = []
      end
    else
      user_role = @user.get_badge_role_including_unverified
      if user_role.present? && user_role.get_badge_user_affiliated_party_circle_id.positive?
        if user_joined_circle_ids.include?(@user.affiliated_party_circle_id)
          # User has a role, and their affiliated party circle is part of joined circles
          user_circle_ids = [@user.affiliated_party_circle_id]
          relevant_leader_circle_ids = CirclesRelation.party_affiliated_circle_ids(party_id: user_role,
                                                                                   circle_ids: user_joined_circle_ids)
          user_circle_ids += relevant_leader_circle_ids
        else
          user_circle_ids = []
        end
      else
        # Default to the user's joined interest circles
        user_circle_ids = user_joined_circle_ids
      end
    end

    posters_feed_filter_circle_ids = PosterFeed.poster_feed_filters(user_circle_ids: user_circle_ids)
    filters = []
    filters << {
      image_url: Constants.poster_feed_for_you_image_url,
      filter: {
        circle_id: nil,
      } }
    photo_id_with_circle_id = Circle.where(id: posters_feed_filter_circle_ids).pluck(:id, :photo_id).to_h
    # fetch all photo urls in one go and map them to their circle id
    photos = Photo.where(id: photo_id_with_circle_id.values)
    posters_feed_filter_circle_ids.each do |circle_id|
      photo_url = photos.find { |photo| photo.id == photo_id_with_circle_id[circle_id] }&.url || Constants.poster_feed_for_you_image_url
      filters << {
        image_url: photo_url,
        filter: {
          circle_id: circle_id
        },
        analytics_params: { circle_id: circle_id }
      }
    end

    # Some circles might not contain atleast 1 poster, we are not showing that as filter
    # So, sending both the count of filters and the count of circles
    analytics_params = {
      filters_count: filters.count,
      circles_count: user_circle_ids.count,
    }

    render json: { filters: filters, show_help: @user.has_premium_layout?,
                   analytics_params: analytics_params }, status: :ok
  end

  # def start_trial
  #   begin
  #     Metadatum.create(entity: @user, key: Constants.user_poster_trial_start_date_key, value: Time.zone.today)
  #     @user.delete_yet_to_start_trial_key
  #   rescue StandardError => e
  #     Honeybadger.notify(e, context: { user_id: @user.id })
  #     return render json: { success: false, message: 'ట్రయల్ ప్రీమియం ప్రారంభించలేదు' }, status: :internal_server_error
  #   end
  #   render json: { success: true, message: 'ట్రయల్ ప్రీమియం ప్రారంభించబడింది' }, status: :ok
  # end

  def extend_user_plan
    # Validate duration_in_days parameter
    unless params[:duration_in_days].present?
      return render json: {
        success: false,
        message: I18n.t('plan_extension.missing_duration')
      }, status: :bad_request
    end

    duration_in_days = params[:duration_in_days].to_i
    if duration_in_days <= 0
      return render json: {
        success: false,
        message: I18n.t('plan_extension.invalid_duration')
      }, status: :bad_request
    end

    # Source parameter is required
    source = params[:source]

    unless source.present?
      return render json: {
        success: false,
        message: I18n.t('plan_extension.missing_source')
      }, status: :bad_request
    end

    # Validate source is a valid reason in UserPlanExtension
    unless UserPlanExtension.reasons.keys.include?(source)
      return render json: {
        success: false,
        message: I18n.t('plan_extension.invalid_source')
      }, status: :bad_request
    end

    user_plan = UserPlan.where(user_id: @user.id).last if @user.is_poster_subscribed
    return render json: { success: false, message: I18n.t('plan_extension.no_active_plan') },
                  status: :unprocessable_entity unless user_plan.present?

    # Check if there's a recent extension created in the last 5 minutes
    # Using the composite index on user_id and created_at for better performance
    recent_extension = UserPlanExtension.where(user_id: @user.id)
                                        .where('created_at >= ?', 5.minutes.ago)
                                        .where(reason: source)
                                        .order(created_at: :desc)
                                        .first

    if recent_extension.present?
      # If there's a recent extension, return success without creating a new one
      return render json: {
        success: true,
        message: I18n.t('plan_extension.success'),
        duplicate: true
      }, status: :ok
    end

    # Check if user is eligible for extension
    is_eligible_for_extension, msg = @user.eligible_for_user_plan_extension_in_cancellation_flow?
    if !is_eligible_for_extension
      render json: { success: false, message: msg }, status: :forbidden
      return
    end

    # Create the extension
    ActiveRecord::Base.transaction do
      old_end_date = user_plan.end_date
      extension = UserPlanExtension.create!(
        user: @user,
        user_plan: user_plan,
        duration_in_days: duration_in_days,
        reason: source
      )

      new_start_date = old_end_date.advance(days: 1).beginning_of_day
      new_end_date = new_start_date.advance(days: duration_in_days - 1).end_of_day

      user_plan.update!(
        end_date: new_end_date,
        source: "user-plan-extension-#{extension.id}"
      )

      UserPlanLog.create!(
        user_id: @user.id,
        plan_id: user_plan.plan_id,
        start_date: new_start_date,
        end_date: new_end_date,
        entity: extension,
        active: true
      )
    end

    SyncMixpanelUser.perform_async(@user.id)

    render json: {
      success: true,
      message: I18n.t('plan_extension.success'),
      duplicate: false
    }, status: :ok
  end

  def update_poster_photo
    # if there is no user_photo_type param, then return bad request
    if params[:user_photo_type].blank?
      return render json: { success: false, error_code: 'missing_user_photo_type' }, status: :bad_request
    end

    # if user_photo_type is not valid, then return bad request
    if !Frame::USER_PHOTO_TYPES.values.include?(params[:user_photo_type])
      return render json: { success: false, error_code: 'invalid_user_photo_type' }, status: :bad_request
    end

    if params[:photo_id].blank?
      photo_data = params[:photo_data]
      if photo_data.blank?
        return render json: { success: false, error_code: 'missing_photo_data' }, status: :bad_request
      end

      photo = Photo.new(ms_data: photo_data, user: @user, service: photo_data[:service])
      photo.save!
    else
      id = params[:photo_id]
      type = params[:photo_type]
      photo = type.constantize.find(id)
    end

    case params[:user_photo_type]
    when Frame::USER_PHOTO_TYPES[:cutout]
      @user.update(poster_photo: photo)
    when Frame::USER_PHOTO_TYPES[:with_background]
      @user.update(poster_photo_with_background: photo)
    when Frame::USER_PHOTO_TYPES[:family_cutout]
      @user.update(family_frame_photo: photo)
    when Frame::USER_PHOTO_TYPES[:hero_cutout]
      @user.update(hero_frame_photo: photo)
    else
      # we are already checking for valid type, this block should be unreachable
      raise "Invalid photo type"
    end

    render json: { type: photo.class.name, photo: photo }, status: :ok
  end

  def poster_photos
    # if there is no user_photo_type param, then return bad request
    if params[:user_photo_type].blank?
      return render json: { success: false, error_code: 'missing_user_photo_type' }, status: :bad_request
    end

    # if user_photo_type is not valid, then return bad request
    if !Frame::USER_PHOTO_TYPES.values.include?(params[:user_photo_type])
      return render json: { success: false, error_code: 'invalid_user_photo_type' }, status: :bad_request
    end

    versions = PaperTrail::Version.where(tag: 'user_poster_photos_changed', item_id: @user.id, item_type: @user.class.name).order(id: :desc)

    # id, type
    poster_photo_entities = []

    case params[:user_photo_type]
    when Frame::USER_PHOTO_TYPES[:cutout]
      poster_photo_entities = versions.map { |version|
        version_user = version.reify
        if version_user.poster_photo_id.present? && version_user.poster_photo_type.present?
          { id: version_user.poster_photo_id, type: version_user.poster_photo_type }
        else
          nil
        end
      }
      if @user.poster_photo_id.present? && @user.poster_photo_type.present?
        poster_photo_entities = poster_photo_entities.unshift(
          { id: @user.poster_photo_id, type: @user.poster_photo_type }
        )
      end
    when Frame::USER_PHOTO_TYPES[:with_background]
      poster_photo_entities = versions.map { |version|
        version_user = version.reify
        if version_user.poster_photo_with_background_id.present? && version_user.poster_photo_with_background_type.present?
          { id: version_user.poster_photo_with_background_id, type: version_user.poster_photo_with_background_type }
        else
          nil
        end
      }
      if @user.poster_photo_with_background_id.present? && @user.poster_photo_with_background_type.present?
        poster_photo_entities = poster_photo_entities.unshift(
          { id: @user.poster_photo_with_background_id, type: @user.poster_photo_with_background_type }
        )
      end
    when Frame::USER_PHOTO_TYPES[:family_cutout]
      poster_photo_entities = versions.map { |version|
        version_user = version.reify
        if version_user.family_frame_photo_id.present? && version_user.family_frame_photo_type.present?
          { id: version_user.family_frame_photo_id, type: version_user.family_frame_photo_type }
        else
          nil
        end
      }
      if @user.family_frame_photo_id.present? && @user.family_frame_photo_type.present?
        poster_photo_entities = poster_photo_entities.unshift(
          { id: @user.family_frame_photo_id, type: @user.family_frame_photo_type }
        )
      end
    when Frame::USER_PHOTO_TYPES[:hero_cutout]
      poster_photo_entities = versions.map { |version|
        version_user = version.reify
        if version_user.hero_frame_photo_id.present? && version_user.hero_frame_photo_type.present?
          { id: version_user.hero_frame_photo_id, type: version_user.hero_frame_photo_type }
        else
          nil
        end
      }
      if @user.hero_frame_photo_id.present? && @user.hero_frame_photo_type.present?
        poster_photo_entities = poster_photo_entities.unshift(
          { id: @user.hero_frame_photo_id, type: @user.hero_frame_photo_type }
        )
      end
    else
      # we are already checking for valid type, this block should be unreachable
      raise "Invalid photo type"
    end

    poster_photo_entities = poster_photo_entities.compact.uniq

    # filter out photo entities with entity type photo and admin media into seperate vars
    photo_entities = poster_photo_entities.select { |entity| entity[:type] == 'Photo' }
    admin_media_entities = poster_photo_entities.select { |entity| entity[:type] == 'AdminMedium' }

    max_required_photos = 20

    admin_media_photoes = AdminMedium.where(id: admin_media_entities.map { |entity| entity[:id] }).order(id: :desc).limit(max_required_photos)
    photos = Photo.where(id: photo_entities.map { |entity| entity[:id] }).where.not(service: :azure).order(id: :desc).limit(max_required_photos)

    count = 0
    poster_photos = []
    poster_photo_entities.each do |entity|
      if entity[:type] == 'Photo'
        photo = photos.find { |photo| photo.id == entity[:id] }
      else
        photo = admin_media_photoes.find { |admin_media| admin_media.id == entity[:id] }
      end

      if photo.present?
        poster_photos << { type: entity[:type], photo: photo }
        count += 1
      end

      break if count >= max_required_photos
    end

    render json: { poster_photos: poster_photos.compact }, status: :ok
  end

  def plan_upgrade_details
    campaign_id = params[:campaign_id]
    campaign = campaign_id.present? ? Campaign.find_by(id: campaign_id) : nil
    if @user.get_active_user_plan.blank?
      return render json: { success: false, message: I18n.t('upgrade_plan.no_active_subscription') },
                    status: :bad_request
    end

    user_cohort = nil
    target_plan_duration_in_months = if campaign.present?
                                       user_cohort = @user.user_cohort
                                       if user_cohort.blank?
                                         Rails.logger.warn("User cohort is blank for user_id: #{@user.id} and campaign id #{campaign_id}")
                                         return render json: { success: false, message: 'You are not eligible for this campaign' },
                                                       status: :bad_request
                                       end
                                       campaign.cohort_details_json.dig(user_cohort, 0, "plan_duration_in_months")
                                     else
                                       (params[:target_duration_in_months] || 12).to_i
                                     end

    @user.mark_as_shown_upgrade_package_sheet

    plan_details = fetch_plan_details_for_upgrade(user: @user, target_plan_duration_in_months: target_plan_duration_in_months)

    if plan_details[:current_plan].duration_in_months == target_plan_duration_in_months
      return render json: { message: I18n.t('upgrade_plan.already_in_target_plan',
                                            duration: target_plan_duration_in_months) }, status: :conflict
    end

    render json: build_upgrade_plan_json(user: @user, plan_details: plan_details, campaign: campaign,
                                         user_cohort: user_cohort), status: :ok
  end

  def offer_reveal_sheet
    campaign = @user.user_eligible_1_year_campaign
    if campaign.blank?
      Honeybadger.notify("No campaign found for user", context: { user_id: @user.id })
      return render json: { success: false, message: I18n.t('offer_reveal_sheet.no_campaign') }, status: :bad_request
    end

    render json: {
      image_url: campaign.image_url,
      label: nil,
      gift_offer_text: I18n.t('offer_reveal_sheet.gift_offer_text', campaign_name: campaign.name),
      sub_text: I18n.t('offer_reveal_sheet.sub_text'),
      button_text: I18n.t('offer_reveal_sheet.button_text'),
      skip_text: I18n.t('offer_reveal_sheet.skip_text'),
      deeplink: "/upgrade?source=offer_reveal_sheet&campaign_id=#{campaign.id}&offer=true",
      analytics_params: {
        campaign_id: campaign.id,
        campaign_name: campaign.name,
      },
      params: {
        campaign_id: campaign.id,
        offer: true
      }
    }, status: :ok
  end

  def offer_reveal_status
    Rails.logger.warn("Offer reveal status called: #{params}")
    campaign_id = params[:campaign_id]
    if campaign_id.blank?
      Honeybadger.notify("campaign_id is blank", context: { user_id: @user.id })
      return render json: { success: false, message: "campaign_id can't be blank" }, status: :bad_request
    end

    if params[:status].blank?
      Honeybadger.notify("offer reveal status is blank", context: { user_id: @user.id })
      return render json: { success: false, message: "status can't be blank" }, status: :bad_request
    end

    UserMetadatum.find_or_create_by(user_id: @user.id, key: Constants.campaign_offer_reveal_status_key(campaign_id))
                 .update(value: params[:status])
    render json: {
      success: true,
    }, status: :ok
  end

  # Method to Update the layout feedback review status on UserPosterLayout
  def layout_feedback_review_status
    review_status = params[:review_status]

    if review_status.blank?
      Honeybadger.notify("review_status is blank", context: { user_id: @user.id })
      return render json: { success: false, message: "review_status can't be blank" }, status: :bad_request
    end

    upl = @user.get_user_poster_layout

    if upl.blank?
      Honeybadger.notify("UserPosterLayout is blank while updating layout feedback review status", context: { user_id: @user.id })
      return render json: { success: false, message: "UserPosterLayout can't be blank" }, status: :bad_request
    end

    if review_status == 'rejected' && params[:reject_reason].present?
      upl_metadatum = Metadatum.find_or_initialize_by(entity: upl, key: Constants.layout_rejection_reason)
      upl_metadatum.value = params[:reject_reason]
      upl_metadatum.save!
    end

    upl.update(review_status: review_status)

    render json: { success: true }, status: :ok
  end

  private

  def build_pay_wall(plan)
    eligible_for_self_trial = @user.eligible_for_self_trial?

    plan_duration = plan.duration_in_months
    plan_amount_duration_text = case plan_duration
                                when 1
                                  I18n.t('recharge_paywall.pay_block.plan_amount_duration_details.one', amount: plan.amount)
                                when 12
                                  I18n.t('recharge_paywall.pay_block.plan_amount_duration_details.twelve', amount: plan.amount)
                                end

    [
      {
        type: eligible_for_self_trial ? 'image' : 'text',
        text: I18n.t('recharge_paywall.pay_block.trial_start_text'),
        image_url: Constants.designer_photo_url,
        sub_text: nil,
        define_text: if eligible_for_self_trial
                       I18n.t('recharge_paywall.pay_block.no_layout_trial_start_define_text')
                     else
                       I18n.t('recharge_paywall.pay_block.trial_start_define_text')
                     end,
      },
      {
        type: 'text',
        text: I18n.t('recharge_paywall.pay_block.trial_duration_text'),
        sub_text: I18n.t('recharge_paywall.pay_block.trial_duration_sub_text'),
        define_text: I18n.t('recharge_paywall.pay_block.trial_duration_define_text'),
      },
      {
        type: 'text',
        text: I18n.t('recharge_paywall.pay_block.plan_text', amount: plan.amount),
        sub_text: nil,
        define_text: plan_amount_duration_text,
      }
    ]
  end

  def build_annual_pay_wall
    eligible_for_self_trial = @user.eligible_for_self_trial?

    [
      {
        type: eligible_for_self_trial ? 'image' : 'text',
        text: I18n.t('annual_recharge_paywall.pay_block.trial_start_text'),
        image_url: Constants.designer_photo_url,
        sub_text: eligible_for_self_trial ? I18n.t('annual_recharge_paywall.pay_block.no_layout_trial_start_sub_text') : I18n.t('annual_recharge_paywall.pay_block.trail_start_sub_text'),
        define_text: if eligible_for_self_trial
                       I18n.t('annual_recharge_paywall.pay_block.no_layout_trial_start_define_text')
                     else
                       I18n.t('annual_recharge_paywall.pay_block.trial_start_define_text')
                     end,
      },
      {
        type: 'text',
        text: I18n.t('annual_recharge_paywall.pay_block.trial_duration_text'),
        sub_text: I18n.t('annual_recharge_paywall.pay_block.trial_duration_sub_text'),
        define_text: I18n.t('annual_recharge_paywall.pay_block.trial_duration_define_text'),
      },
      {
        type: 'icon',
        sub_text: I18n.t('annual_recharge_paywall.pay_block.post_trial_sub_text'),
        define_text: I18n.t('annual_recharge_paywall.pay_block.post_trial_define_text')
      }
    ]
  end

  def build_help(rm_user)
    {
      title: I18n.t('recharge_paywall.help.title'),
      rm_option_text: rm_user.present? ? I18n.t('recharge_paywall.help.rm_option_text') : nil,
      payment_share_text: I18n.t('recharge_paywall.help.payment_share_text'),
    }
  end

  def build_existing_premium_users(duration_in_months: nil)
    if duration_in_months.blank?
      existing_premium_users = {
        title: I18n.t('recharge_paywall.existing_premium_users.title'),
        users: @user.premium_users_list_for_premium_experience,
      }
    else
      existing_premium_users = {
        title: I18n.t('upgrade_plan.existing_premium_specific_duration_users.title', duration: duration_in_months),
        users: @user.premium_users_list_for_premium_experience(duration_in_months: duration_in_months),
      }
    end
    existing_premium_users[:users].blank? ? nil : existing_premium_users
  end

  def build_analytics_params(plan, rm_user)
    {
      is_self_trial_user: @user.eligible_for_self_trial?,
      plan_id: plan.id,
      plans_count: 1,
      default_plan_duration: plan.duration_in_months,
      default_plan_id: plan.id,
      default_plan_amount: plan.amount,
      rm_user_name: rm_user&.dig(:name),
      rm_user_id: rm_user&.dig(:id),
    }.compact
  end

  def set_user
    @request_user = if !params[:id].nil? && params[:id] != 'null'
                      if params[:id].to_i.to_s == params[:id].to_s
                        User.active.find_by(id: params[:id])
                      else
                        User.active.find_by_hashid(params[:id])
                      end
                    elsif !params[:user_id].nil? && params[:user_id] != 'null'
                      if params[:user_id].to_i.to_s == params[:user_id].to_s
                        User.active.find_by(id: params[:user_id])
                      else
                        User.active.find_by_hashid(params[:user_id])
                      end
                    else
                      @user
                    end
    if @request_user.nil?
      render json: { message: 'యూసర్ కనుగొనబడలేదు' }, status: :not_found
    end
  rescue StandardError
    render json: { message: 'యూసర్ ప్రొఫైల్ని ప్రాసెస్ చేయలేకపోయాము. కాసేపాగి ప్రయత్నించండి' },
           status: :unprocessable_entity
  end

  def set_circle
    @circle = Circle.find(params[:circle_id])
  rescue StandardError
    if ActiveRecord::RecordNotFound
      render json: { message: 'సర్కిల్ కనుగొనబడలేదు' }, status: :not_found
    else
      render json: { message: 'సర్కిల్ ప్రాసెస్ చేయలేకపోయాము. కాసేపాగి ప్రయత్నించండి' },
             status: :unprocessable_entity
    end
  end

  # Only allow a trusted parameter "white list" through.
  def user_params
    params.require(:user).permit(:name, :email, :phone, :active, :verified, :photo)
  end

  def build_support_option(identifier:, text:, type:, deeplink: "", api_url: "", button_text:, button_icon:, sub_options: [], body_data: {}, analytics: {})
    {
      text: I18n.t(text),
      identifier: identifier,
      type: type,
      deeplink: deeplink,
      api_url: api_url,
      button_text: I18n.t(button_text),
      sub_options: sub_options,
      icon: button_icon,
      body_data: body_data,
      analytics_params: { option: identifier }.merge(analytics),
    }
  end

  def get_retry_twiml_response
    response = Twilio::TwiML::VoiceResponse.new
    response.gather(num_digits: 1, action: "#{Constants.get_api_host}/users/#{@request_user.hashid}/charge-ivr-menu-selection", timeout: 15) do |g|
      g.play(url: "https://thankful-join-9973.twil.io/m-play-2.wav")
    end
    response.to_s
  end

  def get_success_twiml_response
    response = Twilio::TwiML::VoiceResponse.new
    response.play(url: "https://thankful-join-9973.twil.io/m-play-3.wav")
    response.hangup
    response.to_s
  end
end
