# frozen_string_literal: true

class AgentPartner::AuthController < ActionController::API
  include AgentPartnerAuthLogic

  before_action :authenticate_agent_partner, except: [:login, :get_otp]

  def get_otp
    phone = params[:phone]
    partner = AgentPartner.find_by(phone: phone)
    return render json: { success: false, message: I18n.t('errors.invalid_phone_number') }, status: :unauthorized unless partner&.active?

    # Generate OTP
    otp_count, code = partner.generate_login_otp

    # Send SMS OTP (reuse existing job or service)
    SendOtpJob.perform_async(nil, partner.phone, code, otp_count, "")

    Rails.logger.debug("OTP sent: #{code}")

    render json: { success: true, message: I18n.t('jathara.agent_partner.otp_sent') }, status: :ok
  end

  def login
    phone = params[:phone].to_i
    otp = params[:otp].to_i

    partner = AgentPartner.find_by(phone: phone, active: true)
    return render json: { success: false, message: I18n.t('errors.invalid_phone_number') }, status: :unauthorized if partner.nil?

    # Validate OTP
    is_valid = partner.validate_otp(otp)
    return render json: { success: false, message: I18n.t('errors.invalid_otp') }, status: :unprocessable_entity unless is_valid

    # Generate JWT token
    token = partner.generate_jwt_token

    response.set_header(Constants.jwt_access_token_header, token)

    render json: { success: true, access_token: token }, status: :ok
  end

  def logout
    @agent_partner.deactivate_jwt_token(@jti)

    render json: { success: true }, status: :ok
  end
end
