# frozen_string_literal: true

module J<PERSON><PERSON><PERSON>ualAuth
  extend ActiveSupport::Concern
  include AgentPartnerAuthLogic

  included do
    # Skip the default admin authentication
    skip_before_action :authenticate_admin_user!, if: :agent_partner_request?

    # Skip CSRF token verification for agent partner requests
    skip_before_action :verify_authenticity_token, if: :agent_partner_request?
  end

  private

  def check_dual_auth
    # Try admin user authentication first
    if admin_user_signed_in?
      begin
        authorize current_admin_user, :can_access_jathara?
        @current_auth_type = :admin
        return
      rescue Pundit::NotAuthorizedError
        return render json: { success: false, message: I18n.t('errors.permission_denied_text') }, status: :unauthorized
      end
    end

    # If admin auth failed, try agent partner auth
    if agent_partner_request?
      authenticate_agent_partner
      @current_auth_type = :agent_partner if @agent_partner.present?
      return @agent_partner.present?
    end

    # If both failed, return unauthorized
    render json: { success: false, message: I18n.t('errors.permission_denied_text') }, status: :unauthorized
  end

  def agent_partner_request?
    request.headers['Authorization'].present? &&
    request.headers['Authorization'].sub('Bearer ', '').present? &&
    !admin_user_signed_in?
  end

  def current_user
    if @current_auth_type == :admin
      current_admin_user
    else
      @agent_partner
    end
  end

  def current_creator
    if @current_auth_type == :admin
      current_admin_user
    else
      @agent_partner
    end
  end

  # authenticate_agent_partner method is now provided by AgentPartnerAuthLogic
end
