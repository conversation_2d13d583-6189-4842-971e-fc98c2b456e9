# frozen_string_literal: true

module AgentPartnerAuthLogic
  extend ActiveSupport::Concern

  private

  # This method authenticates an agent partner without rendering unauthorized
  def authenticate_agent_partner
    @agent_partner = nil
    @jti = nil

    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')
      if token.present?
        payload = JsonWebToken.decode(token)

        if payload.present?
          @jti = payload["nano_id"]&.strip

          if @jti.present?
            # Extract agent partner id from payload
            agent_partner_id = nil
            agent_partner_id = payload["agent_partner_id"]&.to_i

            # Verify if there is a valid agent partner id
            @agent_partner = AgentPartner.find_by(id: agent_partner_id, active: true) if agent_partner_id.present?
          end
        end
      end
    end

    if @agent_partner.nil? || @jti.nil? || !$redis.sismember(@agent_partner.jwt_token_key, @jti)
      render json: { message: I18n.t('errors.permission_denied_text') }, status: :unauthorized
    end
  end
end
