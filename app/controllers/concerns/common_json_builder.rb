module CommonJsonBuilder
  extend ActiveSupport::Concern

  def fetch_plan_details_for_upgrade(user:, target_plan_duration_in_months: 12)
    target_plan = Plan.get_plan_based_on_duration(user: user, duration_in_months: target_plan_duration_in_months)
    target_plan_amount = user.get_plan_amount_based_on_duration(plan: target_plan, duration: target_plan_duration_in_months)
    current_user_plan = user.get_active_user_plan
    current_plan = current_user_plan.plan
    total_savings = ((current_user_plan.amount * target_plan_duration_in_months) - target_plan_amount).round(-2)

    {
      target_plan: target_plan,
      target_plan_amount: target_plan_amount,
      current_plan: current_plan,
      current_user_plan_amount: current_user_plan.amount,
      total_savings: total_savings,
      target_plan_duration_in_months: target_plan_duration_in_months
    }
  end

  def build_upgrade_plan_json(user:, plan_details:, campaign: nil, user_cohort: nil,
                              send_as_feed_item: false)
    upgrade_plan_hash = {
      title: I18n.t('upgrade_plan.title', duration: plan_details[:target_plan_duration_in_months]),
      terms: user.terms_json_v1,
      button_details: {
        type: 'buy',
        text: I18n.t('upgrade_plan.button_text', amount: plan_details[:target_plan_amount]),
      },
      use_juspay_sdk: AppVersionSupport.juspay_sdk_enabled?,
      payment_gateway: 'intent',
      existing_premium_users: send_as_feed_item ? [] : build_existing_premium_users(duration_in_months: plan_details[:target_plan_duration_in_months]),
      current_plan: {
        plan_id: plan_details[:current_plan].id,
        title: I18n.t('upgrade_plan.current_plan.title'),
        amount: "₹#{plan_details[:current_user_plan_amount]}",
        duration_text: I18n.t('upgrade_plan.current_plan.duration_text'),
      },
      target_plan: {
        plan_id: plan_details[:target_plan].id,
        title: I18n.t('upgrade_plan.target_plan.title'),
        amount: "₹#{plan_details[:target_plan_amount]}",
        duration_text: I18n.t('upgrade_plan.target_plan.duration_text'),
        per_month_text: I18n.t('upgrade_plan.target_plan.per_month_text', amount: plan_details[:target_plan_amount] / plan_details[:target_plan_duration_in_months]),
      },
      savings: {
        title: I18n.t('upgrade_plan.savings.title'),
        text: I18n.t('upgrade_plan.savings.text', amount: plan_details[:total_savings]),
        sub_text: I18n.t('upgrade_plan.savings.sub_text', amount: plan_details[:total_savings] / plan_details[:target_plan_duration_in_months]),
      },
      benefits: [
        I18n.t('upgrade_plan.benefits.benefit_1', amount: plan_details[:total_savings]),
        I18n.t('upgrade_plan.benefits.benefit_2'),
        I18n.t('upgrade_plan.benefits.benefit_3')
      ],
      secure_text: I18n.t('upgrade_plan.secure_text'),
      cancel_text: I18n.t('upgrade_plan.cancel_text'),
      analytics_params: {
        current_plan_id: plan_details[:current_plan].id,
        current_plan_amount: plan_details[:current_user_plan_amount],
        target_plan_id: plan_details[:target_plan].id,
        target_plan_amount: plan_details[:target_plan_amount],
        total_savings: plan_details[:total_savings],
        campaign_id: campaign&.id,
        user_cohort: user_cohort,
        offer: campaign.present?,
      }.compact,
      offer: campaign.present?,
      background_image_url: campaign&.image_url,
    }

    if send_as_feed_item
      feed_type = 'upgrade'
      upgrade_plan_hash[:feed_type] = feed_type
      upgrade_plan_hash[:feed_item_id] = feed_type + "_" + Time.zone.now.to_i.to_s
    end
    upgrade_plan_hash
  end
end
