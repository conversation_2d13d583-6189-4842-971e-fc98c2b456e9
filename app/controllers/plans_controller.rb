class PlansController < ApiController
  before_action :set_logged_in_user
  before_action :set_plan, only: [:subscribe, :juspay_subscribe, :intent_checkout_subscribe]
  include PlanHelper

  def subscribe
    return render_conflict unless valid_plan?

    save_trial_duration_of_user
    subscription = find_or_create_subscription
    render_subscription(subscription)
  end

  def juspay_subscribe
    return render_conflict unless valid_plan?

    save_trial_duration_of_user
    subscription = find_or_create_juspay_subscription
    render_juspay_subscription(subscription, params[:package_name])
  end

  def intent_checkout_subscribe
    return render_conflict unless valid_plan?

    save_trial_duration_of_user
    subscription = find_or_create_phonepe_subscription(params[:package_name])
    render_phonepe_subscription(subscription)
  end

  def premium_bottom_sheet
    if @user.active_subscription.present?
      return render json: { success: false, message: I18n.t('errors.subscriptions.already_active') }, status: :conflict
    end

    if @user.blank?
      return render json: { success: false }, status: :not_found
    end

    info_toast = @user.subscription_info_toast(user: @user)
    title = build_title(info_toast)
    title_text_color = info_toast[:type] == 'info' ? 0xff4EA502 : 0xffF64646
    sub_title = nil
    terms = @user.terms_json_v1

    plans = Plan.get_premium_plans(user: @user)

    # get one month with help of duration_in_months
    one_month_plan = plans.find { |plan| plan.duration_in_months == 1 }

    one_year_plan = plans.find { |plan| plan.duration_in_months == 12 }

    plans = premium_plans_json(user: @user, plans: plans)

    premium_special_offer_user = @user.eligible_for_one_month_special_campaign?
    if one_month_plan.present? && premium_special_offer_user
      special_offer_price = @user.get_plan_amount_based_on_duration(plan: one_month_plan,
                                                                    duration: one_month_plan.duration_in_months)
      one_month_plan_json = plans.find { |plan| plan[:id] == one_month_plan.id }
      one_month_plan_json[:amount] = special_offer_price
      one_month_plan_json[:per_month_text] = "₹#{one_month_plan.amount}/నె"
      discount_percentage = (((one_month_plan.total_amount - special_offer_price) / one_month_plan.total_amount.to_f) * 100.0).round(0)
      discount_percentage = discount_percentage == 100 ? discount_percentage - 1 : discount_percentage
      one_month_plan_json[:discount_percentage] = discount_percentage
      one_month_plan_json[:discount_text] = "#{discount_percentage}% ఆదా"
      sub_title = "మొదటి నెల ₹#{special_offer_price}, తర్వాత నెల నుండి ₹#{one_month_plan.amount}"
      # make selected as false for existing selected plan
      plans.each { |plan| plan[:selected] = false }
      # now make selected as true for one month plan
      one_month_plan_json[:selected] = true
    end

    if one_year_plan.present? && @user.eligible_for_half_price_discount?
      one_year_plan_amount = @user.get_plan_amount_based_on_duration(plan: one_year_plan,
                                                                     duration: one_year_plan.duration_in_months)
      one_year_plan_json = plans.find { |plan| plan[:id] == one_year_plan.id }
      one_year_plan_json[:amount] = one_year_plan_amount
      one_year_plan_json[:per_month_amount] = one_year_plan_json[:amount] / 12
      one_year_plan_json[:per_month_text] = I18n.t('premium_bottom_sheet.plans.per_month_text',
                                                   amount: one_year_plan_json[:amount] / 12)
      discount_percentage = (((one_year_plan.total_amount - one_year_plan_amount) / one_year_plan.total_amount.to_f) * 100.0).round(0)
      one_year_plan_json[:discount_percentage] = discount_percentage
      one_year_plan_json[:discount_text] = "#{discount_percentage}% ఆదా"
      sub_title = "మొదటి సంవత్సరం ₹#{one_year_plan_amount}, తర్వాత సంవత్సరం నుండి ₹#{one_year_plan.amount}"
      # remove one month plan discount text\
      if one_month_plan.present?
        one_month_plan_json = plans.find { |plan| plan[:id] == one_month_plan.id }
        one_month_plan_json[:discount_text] = nil if one_month_plan_json.present?
      end
      # make selected as false for existing selected plan
      plans.each { |plan| plan[:selected] = false }
      # now make selected as true for one year plan
      one_year_plan_json[:selected] = true
    end

    if one_month_plan.present? && one_year_plan.present? && premium_special_offer_user
      # remove one year plan discount text
      one_year_plan_json = plans.find { |plan| plan[:id] == one_year_plan.id }
      one_year_plan_json[:discount_text] = nil
    end

    # If a yearly user is eligible for monthly pitch campaign, make the 1M plan selected
    if one_month_plan.present? && @user.eligible_for_monthly_campaign_for_yearly_users?
      # make selected as false for existing selected plan
      plans.each { |plan| plan[:selected] = false }

      one_month_plan_json = plans.find { |plan| plan[:id] == one_month_plan.id }
      one_month_plan_json[:selected] = true if one_month_plan_json.present?
    end

    max_discount = plans.map { |plan| plan[:discount_text].to_s.scan(/\d+/).map(&:to_i).max }.compact.max || 0
    sub_title = I18n.t('premium_bottom_sheet.sub_title', discount: max_discount) if sub_title.blank?

    existing_premium_users = {
      title: I18n.t('recharge_paywall.existing_premium_users.title'),
      users: @user.premium_users_list_for_premium_experience,
    }
    if existing_premium_users[:users].blank?
      existing_premium_users = nil
    end

    render json: build_response_json(user: @user, title:, title_text_color:, terms:, plans:, sub_title:,
                                     existing_premium_users:), status: :ok
  end

  private

  def find_or_create_phonepe_subscription(package_name)
    active_subscription = @user.active_subscription
    return active_subscription if active_subscription.present? && active_subscription.plan_id == @plan.id
    Subscription.create_plan_phonepe_subscription(plan: @plan, user: @user, package_name: package_name, app_os: @app_os)
  end

  def render_phonepe_subscription(subscription)
    render json: {
      id: "sub_#{subscription.id}",
      intent_url: subscription.active? ? nil : subscription.auth_link,
      share_text: subscription.active? ? nil : subscription.auth_link
    }, status: :ok
  end

  def save_trial_duration_of_user
    return if SubscriptionUtils.has_user_ever_subscribed?(@user.id)

    Metadatum.save_user_trial_data_for_autopay(@user, Constants.poster_trial_default_duration)
  end

  def valid_plan?
    plan_ids = Plan.get_premium_plans(user: @user).map(&:id)
    Rails.logger.debug("Plan ids: #{plan_ids}")
    plan_ids.present? && plan_ids.include?(@plan.id)
  end

  def find_or_create_subscription
    # if active_subscription is present and plan is same then return active_subscription
    # else create new subscription
    active_subscription = @user.active_subscription
    return active_subscription if active_subscription.present? && active_subscription.plan_id == @plan.id
    Subscription.create_plan_subscription(plan: @plan, user: @user)
  end

  def find_or_create_juspay_subscription
    active_subscription = @user.active_subscription
    return active_subscription if active_subscription.present? && active_subscription.plan_id == @plan.id
    Subscription.create_plan_juspay_subscription(plan: @plan, user: @user)
  end

  def render_conflict
    render json: { message: I18n.t('errors.plan_mismatch_error') }, status: :conflict
  end

  def render_subscription(subscription)
    render json: { id: "sub_#{subscription.id}",
                   checkout_url: subscription.active? ? nil : subscription.auth_link,
                   share_text: subscription.active? ? nil : subscription.auth_link },
           status: :ok
  end

  def render_juspay_subscription(subscription, package_name)
    render json: { id: "sub_#{subscription.id}",
                   share_text: subscription.active? ?
                                 nil :
                                 "https://praja.app/plans/#{subscription.plan.id}/juspay-subscribe?user_id=#{subscription.user.hashid}",
                   juspay_payload: if subscription.active?
                                     nil
                                   else
                                     {
                                       action: 'upiTxn',
                                       paymentMethod: 'UPI_PAY',
                                       orderId: subscription.subscription_charges.first.pg_id,
                                       upiSdkPresent: true,
                                       payWithApp: package_name.present? ? package_name : nil,
                                       displayNote: subscription.plan.name,
                                       clientAuthToken: subscription.user.get_juspay_client_auth_token,
                                       shouldCreateMandate: true,
                                       showLoader: true,
                                     }.compact
                                   end },
           status: :ok
  end

  def set_plan
    if params[:plan_id].present? && params[:plan_id].to_i.to_s == params[:plan_id]
      @plan = Plan.find_by(id: params[:plan_id])
    end
    render json: { message: I18n.t('plan_not_found') }, status: :not_found if @plan.nil?
  end
end
