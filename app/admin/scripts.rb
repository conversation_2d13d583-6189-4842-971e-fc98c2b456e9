ActiveAdmin.register_page "Scripts" do
  controller do
    include PlanHelper
  end
  menu label: "Scripts"
  require 'csv'

  page_action :show_checkout_link, method: :post do
    begin
      @user_id = params[:user_id]
      @user = User.find_by(id: @user_id)
      if @user.blank?
        flash[:error] = "User not found!"
      end

      user_layout = UserPosterLayout.where(entity: @user, active: true).first
      if user_layout.blank?
        flash[:error] = "No active UserPosterLayout found for this user!"
        redirect_to admin_scripts_path
        return
      else
        unless @user.has_user_frames?
          flash[:error] = "No frames found for this user! Please select frames for this user before creating checkout link"
          redirect_to admin_scripts_path
          return
        end
      end

      plans = Plan.get_premium_plans(user: @user)
      if plans.blank?
        flash[:error] = "No plans found for this user!"
        redirect_to admin_scripts_path
        return
      end

      plans_json = premium_plans_json(user: @user, plans: plans)

      # fetch the :selected true plan
      selected_plan = plans_json.select { |plan| plan[:selected] }
      selected_plan = selected_plan.first
      @plan = Plan.find_by(id: selected_plan[:id])

      # TODO: Should move to juspay
      subscription = Subscription.get_open_subscription(@plan.id, @user.id)
      if subscription.blank?
        subscription = Subscription.create_plan_subscription(plan: @plan, user: @user)
      end

      if subscription.blank?
        flash[:error] = "Failed to create subscription!"
        redirect_to admin_scripts_path
        return
      end

      @auth_link = subscription.auth_link
      render 'admin/scripts/show_checkout_link'
    rescue => e
      flash[:error] = "Failed to show checkout link! - Error: #{e}"
      redirect_to admin_scripts_path
      return
    end
  end

  page_action :swap_or_update_phone, method: :post do
    user_id = params[:user_id]
    phone = params[:phone]
    if user_id.blank? || phone.blank?
      flash[:error] = "Both user_id and phone are required"
      redirect_to admin_scripts_path and return
    end

    @user1 = User.find_by(id: user_id)
    if @user1.nil?
      flash[:error] = "There is no user for the given user_id"
      redirect_to admin_scripts_path and return
    end

    @user2 = User.find_by(phone: phone)
    if @user2.blank?
      @user1.update(phone: phone)
      flash[:notice] = "Phone number updated successfully"
      redirect_to admin_scripts_path and return
    end

    phone1 = @user1&.phone
    phone2 = phone
    begin
      temp = "4329874389"
      @user1.update(phone: temp)
      @user2.update(phone: phone1)
      @user1.update(phone: phone2)
      flash[:notice] = "Phone numbers swapped successfully"
    rescue => e
      flash[:error] = "Failed to swap phone numbers: #{e.message}"
    end
    redirect_to admin_scripts_path
  end

  page_action :premium_special_offer_event, method: :post do
    @select_plan = params[:select_plan]
    csv_data = params[:csv_data]
    csv_file = csv_data.read
    CSV::Converters[:blank_to_nil] = lambda { |field| field && field.empty? ? nil : field }
    csv = CSV.new(csv_file, headers: true, header_converters: :symbol, converters: [:all, :blank_to_nil])
    required_headers = [:user_id, :no_of_days]
    csv_hash = csv.to_a.map { |row| row.to_hash }
    failed_user_ids = []
    valid_users_count = 0
    csv_hash.each_slice(1000) do |batch|
      user_ids = batch.map { |row| row[:user_id] }
      redis_key = case @select_plan
                  when "rs_1" then Constants.premium_1_rs_user_redis_key
                  when "rs_29" then Constants.premium_29_rs_campaign_redis_key
                  when "rs_59" then Constants.premium_59_rs_campaign_redis_key
                  when "50%_offer" then Constants.premium_half_price_campaign_redis_key
                  else
                    failed_user_ids += user_ids
                  end
      redis_data = []
      begin
        missing_users = batch.select { |row| required_headers.any? { |field| row[field].nil? } }
        failed_user_ids += missing_users.map { |user| user[:user_id] }

        active_mandate_user_ids = Subscription.cancellable.where(user_id: user_ids).pluck(:user_id)
        existing_charge_user_ids = SubscriptionCharge.special_offer.where(user_id: user_ids, status: 'success').pluck(:user_id)
        user_plan_existing_user_ids = UserPlan.where(user_id: user_ids).pluck(:user_id)
        user_plan_not_existing_user_ids = user_ids - user_plan_existing_user_ids
        failed_user_ids |= active_mandate_user_ids + existing_charge_user_ids
        failed_user_ids |= user_plan_not_existing_user_ids

        valid_users = batch.reject { |row| failed_user_ids.include?(row[:user_id]) }
        valid_users_count += valid_users.count

        valid_users.each do |row|
          user_id = row[:user_id]
          given_expire_days = row[:no_of_days].to_i
          expire_time = Time.zone.now.end_of_day.advance(days: given_expire_days).to_i

          redis_data << [expire_time, user_id]
        end
        $redis.pipelined do |pipeline|
          redis_data.each do |expire_time, user_id|
            pipeline.zadd(redis_key, expire_time, user_id)
          end
        end
      rescue => e
        flash[:error] = "An error occurred while processing the batch: #{e.message}"
        Honeybadger.notify("Redis Operations Failed", context: { error: e })
        next
      end
    end

    failed_user_ids = failed_user_ids.compact
    failed_users_csv = CSV.generate(headers: true) do |failed_csv_file|
      failed_csv_file << ["user_id"]
      failed_user_ids.each { |user_id| failed_csv_file << [user_id] }
    end

    if failed_user_ids.any?
      flash[:error] = "Valid users processed: #{valid_users_count}. Some users are not valid for special offer: #{failed_user_ids.count}."
      send_data failed_users_csv, filename: "failed_users.csv", type: "text/csv", disposition: "attachment"
    else
      flash[:notice] = "Special offer enabled successfully for #{valid_users_count} users."
    end
  end

  page_action :add_leads_to_floww, method: :post do
    @select_source = params[:select_source]
    csv_data = params[:csv_data]
    csv_file = csv_data.read
    CSV::Converters[:blank_to_nil] = lambda { |field| field && field.empty? ? nil : field }
    csv = CSV.new(csv_file, headers: true, header_converters: :symbol, converters: [:all, :blank_to_nil])
    csv_hash = csv.to_a.map { |row| row.to_hash }
    failed_leads = []
    valid_leads_count = 0
    csv_hash.each_slice(500) do |batch|
      user_ids = batch.map { |row| row[:user_id] }
      begin
        missing_tag_leads = batch.select { |row| row[:stage] == "Layout Completed" && row[:new_tag].blank? }
        failed_leads += missing_tag_leads.map { |user| user[:user_id] }
        users = user_ids - failed_leads
        existing_users = User.where(id: users).pluck(:id)
        missing_users = users - existing_users
        invalid_leads = User.where(id: existing_users)
                            .where("internal = ? or status != ?", true, "active")
                            .pluck(:id)
        failed_leads |= invalid_leads + missing_users
        layout_users = user_ids - failed_leads
        if batch.any? { |row| row[:stage] == "Layout Completed" }
          no_active_layout_leads = UserPosterLayout.where(entity_type: :user, entity_id: layout_users, active: false)
                                                   .pluck(:entity_id)
          failed_leads |= no_active_layout_leads
        end

        valid_leads = batch.reject { |row| failed_leads.include?(row[:user_id]) }
        valid_leads_count += valid_leads.count
        valid_leads.each do |lead|
          user_id = lead[:user_id]
          lead_type = lead[:lead_type]
          new_tag = lead[:new_tag]
          auto_assign = false
          case lead[:stage]
          when "Fresh Lead"
            premium_pitch = PremiumPitch.find_by(user_id: user_id)
            unless premium_pitch
              premium_pitch = PremiumPitch.create(user_id: user_id, source: @select_source, lead_type: lead_type)
            end

            if premium_pitch.may_shown_interest?
              premium_pitch.shown_interest!(new_tag, auto_assign)
            elsif premium_pitch.interested?
              Floww::AddLead.perform_async(user_id, lead_type, new_tag, auto_assign)
            end
          when "Layout Completed"
            layout_premium_pitch = PremiumPitch.find_by(user_id: user_id)
            if layout_premium_pitch.present?
              layout_premium_pitch.update(lead_type: lead_type) if layout_premium_pitch.lead_type != lead_type
              if layout_premium_pitch.status == "trail_enabled"
                layout_premium_pitch.update(status: :interested)
                Floww::AddLead.perform_async(user_id, lead_type, new_tag, auto_assign)
              end
            else
              layout_premium_pitch = PremiumPitch.create(user_id: user_id, source: @select_source, lead_type: lead_type)
            end

            if layout_premium_pitch.may_shown_interest?
              layout_premium_pitch.shown_interest!(new_tag, auto_assign)
            elsif layout_premium_pitch.interested?
              Floww::AddLead.perform_async(user_id, lead_type, new_tag, auto_assign)
            end

            MovePremiumPitchToLayoutCompleted.perform_at(25.minutes.from_now, user_id)
          end
        end
      rescue => e
        flash[:error] = "An error occurred while processing the batch: #{e.message}"
        next
      end
    end

    if failed_leads.any?
      flash[:error] = "Added valid leads into Floww: #{valid_leads_count}. Some leads are not valid: #{failed_leads.join(', ')}."
    else
      flash[:notice] = "Added #{valid_leads_count} leads into Floww."
    end
    redirect_to admin_scripts_path
  end

  page_action :auto_create_or_update_users, method: :post do
    if current_admin_user.admin_role?
      csv_data = params[:csv_data]
      csv_file = csv_data.read
      CSV::Converters[:blank_to_nil] = lambda do |field|
        field && field.empty? ? nil : field
      end
      csv = CSV.new(csv_file, headers: true, header_converters: :symbol, converters: [:all, :blank_to_nil])
      required_headers = [:name, :phone, :village_id, :role_id, :purview_circle_id, :start_date, :end_date]
      csv_hash = csv.to_a.map { |row| row.to_hash }
      failed_phones = []
      csv_hash.each do |row|
        phone = row[:phone].to_s
        begin
          if phone.nil? || phone.length != 10 || !%w[6 7 8 9].include?(phone[0])
            raise "Invalid phone number"
          end

          missing_fields = required_headers.select { |field| row[field].nil? }
          if missing_fields.any?
            raise "Missing required fields: #{missing_fields.join(', ')}"
          end

          user = User.find_by(phone: phone)
          if user.present?
            next if ['active', 'banned', 'deleted'].include?(user.status)
            if user.status == 'pre_signup'
              user.update(name: row[:name], status: :auto_signed_up, village_id: row[:village_id])
            end
          else
            user = User.create(name: row[:name], phone: phone, village_id: row[:village_id], status: :auto_signed_up)
          end
          user.populate_location
          user.user_circles.build(circle_id: row[:village_id])
          user.save
          UserRole.create(user_id: user.id, role_id: row[:role_id], purview_circle_id: row[:purview_circle_id], primary_role: true,
                          start_date: Time.zone.parse(row[:start_date]), end_date: Time.zone.parse(row[:end_date]))
        rescue => e
          failed_phones << phone
          next
        end
      end

      if failed_phones.any?
        flash[:error] = "Some phone numbers were invalid or had missing fields: #{failed_phones.join(', ')}"
        redirect_to admin_scripts_path
        return
      end
      redirect_to admin_scripts_path, notice: "Auto creating or updating the users is Successful"
    else
      flash[:error] = "You are not authorized to perform this action"
      redirect_to admin_scripts_path
    end
  end

  page_action :send_all_users_notification, method: :post do
    begin
      @title = params[:title]
      @body = params[:body]
      @path = params[:path]

      if @title.blank? || @body.blank? || @path.blank?
        flash[:warning] = "Fields cannot be blank"
      else
        uri = URI.parse("#{Constants.get_garuda_base_url}/users/all/dispatch")

        Net::HTTP.post(
          uri,
          {
            title: @title,
            body: @body,
            path: @path
          }.to_json,
          "Content-Type" => "application/json"
        )

        flash[:success] = "Notification triggered!"
      end
    rescue => e
      flash[:error] = "Failed to send notification! - Error: #{e}"
    end
    redirect_to admin_scripts_path
  end

  page_action :block_for_tagging, method: :post do
    begin
      @user_ids = params[:user_ids]
      user_ids = @user_ids&.split(",").map(&:to_i)
      $redis.del("users_blocked_for_tagging")
      $redis.sadd("users_blocked_for_tagging", user_ids) if user_ids.length > 0

      flash[:notice] = "Updated blocked users list!"
    rescue
      flash[:error] = "Failed to Update!"
    end
    redirect_to admin_scripts_path
  end

  page_action :block_for_commenting, method: :post do
    begin
      @user_ids = params[:user_ids]
      user_ids = @user_ids&.split(",").map(&:to_i)
      $redis.del("users_blocked_for_commenting")
      $redis.sadd("users_blocked_for_commenting", user_ids) if user_ids.length > 0

      flash[:notice] = "Updated blocked users list!"
    rescue
      flash[:error] = "Failed to Update!"
    end
    redirect_to admin_scripts_path
  end

  # page_action :generate_invite_card, method: :post do
  #   @phone_numbers = params[:phone_numbers]
  #   phones = @phone_numbers&.split(",").map(&:to_i)
  #
  #   count = 0
  #   User.where(phone: phones).each do |u|
  #     GenerateInviteCard.perform_async(u.id)
  #     count += 1
  #   end
  #
  #   if phones.size == count
  #     flash[:notice] = "Invite card generation triggered successfully for all #{count} phone numbers!"
  #     redirect_to admin_users_path
  #   elsif count > 0
  #     flash[:warning] = "Only #{count} have been generated!"
  #     redirect_to admin_scripts_path
  #   else
  #     flash[:error] = "Unable to generate!"
  #     redirect_to admin_scripts_path
  #   end
  # end

  page_action :refresh_hashtags, method: :post do
    begin
      Rails.cache.delete(Hashtag::TRENDING_HASHTAGS_CACHE_KEY)

      flash[:notice] = "Refreshed!"
    rescue
      flash[:error] = "Failed to refresh!"
    end

    redirect_to admin_scripts_path
  end

  # uncomment this we want to index user invites again as of now we are commenting this
  # page_action :marketing_sent, method: :post do
  #   begin
  #     if Time.zone.now.hour >= 22 || Time.zone.now.hour < 2
  #       @phone_numbers = params[:phone_numbers]
  #       phones = @phone_numbers&.split(",").map(&:to_i).uniq
  #       phones.each_slice(200) { |b| BulkUpdateUserInvitesSent.perform_async(b) }
  #
  #       ::NewRelic::Agent.record_custom_event('Triggered_BulkUpdateUserInvitesSent', count: phones.count)
  #       flash[:notice] = "Marked!"
  #     else
  #       flash[:warning] = "Only allowed between 10pm and 2am!"
  #     end
  #   rescue
  #     flash[:error] = "Failed to mark!"
  #   end
  #
  #   redirect_to admin_scripts_path
  # end

  page_action :my_feed_districts, method: :post do
    begin
      @district_ids = params[:district_ids]
      new_district_ids = @district_ids&.split(",").map(&:to_i).uniq

      if new_district_ids.count > 0
        if new_district_ids.count == Circle.where(level: :district, active: true, id: new_district_ids).count
          $redis.sadd('my_feed_enabled_district_ids', new_district_ids)

          Circle.where(id: new_district_ids).each do |district|
            Toast.create(
              name: "My Feed (#{district.name_en} users)",
              title: I18n.t('my_feed_districts.title'),
              body: I18n.t('my_feed_districts.body', district_name: district.name),
              admin_user: current_admin_user,
              active: true,
              start_time: Time.zone.now,
              circle_id: district.id,
              max_user_sessions: 3
            )
          end

          flash[:notice] = "Enabled!"
        else
          flash[:error] = "Some of the provided IDs are not District IDs!"
        end
      end
    rescue => e
      flash[:error] = "Failed to enable!"
    end

    redirect_to admin_scripts_path
  end

  page_action :test_user_ids, method: :post do
    begin
      @test_user_ids = params[:test_user_ids]
      user_ids = @test_user_ids&.split(",").map(&:to_i)
      $redis.del("test_user_ids")
      $redis.sadd("test_user_ids", user_ids) if user_ids.length > 0

      flash[:notice] = "Updated test users list!"
    rescue
      flash[:error] = "Failed to Update!"
    end

    redirect_to admin_scripts_path
  end

  page_action :sync_to_mixpanel, method: :get do
    begin
      @sync_user_ids = params[:sync_user_ids]
      if @sync_user_ids.present?
        user_ids = @sync_user_ids&.split(",").map(&:to_i)

        found_user_ids = User.where(id: user_ids, status: :active).pluck(:id)
        missing_user_ids = user_ids - found_user_ids

        found_user_ids.each do |user_id|
          SyncMixpanelUser.perform_async(user_id)
        end

        if missing_user_ids.any?
          flash[:error] = "Users not found for IDs: #{missing_user_ids.join(', ')}"
        end

        flash[notice:] = "Sync started for the following user IDs: #{found_user_ids.join(', ')}"
      else
        flash[:error] = "No user IDs provided"
      end
      redirect_to admin_scripts_path
    rescue StandardError => e
      flash[:error] = "Sync failed: #{e.message}"
      redirect_to admin_scripts_path
    end
  end

  page_action :trigger_premium_pitch_callbacks, method: :post do
    begin
      @user_id = params[:user_id]&.to_i

      if @user_id.blank? || @user_id <= 0
        flash[:error] = "Please provide a valid user ID"
        redirect_to admin_scripts_path
        return
      end

      user = User.find_by(id: @user_id)
      if user.blank?
        flash[:error] = "User with ID #{@user_id} not found"
        redirect_to admin_scripts_path
        return
      end

      premium_pitch = user.premium_pitch
      if premium_pitch.blank?
        flash[:error] = "User #{@user_id} does not have a premium pitch record"
        redirect_to admin_scripts_path
        return
      end

      # Trigger appropriate callback method based on current status
      triggered_callbacks = []
      case premium_pitch.status
      when 'badge_setup', 'badge_setup_no_layout_setup'
        premium_pitch.send(:after_badge_setup)
        triggered_callbacks << 'after_badge_setup'
      when 'layout_setup'
        premium_pitch.send(:after_layout_setup)
        triggered_callbacks << 'after_layout_setup'
      when 'pending_layout_approval'
        premium_pitch.send(:after_sending_for_layout_approval)
        triggered_callbacks << 'after_sending_for_layout_approval'
      when 'trail_enabled'
        premium_pitch.send(:after_trial_enabled)
        triggered_callbacks << 'after_trial_enabled'
      when 'milestone_1'
        premium_pitch.send(:after_milestone_1)
        triggered_callbacks << 'after_milestone_1'
      when 'milestone_2'
        premium_pitch.send(:after_milestone_2)
        triggered_callbacks << 'after_milestone_2'
      when 'trial_low_usage'
        premium_pitch.send(:after_trial_low_usage)
        triggered_callbacks << 'after_trial_low_usage'
      when 'paid'
        premium_pitch.send(:after_paid)
        triggered_callbacks << 'after_paid'
      when 'subscribed_no_layout'
        premium_pitch.send(:after_subscribed_no_layout)
        triggered_callbacks << 'after_subscribed_no_layout'
      when 'interested'
        premium_pitch.send(:after_shown_interest, "", true)
        triggered_callbacks << 'after_shown_interest'
      else
        flash[:warning] = "No callback methods available for status: #{premium_pitch.status}"
        redirect_to admin_scripts_path
        return
      end

      if triggered_callbacks.any?
        flash[:success] = "Successfully triggered callbacks for User #{@user_id} (Status: #{premium_pitch.status}): #{triggered_callbacks.join(', ')}"
      else
        flash[:warning] = "No callbacks were triggered for User #{@user_id}"
      end

    rescue StandardError => e
      flash[:error] = "Failed to trigger premium pitch callbacks: #{e.message}"
    end

    redirect_to admin_scripts_path
  end

  # uncomment this if we want to send marketing messages to users
  # page_action :request_for_user_invite_phones, method: :post do
  #   begin
  #     @location_id = params[:location_id].to_i
  #     @count = params[:count].to_i
  #     @email_id = params[:email_id]
  #     if @location_id == 0 || @count == 0 || @email_id == ""
  #       flash[:warning] = "Fields cannot be blank"
  #     else
  #       enhanced_count = (@count * 1.2).to_i
  #       GetUserInvitesFromEs.perform_async(enhanced_count, @location_id, @email_id, @count)
  #       ::NewRelic::Agent.record_custom_event(
  #         'Triggered_GetUserInvitesFromEs',
  #         location_id: @location_id,
  #         count: @count,
  #         email_id: @email_id,
  #       )
  #       flash[:notice] = "Submitted!"
  #     end
  #   rescue
  #     flash[:error] = "Error!"
  #   end
  #   redirect_to admin_scripts_path
  # end

  page_action :redis_get_key, method: :post do
    begin
      @mode = params[:mode].to_i
      @key_type = params[:key_type].to_i
      @key_name = params[:key_name]
      @set_key_name = params[:set_key_name]
      flash[:notice] = case @key_type
                       when 1
                         if @mode == 1
                           value = $redis.get(@key_name)
                           value.present? ? " #{value} - GET #{@key_name}" : "NIL"
                         else
                           value = $redis.ttl(@key_name)
                           case value
                           when -1
                             "#{@key_name} expiry is not set"
                           when -2
                             "#{@key_name} got expired"
                           else
                             "#{value} - TTL #{@key_name}"
                           end
                         end
                       when 2
                         value = $redis.hget(@key_name, @set_key_name)
                         value.present? ? "#{value} - GET #{@key_name} #{@set_key_name}" : "NIL"
                       when 3
                         value = $redis.sismember(@key_name, @set_key_name)
                         value == true ? "True - SISMEMBER #{@key_name} #{@set_key_name}" : "False - SISMEMBER #{@key_name} #{@set_key_name}"
                       else
                         "Invalid request"
                       end
    rescue
      flash[:error] = "Failed to fetch key!"
    end

    redirect_to admin_scripts_path
  end

  page_action :core_party_user_ids, method: :post do
    begin
      core_party_user_ids = params[:user_ids]
      user_ids = core_party_user_ids&.split(",").map(&:to_i)
      existing_user_ids = User.core_party_user_ids.map(&:to_i)

      new_user_ids = user_ids - existing_user_ids
      old_user_ids = existing_user_ids - user_ids

      # Add new user ids to the set
      $redis.sadd("core_party_user_ids", new_user_ids) unless new_user_ids.blank?

      # Remove deleted user ids from the set
      $redis.srem("core_party_user_ids", old_user_ids) unless old_user_ids.blank?

      flash[:notice] = "Updated core party users list!"
    rescue
      flash[:error] = "Failed to Update!"
    end
  end

  page_action :subscription_banner_to_be_enabled_trial_user_ids, method: :post do
    begin
      @subscription_banner_to_be_enabled_trial_user_ids = params[:subscription_banner_to_be_enabled_trial_user_ids]
      user_ids = @subscription_banner_to_be_enabled_trial_user_ids&.split(",").map(&:to_i)
      $redis.del(Constants.get_subscription_banner_to_be_enabled_trial_users_key)
      $redis.sadd(Constants.get_subscription_banner_to_be_enabled_trial_users_key, user_ids) if user_ids.length > 0

      flash[:notice] = "Updated subscription banner to be enabled users list!"
    rescue
      flash[:error] = "Failed to Update!"
    end

    redirect_to admin_scripts_path
  end

  # page_action :update_tg_election_data, method: :post do
  #   begin
  #     data_hash = {}
  #     win_data_hash = {}
  #     index = 0
  #     ElectionsUtil::TG_PARTIES.keys.each do |party|
  #       data_hash[party] = params["party_#{index}"].to_i
  #       win_data_hash[party] = params["party_win_#{index}"].to_i
  #       index += 1
  #     end
  #
  #     ElectionsUtil.process_tg_elections_data_hash(data_hash, win_data_hash, current_admin_user.id)
  #
  #     flash[:notice] = "Updated!"
  #   rescue => e
  #     Rails.logger.error(e)
  #     flash[:error] = "Failed to Update! Error: #{e}"
  #   end
  #   redirect_to admin_scripts_path
  # end

  content title: 'Scripts' do
    # columns do
    #   column do
    #     panel "TG elections data (lead/win count)" do
    #       form action: "scripts/update_tg_election_data", method: :post do |f|
    #         index = 0
    #         redis_data = $redis.get("tg_2023_election_data")
    #         data = nil
    #         if redis_data.present?
    #           full_data = JSON.parse(redis_data)
    #           data = full_data['party_data']
    #           win_data = full_data['party_win_data'] || {}
    #           timestamp = full_data['updated_at']
    #           label "Last updated at: #{Time.zone.at(timestamp).strftime('%d %b %Y %H:%M:%S')}"
    #         end
    #         table border: 0, margin: 0, padding: 0, align: 'center' do
    #           tr do
    #             td { label 'Party' }
    #             td { label 'Lead Count' }
    #             td { label 'Win Count' }
    #           end
    #           ElectionsUtil::TG_PARTIES.keys.each do |party|
    #             tr do
    #               td { label ElectionsUtil::TG_PARTIES[party]['display_name'] }
    #               td { input type: :number, min: 0, value: data.present? && data[party].present? ? data[party].to_i : 0, placeholder: "#{party} lead count", name: "party_#{index}" }
    #               td { input type: :number, min: 0, value: win_data.present? && win_data[party].present? ? win_data[party].to_i : 0, placeholder: "#{party} win count", name: "party_win_#{index}" }
    #             end
    #             index += 1
    #           end
    #         end
    #         input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
    #         br
    #         input :submit, type: :submit, value: 'Update'
    #       end
    #     end
    #   end
    #   column do
    #   end
    #   column do
    #   end
    # end
    columns do
      column do
        panel "My Feed enabled district ids (ONLY ADDING)" do
          form action: "scripts/my_feed_districts", method: :post do |f|
            label 'Existing District IDs'
            input value: $redis.smembers("my_feed_enabled_district_ids")&.join(','), disabled: true, class: 'spacer'
            br
            input placeholder: 'Add new District IDs', class: 'spacer width-100', name: 'district_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Enable!'
          end
        end
      end
      # column do
      #   panel "Generate Invite Cards" do
      #     form action: "scripts/generate_invite_card", method: :post do |f|
      #       label 'Phone numbers (comma seperated)'
      #       input :phone_numbers, as: :text, label: 'Phone Numbers (case insensitive, comma seperated)', name: 'phone_numbers',
      #             value: controller.instance_variable_get(:@phone_numbers)
      #       input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
      #       br
      #       br
      #       input :submit, type: :submit, value: 'Submit'
      #     end
      #   end
      # end

      column do
        panel "Core party user ids" do
          form action: "scripts/core_party_user_ids", method: :post do |f|
            textarea User.core_party_user_ids&.join(','), placeholder: "User ids (comma seperated user ids)", name: 'user_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Update'
          end
        end
      end

      column do
        panel "Refresh Hashtags (clear cache)" do
          form action: "scripts/refresh_hashtags", method: :post do |f|
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            input :submit, type: :submit, value: 'Refresh'
          end
        end
      end

      column do
        panel "Auto Create or Update Users" do
          form action: "scripts/auto_create_or_update_users", method: :post, enctype: "multipart/form-data" do |f|
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            label 'Import/Upload CSV: '
            input type: :file, name: :csv_data, accept: '.csv'
            br
            br
            f.input :submit, type: :submit, value: 'Submit'
          end
        end
      end
    end
    columns do
      # column do
      #   panel "Marketing sent phones" do
      #     form action: "scripts/marketing_sent", method: :post do |f|
      #       textarea placeholder: 'Phone Numbers (case insensitive, comma seperated)', name: 'phone_numbers'
      #       input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
      #       br
      #       input :submit, type: :submit, value: 'Mark as sent'
      #     end
      #   end
      # end

      column do
        panel "Show Checkout Link" do
          form action: "scripts/show_checkout_link", method: :post do |f|
            label 'User ID'
            input type: :text, name: :user_id, placeholder: 'Enter User ID'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Show Checkout Link'
          end
        end
      end

      column do
        panel "Redis Key" do
          form action: "scripts/redis_get_key", method: :post do |f|
            label "Mode"
            f.select name: "mode", value: controller.instance_variable_get(:@mode) do |s|
              s.option :GET, value: 1, selected: true
              s.option :TTL, value: 2
            end
            br
            br
            label "Key type"
            f.select name: "key_type", value: controller.instance_variable_get(:@key_type) do |s|
              s.option :string, value: 1, selected: true
              s.option :hash, value: 2
              s.option :set, value: 3
            end
            br
            br
            label "Key name"
            f.input :key_name, as: :text, label: 'Key name', name: 'key_name',
                    value: controller.instance_variable_get(:@key_name)
            br
            br
            label "Member or Field"
            f.input :field_name, as: :text, placeholder: 'For hash or set', label: 'Member / Field', name: 'set_key_name',
                    value: controller.instance_variable_get(:@set_key_name)
            f.input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            br
            f.input :submit, type: :submit, value: 'Submit'
          end
        end
      end
      column do
        panel "Test User ID's" do
          form action: "scripts/test_user_ids", method: :post do |f|
            textarea $redis.smembers("test_user_ids")&.join(','), placeholder: "User ids (comma seperated user ids)", name: 'test_user_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Update'
          end
        end
      end
      column do
        panel "Sync User to Mixpanel" do
          form action: admin_scripts_sync_to_mixpanel_path, method: :get do |f|
            f.textarea placeholder: "User ID's(Comma Separated)", name: 'sync_user_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Update'
          end
        end
      end
    end

    columns do
      # column do
      #   panel "Phone Numbers Data To Send Messages" do
      #     form action: "scripts/request_for_user_invite_phones", method: :post do |f|
      #       label 'Location Id'
      #       input :location_id, as: :text, label: 'Location_Id', name: 'location_id',
      #             value: controller.instance_variable_get(:@location_id)
      #       br
      #       br
      #       label 'count of phones required'
      #       input :location_id, as: :text, label: 'count_of_phones_required', name: 'count',
      #             value: controller.instance_variable_get(:@count)
      #       br
      #       br
      #       label 'Enter Email address'
      #       input :email_id, as: :text, label: 'Email address', name: 'email_id',
      #             value: controller.instance_variable_get(:@email_id)
      #       input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
      #       br
      #       br
      #       input :submit, type: :submit, value: 'Submit'
      #     end
      #   end
      # end
      column do
        panel "Blocked users for tagging" do
          form action: "scripts/block_for_tagging", method: :post do |f|
            textarea $redis.smembers("users_blocked_for_tagging")&.join(','), placeholder: "User ids (comma seperated user ids)", name: 'user_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Update'
          end
        end
      end
      column do
        panel "Blocked users for commenting" do
          form action: "scripts/block_for_commenting", method: :post do |f|
            textarea $redis.smembers("users_blocked_for_commenting")&.join(','), placeholder: "User ids (comma seperated user ids)", name: 'user_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Update'
          end
        end
      end
      column do
        panel "Subscription banner to be enabled trial user ids" do
          form action: "scripts/subscription_banner_to_be_enabled_trial_user_ids", method: :post do |f|
            textarea $redis.smembers(Constants.get_subscription_banner_to_be_enabled_trial_users_key)&.join(','),
                     placeholder: "User ids (comma seperated user ids)",
                     name: 'subscription_banner_to_be_enabled_trial_user_ids'
            input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
            br
            input :submit, type: :submit, value: 'Update'
          end
        end
      end
    end
    if current_admin_user.admin_role?
      columns do
        column do
          panel "Update or Swap phone numbers" do
            form action: "scripts/swap_or_update_phone", method: :post do |f|
              f.input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
              label "User Id: "
              f.input type: :text, name: :user_id, placeholder: 'Enter User ID'
              br
              label "Phone: "
              f.input type: :text, name: :phone, placeholder: "Enter phone to update"
              br
              br
              f.input :submit, type: :submit, value: 'Update'
            end
          end
        end
        column do
          panel "Premium Special Offer Event" do
            form action: "scripts/premium_special_offer_event", method: :post, enctype: "multipart/form-data" do |f|
              f.input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
              label "Select Plan: "
              f.select name: :select_plan, value: controller.instance_variable_get(:@select_plan) do |s|
                s.option " ", value: 1, selected: true
                s.option "Rs. 1", value: "rs_1"
                s.option "Rs. 29", value: "rs_29"
                s.option "Rs. 59", value: "rs_59"
                s.option "50% Offer", value: "50%_offer"
              end
              br
              br
              label "Upload CSV: "
              f.input type: :file, name: :csv_data, accept: '.csv'
              br
              a "View sample CSV for uploading users", href: "https://docs.google.com/spreadsheets/d/1RyEe1aZNnDqg4RXbHfTvbW5YcnFBOEVI4_8rXj1--a8/edit?usp=sharing"
              br
              br
              f.input :submit, type: :submit, value: 'Apply Special Offer'
            end
          end
        end
        column do
          panel "Add Leads to Floww" do
            form action: "scripts/add_leads_to_floww", method: :post, enctype: "multipart/form-data" do |f|
              f.input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
              label "Select Source: "
              f.select name: :select_source, value: controller.instance_variable_get(:@select_source) do |s|
                PremiumPitch.sources.each do |key, value|
                  next unless key.start_with?("MANUAL", "WATI", "APP")
                  s.option value, value: key
                end
              end
              br
              br
              label "Upload CSV: "
              f.input type: :file, name: :csv_data, accept: '.csv'
              br
              a "View sample CSV for uploading leads", href: "https://docs.google.com/spreadsheets/d/1MkNi5eH6puymUeWHiEXtZ4M268DJ5Ifpydm3VgtX6j0/edit?usp=sharing"
              br
              br
              f.input :submit, type: :submit, value: 'Add Leads'
            end
          end
        end
      end
      if ['admin', 'sales_am'].include?(current_admin_user.role)
        columns do
          column do
            panel "Trigger Premium Pitch Callbacks" do
              form action: "scripts/trigger_premium_pitch_callbacks", method: :post do |f|
                label 'User ID'
                input type: :text, name: :user_id, placeholder: 'Enter User ID'
                input :authenticity_token, type: :hidden, name: :authenticity_token, value: form_authenticity_token
                br
                br
                para "This will trigger the appropriate Floww worker based on the user's current premium pitch status:"
                ul do
                  li "badge_setup/badge_setup_no_layout_setup → Floww::BadgeSetup"
                  li "layout_setup → Floww::LayoutSetup + Floww::AssignOe"
                  li "pending_layout_approval → Floww::PendingLayoutApproval"
                  li "trail_enabled → Floww::LayoutApproved or Floww::TrialEnabled"
                  li "milestone_1 → Floww::TrialUsed"
                  li "milestone_2 → Floww::QualifiedPitch"
                  li "trial_low_usage → Floww::TrialLowUsage"
                  li "paid → Floww::Payment"
                  li "subscribed_no_layout → Floww::SubscribedNoLayout"
                  li "interested → Floww::AddLead"
                end
                br
                input :submit, type: :submit, value: 'Trigger Callbacks'
              end
            end
          end
          column do
          end
          column do
          end
        end
      end
    end
  end
end
