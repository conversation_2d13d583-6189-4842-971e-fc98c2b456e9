class RefundSubscriptionCharge
  include Sidekiq::Worker

  def perform(refund_id)
    refund = SubscriptionChargeRefund.find_by(id: refund_id)
    return unless refund.present? && refund.initiated?

    subscription_charge = refund.subscription_charge
    return unless subscription_charge.present?


    # Process the refund with the payment gateway
    process_refund(subscription_charge, refund)
  end

  private

  def process_refund(subscription_charge, refund)
    subscription = subscription_charge.subscription

    begin
      if subscription.cashfree?
        response = CashfreePaymentUtils.cashfree_post_v1(
          "/subscriptions/create-refund",
          {
            "merchantTxnId": "#{subscription_charge.pg_id}",
            "refundAmount": refund.amount,
            "refundNote": refund.reason || "Charge refund",
            "merchantRefundId": "refund-#{refund.id}-#{subscription_charge.pg_id}",
            "requestedSpeed": "STANDARD"
          }
        )

        # Store the response but don't update status - wait for webhook
        refund.update(pg_json: response) if response.present?
      elsif subscription.juspay?
        response = JuspayPaymentUtils.post(
          "/orders/#{subscription_charge.pg_id}/refunds",
          {
            unique_request_id: "R-#{refund.id}-#{subscription_charge.pg_id}",
            amount: refund.amount,
          }
        )

        # Store the response but don't update status - wait for webhook
        refund.update(pg_json: response) if response.present?
      elsif subscription.phonepe?
        # Convert amount to paise for PhonePe API
        amount_in_paise = (refund.amount * 100).to_i

        response = PhonepePaymentUtils.post(
          "/payments/v2/refund",
          {
            merchantRefundId: "refund-#{refund.id}-#{subscription_charge.pg_id}",
            originalMerchantOrderId: subscription_charge.pg_id,
            amount: amount_in_paise
          }
        )

        # Store the response but don't update status - wait for webhook
        refund.update(pg_json: response) if response.present?
      end

      # Log the event in mixpanel
      subscription_charge.log_payment_mixpanel_event(
        event_name: 'payment_refund_initiated_backend',
        extra_params: { refund_amount: refund.amount }
      ) unless subscription_charge.is_trial_charge?
    rescue => e
      Honeybadger.notify(e, context: {
        subscription_charge_id: subscription_charge.id,
        refund_id: refund.id,
        amount: refund.amount
      })
    end
  end
end
