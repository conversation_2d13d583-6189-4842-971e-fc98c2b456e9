# frozen_string_literal: true

class GeneratePosterCampaignImage
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  sidekiq_options queue: :campaign_poster_generation, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: 100,
    }
    # threshold: {
    #   limit: 30,
    #   period: 5.second,
    # }
  )

  def perform(user_id, creative_id, campaign_name, frame_id = nil, force_generate = false)
    Honeybadger.context(user_id: user_id, creative_id: creative_id, campaign_name: campaign_name, frame_id: frame_id)

    return if user_id.blank? || creative_id.blank? || campaign_name.blank?

    start_time = Time.zone.now

    user = User.find_by_id(user_id)
    if user.blank?
      Honeybadger.notify("User not found for user_id: #{user_id}")
      Rails.logger.warn("User not found for user_id: #{user_id}")
      return
    end

    unless user.has_premium_layout?
      Rails.logger.warn("User does not have premium layout for user_id: #{user_id}")
      return
    end

    poster_creative = PosterCreative.find_by(id: creative_id)
    if poster_creative.blank?
      Honeybadger.notify("PosterCreative not found for creative_id: #{creative_id}")
      Rails.logger.warn("PosterCreative not found for creative_id: #{creative_id}")
      return
    end

    metadata_key = Constants.poster_image_url(campaign_name)

    if !force_generate && UserMetadatum.exists?(user_id: user_id, key: metadata_key)
      Honeybadger.notify("Campaign #{campaign_name} already has a poster image for user #{user_id}")
      Rails.logger.warn("Campaign #{campaign_name} already has a poster image for user #{user_id}")
      return
    end

    bearer_token = JsonWebToken.get_token_for_media_to_ror_auth
    payload = {
      user_id: user_id,
      creative_id: creative_id,
      frame_id: frame_id,
      bearer_token: bearer_token,
    }

    begin
      payload_response =  invoke_lambda(payload)

      # Above returns a StringIO object, which is a file-like object that can be read from and written to.
      # we can convert the StringIO to a string using the .read method
      payload_parsed_response = JSON.parse(payload_response.payload.read)

      response_body = JSON.parse(payload_parsed_response['body'])
    rescue => e
      Honeybadger.notify("Error capturing poster for user as lambda failed #{user_id}: #{e.message}")
      Rails.logger.warn("Failed to capture poster for user as lambda failed: #{e.message}")
      return
    end

    poster_url = response_body['poster_url']
    unless poster_url.present?
      Honeybadger.notify("Missing Poster URL in lambda response for user #{user_id}: #{response_body}")
      Rails.logger.warn("Missing Poster URL in lambda response for user #{user_id}: #{response_body}")
      return
    end

    user_metadata = UserMetadatum.find_or_initialize_by(user_id: user_id, key: metadata_key)
    user_metadata.update(value: poster_url)

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Time taken for generate_poster_campaign: #{elapsed_time} seconds")
  end

  private
  def invoke_lambda(payload)
    Aws::Lambda::Client.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      )
    ).invoke(
      function_name: 'arn:aws:lambda:ap-south-1:666527360739:function:UserCampaignPosterGenerat-UserCampaignPosterGenera-Y1PUtWW8HxsH',
      invocation_type: 'RequestResponse',
      payload: payload.to_json
    )
  end
end
