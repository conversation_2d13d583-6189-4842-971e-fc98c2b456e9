# frozen_string_literal: true

class AppVersionSupport
  def initialize(app_version_string)
    Current.app_version = Gem::Version.new(app_version_string)
  end

  def self.should_check_for_api_key?
    Current.app_version >= Gem::Version.new('1.7.9')
  end

  def self.has_frame_poster?
    Current.app_version > Gem::Version.new('1.17.0')
  end

  def self.supports_downgrade_sheet?
    Current.app_version >= Gem::Version.new('2412.17.01')
  end

  # making trending feed default feed for political parties for old app versions
  def self.is_trending_feed_default?
    Current.app_version < Gem::Version.new('2304.01.0')
  end

  def self.is_jwt_implemented?
    Current.app_version > Gem::Version.new('2304.01.0')
  end

  def self.contacts_screen_enabled?
    Current.app_version > Gem::Version.new('2304.01.0')
  end

  def self.dm_enabled?
    Current.app_version >= Constants.get_dm_enabled_app_version
  end

  def self.new_contacts_screen_enabled?
    Current.app_version > Gem::Version.new('2305.04.0')
  end

  def self.is_new_response_in_hashtag_feed_enabled?
    Current.app_version > Gem::Version.new('2305.04.0')
  end

  def self.is_new_suggested_users_list_enabled?
    Current.app_version > Gem::Version.new('2305.04.0')
  end

  def self.should_force_update?
    Current.app_version < Gem::Version.new('2409.01.00') || Current.app_version == Gem::Version.new('2504.02.03')
  end

  def self.dm_private_groups_enabled?
    Current.app_version > Constants.get_dm_private_groups_enabled
  end

  def self.dm_channels_enabled?
    Current.app_version > Constants.get_dm_channels_enabled_version
  end

  def self.basic_poster_to_premium_json
    Current.app_version > Gem::Version.new('2310.24.0')
  end

  def self.is_feed_toast_supported?
    Current.app_version > Gem::Version.new('2311.13.0')
  end

  def self.is_full_badge_card_supported?
    Current.app_version > Gem::Version.new('2302.15.00')
  end

  def self.can_show_pitch_premium_layout?
    Current.app_version > Gem::Version.new('2403.10.00')
  end

  def self.know_your_contestant_carousel_supported?
    Current.app_version > Gem::Version.new('2403.14.00')
  end

  def self.can_show_fan_poster_request_layout?
    Current.app_version > Gem::Version.new('2403.29.00')
  end

  def self.eligible_for_suggested_users?
    Current.app_version > Gem::Version.new('1.16.1')
  end

  def self.creative_carousel_in_circle_feed_supported?
    Current.app_version > Gem::Version.new('2402.15.00')
  end

  def self.poster_carousel_in_feed_supported?
    Current.app_version > Gem::Version.new('2404.15.00')
  end

  def self.live_toast_available?
    Current.app_version >= Gem::Version.new('2404.30.04')
  end

  def self.live_toast_properly_supported?
    Current.app_version >= Gem::Version.new('2404.30.07')
  end

  def self.external_deeplink_supported?
    Current.app_version >= Gem::Version.new('2401.05.19')
  end

  def self.supports_profession_selection?
    Current.app_version >= Gem::Version.new('2407.01.00')
  end

  def self.supports_autopay?
    Current.app_version >= Gem::Version.new('2408.05.00')
  end

  def self.posters_feed_enabled?
    Current.app_version >= Gem::Version.new('2409.23.00')
  end

  def self.juspay_sdk_enabled?
    Current.app_version >= Gem::Version.new('2409.25.07')
  end

  def self.show_badge_banner_support_in_basic_frames?
    Current.app_version >= Gem::Version.new('2409.23.01')
  end

  def self.self_trial_supported?
    Current.app_version >= Gem::Version.new('2409.30.00')
  end

  def self.is_annual_recharge_paywall_supported?
    Current.app_version >= Gem::Version.new('2412.17.00')
  end

  def self.transparent_background_type_supported?
    Current.app_version >= Gem::Version.new('2406.11.00')
  end

  def self.supports_upgrade_package_sheet?
    # Don't change patch version number because upgrade package sheet is fixed in the app from this patch version
    Current.app_version >= Gem::Version.new('2412.17.06')
  end

  def self.supports_upgrade_feed_item_in_posters_feed?
    Current.app_version >= Gem::Version.new('2501.09.00')
  end

  def self.is_app_open_analytics_params_supported?
    Current.app_version >= Gem::Version.new('2501.09.00')
  end

  def self.supports_unverified_badge_with_verification_text?
    Current.app_version >= Gem::Version.new('2502.08.00')
  end

  #@note: Need to update the version number after the release
  def self.layout_feedback_prompt_v2_enabled?
    Current.app_version >= Gem::Version.new('2504.02.00')
  end

  def self.cancellation_flow_v2_supported?
    Current.app_version >= Gem::Version.new('2505.01.00')
  end

end
