class PosterFeed
  include SearchkickAdapter

  attr_accessor :id, :created_at, :start_date, :end_date, :views_count, :shares_count, :priority,
                :primary_creative_id, :creative_ids, :circle_ids, :active

  def initialize(attributes = {})
    @id = attributes[:id]
    @created_at = attributes[:created_at]
    @start_date = attributes[:start_date]
    @end_date = attributes[:end_date]
    @views_count = attributes[:views_count]
    @shares_count = attributes[:shares_count]
    @priority = attributes[:priority]
    @primary_creative_id = attributes[:primary_creative_id]
    @creative_ids = attributes[:creative_ids]
    @circle_ids = attributes[:circle_ids]
    @active = attributes[:active]
  end

  searchkick index_name: -> { index_name }, mappings: {
    properties: {
      id: { type: 'keyword' },
      created_at: { type: 'date' },
      start_date: { type: 'date' },
      end_date: { type: 'date' },
      views_count: { type: 'rank_feature' },
      shares_count: { type: 'rank_feature' },
      priority: { type: 'rank_feature' },
      primary_creative_id: { type: 'keyword' },
      creative_ids: { type: 'keyword' },
      circle_ids: { type: 'keyword' },
      active: { type: 'boolean' }
    }
  }

  def search_data
    {
      id: id,
      created_at: created_at,
      start_date: start_date,
      end_date: end_date,
      views_count: views_count,
      shares_count: shares_count,
      priority: priority,
      primary_creative_id: primary_creative_id,
      creative_ids: creative_ids,
      circle_ids: circle_ids,
      active: active
    }
  end

  def self.index_name
    EsUtil.get_index_name(name.tableize)
  end

  def reindex
    # This method will index the current object's data into Elasticsearch
    Searchkick.client.index(
      index: self.class.index_name, # Index name derived from the class method
      id: id, # Document ID
      body: search_data # Data to be indexed
    )
  rescue => e
    puts "Failed to index document: #{e.message}"
  end

  def self.poster_feed_query(user:, per_page:, circle_id: nil, loaded_feed_item_ids: [], specific_doc_id: nil)
    user_joined_circle_ids = user.get_user_joined_circle_ids_for_posters_feed_for_you(add_limit: true, add_public_and_state_circle_id: true)
    poster_affiliated_party_id = user.get_poster_affiliated_party_id&.to_i
    user_role = user.get_badge_role_including_unverified
    if poster_affiliated_party_id.present?
      if user_joined_circle_ids.include?(poster_affiliated_party_id)
        relevant_leader_circle_ids = CirclesRelation.party_affiliated_circle_ids(party_id: poster_affiliated_party_id, circle_ids: user_joined_circle_ids)
        user_joined_circle_ids = [poster_affiliated_party_id, user.state_id, Constants.public_circle_id]
        user_joined_circle_ids += relevant_leader_circle_ids
      else
        user_joined_circle_ids = [user.state_id, Constants.public_circle_id]
      end
    elsif user.has_premium_layout? && !user_role&.get_badge_user_affiliated_party_circle_id&.positive?
      user_joined_circle_ids = user.get_user_joined_party_and_leader_ids(add_limit: true) + [user.state_id, Constants.public_circle_id]
    else
      if user_role.present?
        if user.affiliated_party_circle_id.present? && user_joined_circle_ids.include?(user.affiliated_party_circle_id)
          relevant_leader_circle_ids = CirclesRelation.party_affiliated_circle_ids(party_id: user.affiliated_party_circle_id, circle_ids: user_joined_circle_ids)
          user_joined_circle_ids = [user.affiliated_party_circle_id]
          user_joined_circle_ids += relevant_leader_circle_ids
        else
          user_joined_circle_ids = []
        end
      else
        user_joined_circle_ids = user.get_user_joined_party_and_leader_ids(add_limit: true)
      end
      user_joined_circle_ids += [user.state_id, Constants.public_circle_id]
    end

    viewed_creative_ids = user.viewed_pc_event_ids

    # if circle_id is present, add filter in query
    if circle_id.present?
      filter_data = [{
                       term: {
                         circle_ids: circle_id
                       }
                     },
                     {
                       term: {
                         active: true
                       }
                     }]
    else
      filter_data = [
        {
          term: {
            active: true
          }
        },
        {
          terms: {
            circle_ids: user_joined_circle_ids
          }
        }
      ]
    end

    poster_feed = Searchkick.search "*", index_name: index_name, per_page: per_page, load: false do |body|
      body[:query] = {
        function_score: {
          query: {
            bool: {
              must_not: [
                {
                  terms: {
                    id: loaded_feed_item_ids
                  }
                }
              ],
              must: [
                {
                  range: {
                    start_date: {
                      lte: Time.zone.now.iso8601
                    }
                  }
                },
                {
                  range: {
                    end_date: {
                      gte: Time.zone.now.iso8601
                    }
                  }
                },
                {
                  exists: {
                    field: "creative_ids"
                  }
                }
              ],
              filter: filter_data,
              should: specific_doc_id ? [
                {
                  term: {
                    _id: specific_doc_id
                  }
                },
                {
                  constant_score: {
                    filter: {
                      term: {
                        _id: specific_doc_id
                      }
                    },
                    boost: 99999
                  }
                }
              ] : []
            }
          },
          functions: [
            {
              script_score: {
                script: {
                  source: "" "
                double views = doc['views_count'].value;
                double shares = doc['shares_count'].value;
                double share_view_factor;
                if (views < 30) {
                  share_view_factor = 1;
                } else {
                  double conversion_rate = shares / views;
                  share_view_factor = 1 + conversion_rate;
                }

                double seen_factor = 1.5;
                List creativeIds = doc['creative_ids'];
                for (int i = 0; i < creativeIds.size(); i++) {
                  if (params.seen_creative_ids.contains(creativeIds.get(i))) {
                    seen_factor = 1;
                    break;
                  }
                }

                double priority_factor = 0; // Default value for priority_factor
                if (doc.containsKey('priority.keyword') && doc['priority.keyword'].size() > 0) {
                  String priority = doc['priority.keyword'].value;
                  if (priority == 'high') {
                    priority_factor = 1;
                  } else if (priority == 'medium') {
                    priority_factor = 0.5;
                  } else if (priority == 'low') {
                    priority_factor = 0.25;
                  }
                }

                double C = 0;
                List circleIds = doc['circle_ids'];
                for (circleId in params._source.circle_ids) {
                  if (params.joined_leaders_ids.contains(circleId)) {
                    C = 1;
                    break;
                  } else if (params.joined_political_parties_ids.contains(circleId) && C < 0.5) {
                    C = 0.5;
                  } else if (params.location_ids.contains(circleId) && C < 0.25) {
                    C = 0.25;
                  }
                }
                double circle_factor = 1 + C;

                double currentTime = new Date().getTime();
                // Calculate the time difference in hours
                ZonedDateTime startDate = ZonedDateTime.parse(doc['start_date'].value.toString());
                long startDateMillis = startDate.toInstant().toEpochMilli();
                long now = new Date().getTime();
                double time_diff_hours = (now - startDateMillis) / 3600000.0;
                double time_factor = 1 + Math.exp(-time_diff_hours / 72.0);
                priority_factor = 1 + priority_factor;

                return share_view_factor * seen_factor * priority_factor * circle_factor * time_factor;
              " "",
                  params: {
                    seen_creative_ids: viewed_creative_ids.present? ? viewed_creative_ids : [],
                    joined_political_parties_ids: user.get_user_joined_party_circle_ids,
                    joined_leaders_ids: user.get_user_joined_leader_ids(add_limit: true),
                    location_ids: [0, user.village_id]
                  }
                }
              }
            }
          ],
          score_mode: 'multiply',
          boost_mode: 'multiply'
        }
      }

      body[:_source] = %w[id creative_ids primary_creative_id]
      body[:size] = per_page
      body[:timeout] = '11s'
    end

    poster_feed_data = {}
    poster_feed.each do |p|
      # store the data with id as key and values are creative_ids and primary_creative_id
      poster_feed_data[p.id] = { creative_ids: p.creative_ids, primary_creative_id: p.primary_creative_id }
    end
    poster_feed_data
  end

  def self.poster_feed_filters(user_circle_ids:)
    return [] if user_circle_ids.blank? # Ensure `user_circle_ids` is not empty

    poster_feed_filters = Searchkick.search "*", index_name: index_name, load: false do |body|
      body[:query] = {
        bool: {
          must: [
            {
              range: {
                start_date: {
                  lte: Time.zone.now.iso8601 # `start_date` is in the past or now
                }
              }
            },
            {
              range: {
                end_date: {
                  gte: Time.zone.now.iso8601 # `end_date` is in the future or now
                }
              }
            }
          ],
          filter: [
            {
              term: {
                active: true # Filter for active documents
              }
            },
            {
              terms: {
                circle_ids: user_circle_ids # Filter based on `user_circle_ids`
              }
            }
          ]
        }
      }

      body[:_source] = %w[circle_ids]
      body[:size] = 1000
      body[:timeout] = '11s'
    end
    retrieved_circle_ids = []
    poster_feed_filters.each do |p|
      # store all the uniq circle_ids in an array
      retrieved_circle_ids << p.circle_ids
    end
    retrieved_circle_ids = retrieved_circle_ids.flatten.uniq
    user_circle_ids & retrieved_circle_ids
  end
end
