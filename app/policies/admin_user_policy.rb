class AdminUserPolicy < ApplicationPolicy
  attr_reader :admin_user

  def initialize(user, admin_user)
    @user = user
    @admin_user = admin_user
  end

  # Only users with the admin role can create or edit admin_users
  def can_manage_admin_users?
    @admin_user.admin_role?
  end

  # Only users with the specified roles can manage circles
  def can_manage_circles?
    @admin_user.admin_role? || @admin_user.op_executive_role? || @admin_user.support_specialist_role?
  end

  # Only users with the specified roles can manage roles
  def can_manage_roles?
    @admin_user.admin_role? || @admin_user.op_executive_role?
  end

  # Only users with the specified roles can manage user_roles
  def can_manage_user_roles?
    @admin_user.admin_role? || @admin_user.op_executive_role? || @admin_user.support_specialist_role?
  end

  def can_access_jathara?
    @admin_user.relationship_manager_role? || @admin_user.admin_role? || @admin_user.senior_relationship_manager_role? || @admin_user.sales_am_role?
  end

  class Scope < Scope
    # Be explicit about which records you allow access to!
    # def resolve
    #   scope.all
    # end
  end
end
